<script setup lang="ts">
// Imports
import { ref, computed, watch, onMounted } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisAccordion from '@/shared/components/general-controls/IrisAccordion.vue'
import IrisAccordionSection from '@/shared/components/general-controls/IrisAccordionSection.vue'
import DataAccess from '@/shared/services/data-access'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'

// Interfaces
interface Folder {
  Id: string;
  Name: string;
  TotalFiles: number;
  TotalSize: number;
  Children: Folder[];
}

interface File {
  Id: string;
  Name: string;
  Size: string; // Assuming this is string from formatBytes, but API might provide number
  Extension?: string;
  Selected: boolean;
}

/* --- Props --- */
const props = defineProps({
  modelValue: {
    type: Array as () => string[],
    required: true,
  },
  supportedExtensions: {
    type: Array as () => string[],
    required: false,
    default: function() { return []; } // Default for optional array prop
  }
});

/* --- Emits --- */
const emit = defineEmits(['update:modelValue']);

/* --- Constants and Refs --- */
const _currentFolder = ref<Folder | null>(null);
const _files = ref<File[]>([]); // Typed more strictly
const _folders = ref<Folder[]>([]);
const _mode = ref('view'); // 'view' or 'edit'
const _searchTerm = ref('');
const isLoading = ref(true);
const allSelected = ref(false); // Represents the state of the "select all" checkbox in edit mode
const dataAccess = new DataAccess();
const currentSortOption = ref('Latest');
const showFilterDropdown = ref(false);
const showSortDropdown = ref(false);
const selectedExtensions = ref<string[]>([]); // Extensions selected in the filter UI
const activeExtensions = ref<string[]>([]); // Extensions actively applied to filter
const accordionItems = ref([{ id: 'extensions', title: 'Extensions' }]);

// Hooks
onMounted(async function() {
});

// Computed
const isEditMode = computed(function() {
  return _mode.value === 'edit';
});

const allChecked = computed(function() { // Renamed to reflect it checks if all *displayed* files are selected
  if (!_files.value || _files.value.length === 0) {
    return false;
  }
  return _files.value.every(function(f) { return f.Selected; });
});

const filteredFolders = computed(() => {
  const foldersToFilter = _currentFolder.value ? _currentFolder.value.Children : _folders.value;

  return foldersToFilter.filter((f) => {
    // Filter out 0 file folders if there are active filters
    if (activeExtensions.value.length > 0 && f.TotalFiles === 0)
      return false;
    
    // Apply search filter if search term exists
    if (_searchTerm.value)
      return f.Name.toLowerCase().includes(_searchTerm.value.toLowerCase());
    
    return true;
  });
});

const filteredFiles = computed(function() {
  return _files.value.filter(function(f) {
    const matchesSearch = !_searchTerm.value || f.Name.toLowerCase().includes(_searchTerm.value.toLowerCase());
    const matchesExtension = activeExtensions.value.length === 0 || (f.Extension && activeExtensions.value.includes(f.Extension.toLowerCase()))
    
    return matchesSearch && matchesExtension;
  });
});

// Watches
watch(function() { return isEditMode.value; }, async function(newMode, oldMode) {
  // Refetch data when mode changes, as available items might differ
  await fetchFolders();

  if (_currentFolder.value)
    await fetchFiles(_currentFolder.value.Id);
  else
    _files.value = []; // Clear files if no folder is selected (root view)
});

// Functions
function getIcon(extension: string | undefined): string {
  if (!extension || extension.trim() === '')
    return 'file';

  extension = extension.replace('.', '').toLowerCase();

  switch (extension) {
    case 'pdf': 
      return 'file-pdf';
    case 'mp4': 
      return 'file-video';
    case 'mp3': 
      return 'file-audio';
    case 'txt': 
      return 'file-lines';
    case 'png':
    case 'jpg':
    case 'jpeg':
      return 'file-image';
    case 'rar':
    case 'zip':
      return 'file-zipper';
    case 'doc':
    case 'docx':
      return 'file-doc';
    case 'xls':
    case 'xlsx': 
      return 'file-spreadsheet';
    case 'ppt':
    case 'pptx': 
      return 'file-ppt';
    case 'js':
    case 'ts':
    case 'jsx':
    case 'tsx':
    case 'html':
    case 'css':
    case 'json':
    case 'xml':
    case 'py':
    case 'java':
    case 'c':
    case 'cpp':
    case 'cs':
    case 'php':
    case 'rb':
    case 'go':
    case 'swift':
    case 'kt':
    case 'vue':
    case 'svelte':
    case 'scss':
    case 'less':
    case 'md':
    case 'yml':
    case 'yaml': 
      return 'file-code';
    case 'folder': 
      return 'folder';
    default: return 'file';
  }
}

function formatBytes(bytesStr: string | number): string {
  const bytes = typeof bytesStr === 'string' ? parseFloat(bytesStr) : bytesStr;

  if (!bytes || isNaN(bytes) || bytes === 0)
    return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function handleSearchTermChange(value: string): void {
  _searchTerm.value = value;
}

function allFilesClicked(value: boolean): void {
  allSelected.value = value; // Update the "select all" checkbox state
  let updatedModelValue = [...props.modelValue];

  filteredFiles.value.forEach(function(file) { // Iterate over currently filtered files
    file.Selected = value; // Visually update the file's selected state
    if (value) { // If selecting all
      if (!updatedModelValue.includes(file.Id)) {
        updatedModelValue.push(file.Id);
      }
    } else { // If deselecting all
      updatedModelValue = updatedModelValue.filter(function(id) {
        return id !== file.Id;
      });
    }
  });
  emit('update:modelValue', updatedModelValue);
}

function handleRemoveFile(fileId: string): void {
  const updatedSelection = props.modelValue.filter(function(id) {
    return id !== fileId;
  });
  emit('update:modelValue', updatedSelection);
  // Also update the local _files state if the file is present
  const fileInList = _files.value.find(function(f) {return f.Id === fileId; });
  if (fileInList) {
    fileInList.Selected = false;
  }
}

function onFileCheckboxChange(file: File, event: Event): void {
  const target = event.target as HTMLInputElement;
  const checked = target.checked;
  file.Selected = checked; // Visually update the file's selected state

  let updatedModelValue = [...props.modelValue];
  if (checked) {
    if (!updatedModelValue.includes(file.Id)) {
      updatedModelValue.push(file.Id);
    }
  } else {
    updatedModelValue = updatedModelValue.filter(function(id) {
      return id !== file.Id;
    });
  }
  emit('update:modelValue', updatedModelValue);

  allSelected.value = filteredFiles.value.every(function(f) { 
    return f.Selected; 
  });
}

async function handleFolderClick(folder: Folder): Promise<void> {
  _currentFolder.value = folder;
  await fetchFiles(folder.Id);
}

async function navigateToParent(): Promise<void> {
  if (_currentFolder.value) {
    // This recursive findParentFolder is okay for moderate depths.
    // For very deep structures, an iterative approach or a flattened map might be more performant.
    const findParent = function(targetId: string, folders: Folder[], parent: Folder | null): Folder | null {
        for (const current of folders) {
            if (current.Id === targetId) {
                return parent;
            }
            const foundInChild = findParent(targetId, current.Children, current);
            if (foundInChild !== null) { // Check if the target was found among children's children, meaning 'current' is an ancestor
                 // If foundInChild is an actual folder, it means it's the direct parent.
                 // If foundInChild is the parent we are looking for.
                 if(current.Children.some(function(child) { return child.Id === targetId;} )) return current;
                 return foundInChild; // propagate up
            }
        }
        return null; // Target not found in this branch
    };

    // To find the parent of _currentFolder.value.Id, we need to search from root
    // A simpler way might be to maintain a breadcrumb/path if performance is an issue.
    // For now, let's reconstruct the parent by searching from the root `_folders.value`.
    // This is tricky because the `findParentFolder` needs to return the parent of the matched child.
    // Let's adjust findParent to search for `_currentFolder.value.Id` among children
    let parentFolder: Folder | null = null;
    function findParentIteratively(nodes: Folder[], targetId: string): Folder | null {
        const stack: {folder: Folder, parent: Folder | null}[] = nodes.map(function(f) {return {folder: f, parent: null};});
        while(stack.length > 0){
            const item = stack.pop();
            if(!item) continue;

            const current = item.folder;
            // Check if any child of 'current' is the targetId
            if(current.Children && current.Children.some(function(child) {return child.Id === targetId;})){
                return current; // 'current' is the parent
            }
            // If not, add children to stack
            if(current.Children){
                current.Children.forEach(function(child){
                    stack.push({folder: child, parent: current});
                });
            }
        }
        return null;
    }
    parentFolder = findParentIteratively(_folders.value, _currentFolder.value.Id);


    if (parentFolder) {
      _currentFolder.value = parentFolder;
      await fetchFiles(parentFolder.Id);
    } else {
      // If no parent is found in _folders, it means we are navigating up from a top-level folder
      _currentFolder.value = null;
      _files.value = []; // Clear files, show root folders
      await fetchFolders(); // Refetch root folders if necessary, or assume _folders is already root
    }
  }
}


function toggleMode(): void {
  _mode.value = isEditMode.value ? 'view' : 'edit';
}

function toggleFilter(): void {
  showFilterDropdown.value = !showFilterDropdown.value;
  showSortDropdown.value = false
}

async function selectSortOption(option: string): Promise<void> {
  currentSortOption.value = option;
  showFilterDropdown.value = false;
  showSortDropdown.value = false;
  // const dropdownElem = document.getElementById('sortDropdown'); // Direct DOM manipulation is discouraged
  // if (dropdownElem) { dropdownElem.classList.add('hidden'); } // Flowbite might handle this

  // Refetch data with new sort option
  await updateFoldersAndCurrentFolder();
}

function handleExtensionClicked(ext: string): void {
  const index = selectedExtensions.value.indexOf(ext);
  if (index >= 0) {
    selectedExtensions.value.splice(index, 1);
  } else {
    selectedExtensions.value.push(ext);
  }
}

async function updateFoldersAndCurrentFolder(): Promise<void> {
  await fetchFolders();
  
  if (_currentFolder.value) {
    const currentFolderId = _currentFolder.value.Id;
    
    // Helper function to find folder by ID in the folder tree
    const findFolderById = (folders: Folder[], id: string): Folder | null => {
      for (const folder of folders) {
        if (folder.Id === id)
          return folder;
        
        if (folder.Children && folder.Children.length > 0) {
          const found = findFolderById(folder.Children, id);

          if (found) 
            return found;
        }
      }

      return null;
    };

    // Find and update the current folder in the new structure
    const updatedFolder = findFolderById(_folders.value, currentFolderId);
    _currentFolder.value = updatedFolder;

    // If folder still exists, fetch its files
    if (updatedFolder)
      await fetchFiles(updatedFolder.Id);
    else {
      _currentFolder.value = null;
      _files.value = [];
    }
  }
}

async function applyFilters(): Promise<void> {
  activeExtensions.value = [...selectedExtensions.value];
  showFilterDropdown.value = false;

  await updateFoldersAndCurrentFolder();
}

async function clearSelectedFilters(): Promise<void> {
  selectedExtensions.value = [];
  activeExtensions.value = [];
  showFilterDropdown.value = false;

  await updateFoldersAndCurrentFolder();
}

async function fetchFolders(): Promise<void> 
{
    if (!isEditMode.value && !props.modelValue.length) {
        _folders.value = [];
        isLoading.value = false;

        return;
    }

    isLoading.value = true;
    
    try 
    {
        const params: any = {
            SupportedExtensions: activeExtensions.value.length > 0 
                ? activeExtensions.value 
                : (props.supportedExtensions && props.supportedExtensions.length > 0 
                    ? props.supportedExtensions 
                    : null),
        };
        
        if (!isEditMode.value)
            params.DocumentIds = props.modelValue;

        let sortExpression = 'ModifiedOn';
        let sortDirection = 1; // 1 for DESC, 0 for ASC

        switch (currentSortOption.value) 
        {
            case 'Latest': sortExpression = 'ModifiedOn'; sortDirection = 1; break;
            case 'Oldest': sortExpression = 'ModifiedOn'; sortDirection = 0; break;
            case 'DESC Name': sortExpression = 'Name'; sortDirection = 1; break;
            case 'ASC Name': sortExpression = 'Name'; sortDirection = 0; break;
        }

        _folders.value = await dataAccess.execute(
            '/sys/folder/getallfolders',
            { setting: params, sortExpression: sortExpression, sortDirection: sortDirection },
            RequestMethod.post
        );
    } 
    finally {
        isLoading.value = false;
    }
}

async function fetchFiles(folderId: string): Promise<void> 
{
    if (!isEditMode.value && !props.modelValue.length) {
        _files.value = [];
        return;
    }

    isLoading.value = true;

    try 
    {
        const params: any = {
            SupportedExtensions: props.supportedExtensions && props.supportedExtensions.length > 0 
                ? props.supportedExtensions 
                : null,
        };
        
        if (!isEditMode.value)
            params.DocumentIds = props.modelValue;

        let sortExpression = 'ModifiedOn';
        let sortDirection = 1; // 1 for DESC, 0 for ASC

        switch (currentSortOption.value) 
        {
            case 'Latest': sortExpression = 'ModifiedOn'; sortDirection = 1; break;
            case 'Oldest': sortExpression = 'ModifiedOn'; sortDirection = 0; break;
            case 'DESC Name': sortExpression = 'Name'; sortDirection = 1; break;
            case 'ASC Name': sortExpression = 'Name'; sortDirection = 0; break;
        }

        const payload = { 
            folderId: folderId, 
            setting: params, 
            sortExpression: sortExpression, 
            sortDirection: sortDirection 
        };    
        const filesFromApi = await dataAccess.execute(
            '/sys/folder/getdocumentsbyfolderid',
            payload,
            RequestMethod.post
        );

        _files.value = filesFromApi.map(function(file: any): File 
        {
            return {
                Id: file.Id,
                Name: file.Name,
                Size: file.Size,
                Extension: file.Extension,
                Selected: props.modelValue.includes(file.Id)
            };
        });

        allSelected.value = allChecked.value;
    } 
    finally 
    {
        isLoading.value = false;
    }
}

function handleClickOutside() {
  if (showFilterDropdown.value)
    showFilterDropdown.value = false;
  
  if (showSortDropdown.value)
    showSortDropdown.value = false;
}

async function openWithMode(mode: string) {
    _mode.value = mode
    _currentFolder.value = null
    _files.value = []
    _searchTerm.value = ''
    activeExtensions.value = [] 
    selectedExtensions.value = []
    showFilterDropdown.value = false
    showSortDropdown.value = false

    await fetchFolders()
  }

defineExpose({
  openWithMode
});
</script>

<template>
  <!-- Header Section -->
  <div class="flex items-center justify-between gap-2 p-4" @click="handleClickOutside">
    <div class="flex items-center gap-2">
      <button
        v-if="_currentFolder"
        @click="navigateToParent"
        class="text-text-color dark:text-text-color-dark"
      >
        <IrisIcon name="arrow-left" class="w-5 h-5" />
      </button>
      <h2 class="text-lg font-semibold">
        {{ _currentFolder ? _currentFolder.Name : 'Documents' }}
      </h2>
    </div>
  </div>
  <!-- Search Input -->
  <div class="flex items-center justify-between mb-6 px-4 gap-2 leading-5">
    <IrisTextbox
        id="search_term"
        :value="_searchTerm"
        @onChange="value => _searchTerm = value"
        placeHolder="Search"
        size="small"
        addOnIcon="magnifying-glass"
        class="flex-1 min-w-0"/>
    <!-- Show filter only if current folder exists -->
    <div v-if="isEditMode && _currentFolder" class="relative">
      <button @click="toggleFilter" class="px-2 py-2 border border-border-color dark:border-border-color-dark rounded inline-flex items-center relative">
        <IrisIcon name="filter" class="w-4 h-4 mr-1" />
        <span>Filter</span>
        <template v-if="activeExtensions.length">
          <span class="bg-bg-color-200 dark:bg-bg-color-200-dark rounded-full text-xs w-5 h-5 flex items-center justify-center ml-1">
            {{ activeExtensions.length }}
          </span>
        </template>
      </button>
      <!-- Custom Accordion for Filter Dropdown -->
      <div v-show="showFilterDropdown" class="absolute z-10 right-0 mt-2 bg-bg-color dark:bg-bg-color-dark rounded">
        <div class="py-4 border border-border-color dark:border-border-color-dark rounded shadow w-72 max-w-72">
          <div class="flex items-center justify-between mb-2 px-4">
            <h3 class="font-semibold text-text-color-400 dark:text-text-color-400-dark">FILTERS</h3>
            <button @click="toggleFilter">
              <IrisIcon name="xmark" class="w-4 h-4" />
            </button>
          </div>
          <div class="max-h-96 min-h-32 overflow-y-auto">
            <IrisAccordion id="filterAccordion" default-open-id="extensions">
              <IrisAccordionSection
                id="extensions"
                title="Extensions">  
                <div class="space-y-2 text-sm text-text-color-300 dark:text-text-color-300-dark">
                  <div v-for="ext in [...props.supportedExtensions].sort()" :key="ext" class="flex items-center">
                      <input 
                        type="checkbox" 
                        :checked="selectedExtensions.includes(ext)"
                        @click="handleExtensionClicked(ext)" 
                        class="iris-checkbox mr-2" />
                      <span>{{ 
                              ext === 'utbe' ? 'YouTube' : 
                              ext === 'vimo' ? 'Vimeo' : 
                              ext 
                            }}
                      </span>
                  </div>
                </div>
              </IrisAccordionSection>
            </IrisAccordion>
          </div>
          <div class="flex justify-between gap-2 mt-4 px-4">
            <button @click="applyFilters()" class="flex-1 btn-primary px-3">Apply filter</button>
            <button @click="clearSelectedFilters()" class="flex-1 btn-light ml-2 px-3">Clear all</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Sorting Dropdown -->
    <div v-if="isEditMode" class="relative">
      <button 
        @click="() => { showFilterDropdown = false; showSortDropdown = !showSortDropdown; }" 
        class="px-2 py-2 border border-border-color dark:border-border-color-dark rounded inline-flex items-center">
        <IrisIcon name="arrow-up-arrow-down" class="w-4 h-4 mr-1" />
        <span>Sort By: {{ currentSortOption }}</span>
      </button>
      <div v-show="showSortDropdown" class="absolute z-10 right-0 mt-2 bg-bg-color dark:bg-bg-color-dark divide-y rounded border border-border-color dark:border-border-color-dark shadow w-44">
        <ul class="py-1 text-sm">
          <li>
              <button @click="selectSortOption('Latest')" class="block w-full text-left px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">Latest</button>
          </li>
          <li>
              <button @click="selectSortOption('Oldest')" class="block w-full text-left px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">Oldest</button>
          </li>
          <li>
            <button @click="selectSortOption('ASC Name')" class="block w-full text-left px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">Name Asc</button>
          </li>
          <li>
            <button @click="selectSortOption('DESC Name')" class="block w-full text-left px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">Name Desc</button>
          </li>
        </ul>
      </div>
    </div>
    <button v-if="!isEditMode" class="btn-primary" @click="toggleMode">
      Add
    </button>
  </div>
  <!-- Table Section -->
  <div class="flex-1 overflow-y-auto px-4" @click="handleClickOutside">
    <!-- Loading Section -->
    <div v-if="isLoading" class="flex justify-center items-center p-4">
      <IrisIcon name="spinner-third" class="w-10 h-10 animate-spin fill-primary"/>
      <span class="ml-3">Loading...</span>
    </div>
    <div v-else class="bg-bg-color dark:bg-bg-color-dark shadow rounded border border-border-color dark:border-border-color-dark overflow-hidden">
      <table class="min-w-full table-auto text-sm text-left">
        <thead class="bg-bg-color-200 dark:bg-bg-color-200-dark uppercase font-semibold">
          <tr>
            <th class="px-3 py-4">
              <div class="flex items-center gap-2">
                <span v-if="isEditMode && _currentFolder">
                  <input
                    type="checkbox"
                    :checked="allSelected"
                    @click="($event) => allFilesClicked(($event.target as HTMLInputElement)?.checked ?? false)"
                    id="select-all-files"
                    class="iris-checkbox"
                  />
                </span>
                FILE NAME
              </div>
            </th>
            <th class="px-2 py-4 min-w-[90px]">FILE DATA</th>
            <th v-if="!isEditMode" class="px-2 py-2"></th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(folder, index) in filteredFolders"
            :key="`folder-${index}`"
            class="border-t border-border-color dark:border-border-color-dark hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark transition cursor-pointer"
            @click="handleFolderClick(folder)"
          >
            <td class="px-3 py-4 flex items-center gap-2 text-left">
              <IrisIcon name="folder" class="shrink-0 w-5 h-5" />
              <span>{{ folder.Name }}</span>
            </td>
            <td class="px-2 py-4 whitespace-nowrap text-text-color-300 dark:text-text-color-300-dark text-left">
              {{ folder.TotalFiles }} files, {{ formatBytes(folder.TotalSize) }}
            </td>
            <td v-if="!isEditMode" class="px-2 py-4">
            </td>
          </tr>
          <!-- Files Section -->
          <tr
            v-for="(file, index) in filteredFiles"
            :key="`file-${index}`"
            class="border-t border-border-color dark:border-border-color-dark hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark transition"
          >
            <td class="px-3 py-4 flex items-center gap-2 text-left">
              <input
                v-if="isEditMode"
                type="checkbox"
                :checked="file.Selected"
                @change="onFileCheckboxChange(file, $event)"
                class="iris-checkbox cursor-pointer mr-2"
              />
              <IrisIcon :name="getIcon(file.Extension)" class="shrink-0 w-5 h-5" :view-box="'0 0 512 512'"/>
              <span class="whitespace-normal break-all">{{ file.Name }}</span>
            </td>
            <td class="px-2 py-4 text-text-color-300 dark:text-text-color-300-dark text-left">
              {{ formatBytes(file.Size) }}
            </td>
            <td v-if="!isEditMode" class="px-2 py-4">
              <button 
                @click.stop="handleRemoveFile(file.Id)"
                class="cursor-pointer"
              >
                <IrisIcon name="trash" class="w-4 h-4" />
              </button>
            </td>
          </tr>
          <!-- Show "No files" only if no folders AND no files -->
          <tr v-if="filteredFiles.length === 0 && filteredFolders.length === 0" class="border-t border-border-color dark:border-border-color-dark">
            <td :colspan="isEditMode ? 2 : 3" class="px-4 py-4 text-center">
              No files
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
