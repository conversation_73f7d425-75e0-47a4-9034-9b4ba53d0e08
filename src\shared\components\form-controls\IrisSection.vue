<script setup lang="ts">
import { type PropType, provide, getCurrentInstance, onMounted, type VNode, type Component, ref } from 'vue'
import { irisInputComponentSymbol } from '@/shared/services/iris-input-component-symbol'

const props = defineProps({
    id : {
        type: String,
        required: true
    },
    cols : {
        type: String as PropType<'one' | 'two'>,
        default: "one",
        validator: (value: string) => {
            // Ensure the value is one of the specified options
            return ['one', 'two'].includes(value);
        }
    },
    title: {
        type: String
    },
    collapsible: {
        type: Boolean,
        default: true
    },
    collapsed: {
        type: Boolean,
        default: true
    },
    flush: {
        type: Boolean,
        default: false
    }
})

const instance = getCurrentInstance()
const hasValidChildren = ref(true)

const validateSlotContent = (slotContent: VNode[] | undefined): boolean => {
    if (!slotContent)
        return true

    const slots = slotContent.filter(vnode => vnode.type !== Comment);

    let isValid = true
    slots.forEach(child => {

        if (!isValid)
            return

        // Check if the component type is allowed
        if (!isComponentAllowed(child as any)) {
            const compName = (child as any).type.__name
            console.error(`Invalid component ${compName} found in IrisSection.`);
            isValid = false
        }
    })

    return isValid
}

function isComponentAllowed(irisComponent : any) {

    if (irisComponent == null)
        return true

    const type = irisComponent.type;

    if (type === Comment || (typeof type === 'symbol' && (type === Symbol.for('v-cmt'))))
        return true

    return typeof type === 'object' && type !== null && (type as any)[irisInputComponentSymbol];
}

provide('validateSlotContent', validateSlotContent)

onMounted(() => {
    const defaultSlot = instance?.slots.default?.()
    hasValidChildren.value = validateSlotContent(defaultSlot)
})
</script>

<template>
<div>
    <div :id="`${id}-accordion-collapse`"
     data-accordion="collapse" 
     :data-active-classes="collapsible ? 'bg-bg-color-200 text-text-color-200 dark:bg-bg-color-200-dark dark:text-text-color-200-dark' : 'bg-bg-color text-text-color dark:bg-bg-color-dark dark:text-text-color-dark'"
     v-if="hasValidChildren">
        <h2 v-show="title" :id="id +'-collapse-heading'">
            <button type="button" 
                :disabled="!collapsible"
                class="flex items-center justify-between w-full font-medium rtl:text-right text-page border-border-color 
                focus:ring-2 focus:ring-border-color gap-3 
                text-text-color bg-bg-color
                dark:text-text-color-dark dark:bg-bg-color-dark dark:border-border-color-dark dark:focus:ring-border-color-dark"
                :class="{
                    'border-b py-5' : flush == true,
                    'border hover:bg-bg-color-300 p-5 dark:hover:bg-bg-color-300-dark' : flush == false
                }"
                :data-accordion-target="`#${id}-collapse-body`"
                :aria-expanded="collapsed" 
                :aria-controls="`${id}-collapse-body`">
                <span>{{ title }}</span>
                <svg v-if="collapsible" data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                </svg>
            </button>
        </h2>
        <div :id="`${id}-collapse-body`" class="hidden" :aria-labelledby="`${id}-collapse-heading`">
            <div class="border-border-color dark:border-border-color-dark" :class="{
                'py-5 border-b' : flush == true,
                'p-5 border border-t-0' : flush == false
             }">
                <div :class="{'grid grid-cols-1 gap-4': cols === 'one', 'grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6': cols === 'two'}">
                    <slot></slot>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="p-4 border rounded-lg text-red-600">
        Invalid components detected within the Section. Only certain components from the Iris library are allowed.
    </div>
</div>
</template>