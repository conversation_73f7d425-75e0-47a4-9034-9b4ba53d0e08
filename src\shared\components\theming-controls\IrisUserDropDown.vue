<script setup lang="ts">
import { computed, onMounted, ref, useAttrs } from 'vue'
import { Dropdown, type DropdownOptions } from 'flowbite'
import { constants } from '@/shared/services/constants'
import { HorizontalAlignmentEnum } from '@/shared/services/form-control-enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Common from '@/shared/services/common'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

defineOptions({
  inheritAttrs: false
})

const props = defineProps({
    value: {
        type: Object,
        required: true
    },
    avatarSize: {
        type: Number,
        default: constants.formControls.userAvatarSize
    },
    showUserName: {
        type: Boolean,
        default: true
    },
    setupMode: {
        type: Boolean,
        default: true
    },
    pictureAlignment: {
        type: String,
        default: HorizontalAlignmentEnum.left,
        validator: (value: string) => {
            return (<any>Object).values(HorizontalAlignmentEnum).includes(value)
        }
    },
    useIcons: {
        type: Boolean,
        default: false
    }
})

const systemInfo = useIrisSystemInfoStore().getData()
const $targetEl = ref<HTMLElement>()
const $triggerEl = ref<HTMLElement>()
const $iframe = ref<HTMLElement>()
const showUserSettings = ref(false)
let $dropdown = null

const computedStyleClass = computed(() => {
  const conditionalClasses = props.showUserName ? '' : 'focus:ring-2'
  const attrs = useAttrs()
  
  return [attrs.class, conditionalClasses].filter(Boolean).join(' ')
})

onMounted(() => {
    initialize()
})

function initialize() : void {
    
    if (!systemInfo.isGuest) {
        // init the dropdown
        const instanceOptions = {}
        const options : DropdownOptions = {
            placement: 'bottom',
            triggerType: 'click',
            offsetSkidding: 0,
            offsetDistance: 4,
        }

        $dropdown = new Dropdown($targetEl.value, $triggerEl.value, options, instanceOptions)
    }
}

function showModal() : void {
    showUserSettings.value = true
}
</script>
<template>
    <div class='userBox' v-if="value.loginEnabled && systemInfo.isGuest">
        <a href='/user/login/' class='signin-link' :class="$attrs.class">
            <IrisIcon v-if="useIcons" name="right-to-bracket"/>
            {{ value.labels.login }}
        </a>
    </div>
    <div class='userBox' v-if="value.allowSelfRegistration && systemInfo.isGuest">
        <a href='/user/register/' class='signup-link' :class="$attrs.class">
            <IrisIcon v-if="useIcons" name="pen-to-square"/>
            {{ value.labels.signup }}
        </a>
    </div>
    <div class='userBox inline-block btn-user-menu' v-if="!systemInfo.isGuest">  
        <a ref="$triggerEl" href='javascript:void(0);' class="user-info flex" :class="computedStyleClass">
            <span v-if="showUserName" class="flex items-center gap-1 p-0.5">
                <span class='hidden md:block' v-if="pictureAlignment == HorizontalAlignmentEnum.right">
                    {{ value.user.name }}
                </span>
                <img 
                    :src='`${Common.getBaseUrl()}/userprofile/img/${systemInfo.userId}`' 
                    :style="{ height: `${avatarSize}rem` }" 
                    :alt='value.labels.myProfile' 
                    crossorigin="anonymous" 
                    class="rounded-full"
                />
                <span class='hidden md:block max-w-32 truncate' v-if="pictureAlignment == HorizontalAlignmentEnum.left">
                    {{ value.user.name }}
                </span>
                <IrisIcon name="chevron-down" aria-hidden="true" width="0.7rem" height="0.7rem" fill="none"/>
            </span>
            <template v-else>
                <img 
                    :src='`${Common.getBaseUrl()}/userprofile/img/${systemInfo.userId}`' 
                    :style="{ height: `${avatarSize}rem` }" 
                    :alt='value.labels.myProfile' 
                    :title='value.user.name' 
                    crossorigin="anonymous"
                    class="rounded-full"
                 />
            </template>
        </a>
        <div ref="$targetEl" class="z-10 w-80 border border-border-color hidden bg-bg-color divide-y divide-border-muted rounded-lg shadow dark:border-border-color-dark dark:bg-bg-color-dark dark:divide-border-muted-dark">
            <div class='p-4'>
                <div class="flex items-center justify-start mb-4">
                    <img :src='`${Common.getBaseUrl()}/userprofile/profileimg/${systemInfo.userId}`' class='rounded user-menu-img-lg' style="width:75px;height:75px;" :alt='value.labels.myProfile' crossorigin="anonymous" />
                    <div class='user-menu-name-box ml-4'>
                        <div class='user-menu-name-lg text-responsive'> {{ value.user.name }}</div>
                        <div class='mb-2 text-sm text-responsive'> {{ value.user.userName }}</div>
                        <button type='button' :data-title='value.labels.mySettings'
                            :data-id='systemInfo.userId' 
                            @click="showModal"
                            class='btn btn-xs btn-primary btn-mag-usr-mysettings'>{{ value.labels.mySettings }}</button> 
                    </div>
                </div>
                
                <ul class="py-2 text-sm text-primary" >
                    <li v-for="item in value.options">
                        <a :href="item.link" :target="item.target" 
                            class="block py-2 hover:text-primary-hover">
                            <IrisIcon 
                                v-if="item.icon" 
                                :name="`${item.icon}`"
                                class="me-2 w-4 h-4"/>
                            {{ item.text }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <iris-modal ref="$iframe" id="mag_ussd_modal" 
        :title="value.labels.mySettings" 
        size="full" :show="showUserSettings" 
        :body-full-height="true"
        @onHide="showUserSettings = false">
        <template #content>
            <iframe frameborder="0" v-if="showUserSettings" 
                :src="`/userprofile/personalinfo/${systemInfo.userId}?__m=ifrmc&amp;__iframe=1`"
                style="width:100%;height:100%;min-height:100%;">
            </iframe>
        </template>
    </iris-modal>
</template>