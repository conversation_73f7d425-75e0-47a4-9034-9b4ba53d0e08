import { describe, it, expect, vi } from "vitest"
import { VueWrapper, mount } from "@vue/test-utils"
import Common from "@/shared/services/common"
import router from '@/router'
import IrisStaticAssets from '@/shared/components/general-controls/IrisStaticAssets.vue'

describe("IrisStaticAssets", () => {
    const createWrapper = (props: any): VueWrapper => {
        const wrapper = mount(IrisStaticAssets, {
            props: { id: Common.newGuid(), value: undefined, field: undefined, ...props },
            global: {
                plugins: [router]
            }
        })

        return wrapper
    }

    vi.mock('@/stores/system-info-store', () => ({
        useIrisSystemInfoStore: () => ({
            getData: vi.fn(() => ({
            env: {
                baseUrl: 'https://dev.magentrix.com',
                production: false
            },
            currentLanguage: 'en'
            }))
        })
    }))

    it("Test component initialization", () => {
        expect(IrisStaticAssets).toBeTruthy()
    })

    it.skip("Test id and text", () => {
        const props = { id: 'Test01' }
        const wrapper = createWrapper(props)
        const id = wrapper.find('#Test01').attributes('id')
        const text = wrapper.find('#Test01').text()

        expect(id).toEqual('Test01')
        expect(text).toEqual('https://dev.magentrix.com')
    })
})