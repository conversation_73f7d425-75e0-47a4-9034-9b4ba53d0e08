import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from "vitest"
import { createPinia, setActivePinia } from "pinia"
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import Common from "@/shared/services/common"
import type SystemInfoData from "@/shared/services/system-info-data"

describe("Common", async () => {
    let systemInfo: SystemInfoData

    beforeAll(async () => {
        const pinia = createPinia()

        setActivePinia(pinia)
        systemInfo = useIrisSystemInfoStore().getData()
        await systemInfo.loadData()
    })

    beforeEach(() => {
        vi.useFakeTimers()
    })

    afterEach(() => {
        vi.useRealTimers()
    })

    it("Test getBaseUrl", () => {
        const result = Common.getBaseUrl()
        
        expect(result).toEqual('https://dev.magentrix.com')
    })

    it("Test getFullUrl", () => {
        const result = Common.getFullUrl('Test')
        
        expect(result).toEqual('https://dev.magentrix.com/Test')
    })

    it("Test setPageTitle", () => {
        Common.setPageTitle('Test')

        const expected = `Test | ${systemInfo.portalName}`
        
        expect(document.title).toEqual(expected)
    })

    it("Test newGuid", () => {
        const result = Common.newGuid()
        
        expect(result.length).toEqual(36)
    })

    it("Test extendObject", () => {
        const f = { firstName: 'John', lastName: 'Smith', age: 24 }
        const p = { lastName: 'Doe' }
        const r = Common.extendObject(f, p)
        const result = r.firstName == 'John' && r.lastName == 'Doe' && r.age == 24
        
        expect(result).toEqual(true)
    })

    it("Test isNumber without type check (3)", () => {
        const result = Common.isNumber(3)
        
        expect(result).toEqual(true)
    })

    it("Test isNumber without type check ('3')", () => {
        const result = Common.isNumber('3')
        
        expect(result).toEqual(true)
    })

    it("Test isNumber with type check (3)", () => {
        const result = Common.isNumber(3, true)
        
        expect(result).toEqual(true)
    })

    it("Test isNumber with type check ('3')", () => {
        const result = Common.isNumber('3', true)
        
        expect(result).toEqual(false)
    })

    it("Test formatFileSize (Bytes)", () => {
        const result = Common.formatFileSize(1024 ** 0)
        
        expect(result).toEqual('1 Bytes')
    })

    it("Test formatFileSize (KB)", () => {
        const result = Common.formatFileSize(1024 ** 1)
        
        expect(result).toEqual('1 KB')
    })

    it("Test formatFileSize (MB)", () => {
        const result = Common.formatFileSize(1024 ** 2)
        
        expect(result).toEqual('1 MB')
    })

    it("Test formatFileSize (GB)", () => {
        const result = Common.formatFileSize(1024 ** 3)
        
        expect(result).toEqual('1 GB')
    })

    it("Test numParts", () => {
        const parts = Common.numParts(12.938)
        const result = parts.integer == 12 && parts.decimal == 938

        expect(result).toEqual(true)
    })

    it("Test isValidEmail (Valid)", () => {
        const result = Common.isValidEmail('<EMAIL>')

        expect(result).toEqual(true)
    })

    it("Test isValidEmail (Invalid)", () => {
        const result = Common.isValidEmail('leo@magentrix')

        expect(result).toEqual(false)
    })

    it("Test isValidUrl (Valid)", () => {
        const result = Common.isValidUrl('https://www.magentrix.com')

        expect(result).toEqual(true)
    })

    it("Test isValidUrl (Invalid)", () => {
        const result = Common.isValidUrl('www.magentrix.com')

        expect(result).toEqual(false)
    })

    it("Test isValidPhone (Valid #1)", () => {
        const result = Common.isValidPhone('************')

        expect(result).toEqual(true)
    })

    it("Test isValidPhone (Valid #2)", () => {
        const result = Common.isValidPhone('(*************')

        expect(result).toEqual(true)
    })

    it("Test isValidPhone (Invalid)", () => {
        const result = Common.isValidPhone('A289205290')

        expect(result).toEqual(false)
    })

    it("Test toTitleCase", () => {
        const result = Common.toTitleCase('magentrix')

        expect(result).toEqual('Magentrix')
    })

    it("Test padNumber", () => {
        const result = Common.padNumber(12, 5, '0')

        expect(result).toEqual('00012')
    })

    it("Test formatAutoNumber", () => {
        const result = Common.formatAutoNumber(1234, 'MAG-{0:000000}')

        expect(result).toEqual('MAG-001234')
    })

    it("Test includeUrlProtocol (non secure)", () => {
        const result = Common.includeUrlProtocol('magentrix.com', false)

        expect(result).toEqual('http://magentrix.com')
    })

    it("Test includeUrlProtocol (secure)", () => {
        const result = Common.includeUrlProtocol('magentrix.com')

        expect(result).toEqual('https://magentrix.com')
    })

    it("Test includeUrlProtocol (secure + with leading slash)", () => {
        const result = Common.includeUrlProtocol('/magentrix.com')

        expect(result).toEqual('https://magentrix.com')
    })

    it("Test includeUrlProtocol (two leading slash)", () => {
        const result = Common.includeUrlProtocol('//magentrix.com')

        expect(result).toEqual('//magentrix.com')
    })

    it("Test includeUrlProtocol (ftp protocol)", () => {
        const result = Common.includeUrlProtocol('ftp://magentrix.com')

        expect(result).toEqual('ftp://magentrix.com')
    })

    it("Test trimSides (start)", () => {
        const result = Common.trimSides('**Test**', '*', '', 's')

        expect(result).toEqual('Test**')
    })

    it("Test trimSides (end)", () => {
        const result = Common.trimSides('**Test**', '*', '', 'e')

        expect(result).toEqual('**Test')
    })

    it("Test trimSides (both)", () => {
        const result = Common.trimSides('**Test**', '*', '', 'b')

        expect(result).toEqual('Test')
    })

    it("isAbsoluteUrl('magentrix.com')", () => {
        const result = Common.hasDomain('magentrix.com')

        expect(result).toEqual(true)
    })

    it("isAbsoluteUrl('https://magentrix.com')", () => {
        const result = Common.hasDomain('https://magentrix.com')

        expect(result).toEqual(true)
    })

    it("isAbsoluteUrl('https://magentrix.com?p=1')", () => {
        const result = Common.hasDomain('https://magentrix.com?p=1')

        expect(result).toEqual(true)
    })

    it("isAbsoluteUrl('/department/employees')", () => {
        const result = Common.hasDomain('/department/employees')

        expect(result).toEqual(false)
    })

    it("isAbsoluteUrl('/product?price=10.29')", () => {
        const result = Common.hasDomain('/product?price=10.29')

        expect(result).toEqual(false)
    })

    it("countOccurenceInString() matched", () => {
        const result = Common.countOccurenceInString('1,2,3,4', ',')

        expect(result).toEqual(3)
    })

    it("countOccurenceInString() not matched", () => {
        const result = Common.countOccurenceInString('1,2,3,4', ':')

        expect(result).toEqual(0)
    })

    it("getHostName()", () => {
        const result = Common.getBackendHostName()

        expect(result).toEqual('dev.magentrix.com')
    })

    it("isValidJson() Good", () => {
        const json = '{ "companyName": "Magentrix" }'
        const result = Common.isValidJson(json)

        expect(result).toEqual(true)
    })

    it("isValidJson() Bad", () => {
        const json = '{ companyName: "Magentrix" }'
        const result = Common.isValidJson(json)

        expect(result).toEqual(false)
    })

    it("tryParseJson() Good", () => {
        const json = '{ "companyName": "Magentrix" }'
        const result = Common.tryParseJson(json)

        expect(result.companyName).toEqual('Magentrix')
    })

    it("tryParseJson() Bad", () => {
        const json = '{ companyName: "Magentrix" }'
        const result = Common.tryParseJson(json)

        expect(result).toEqual(null)
    })

    it("removeUrlParameters() without parameters", () => {
        const url = 'https://www.magentrix.com/'
        const result = Common.removeUrlParameters(url)

        expect(result).toEqual('https://www.magentrix.com/')
    })

    it("removeUrlParameters() with parameters", () => {
        const url = 'https://www.magentrix.com/?kw=test'
        const result = Common.removeUrlParameters(url)

        expect(result).toEqual('https://www.magentrix.com/')
    })

    it("removeUrlParameters() with only question mark", () => {
        const url = 'https://www.magentrix.com/?'
        const result = Common.removeUrlParameters(url)

        expect(result).toEqual('https://www.magentrix.com/')
    })

    it("removeLastChars() number bigger than text length", () => {
        const text = 'text'
        const result = Common.removeLastChars(text, 5)

        expect(result).toEqual('')
    })

    it("removeLastChars() with negative number", () => {
        const text = 'text'

        expect(() => Common.removeLastChars(text, -5)).toThrowError('invalid argument: numberOfChars, should be greater than zero.')
    })

    it("removeLastChars() valid number less than text len", () => {
        const text = 'text'
        const result = Common.removeLastChars(text, 2)

        expect(result).toEqual('te')
    })

    it("upsertUrlParameters()", () => {
        const url = "https://abc.com"
        const result = Common.upsertUrlParameters(url, [ { key:"param1", value:"value1" } ])

        expect(result).toEqual("https://abc.com?param1=value1")
    })

    it("upsertUrlParameters() remove existing parameter", () => { 
        const url = "https://abc.com?param2=value2"
        const result = Common.upsertUrlParameters(url, [ { key:"param1", value:"value1" }, { key:"param2", value:"{$remove}" }, ])

        expect(result).toEqual("https://abc.com?param1=value1")
    })

    it("replaceAll()", () => {
        const text = 'Hello {0}. Bye {0}'
        const result = Common.replaceAll(text, '{0}', 'Leo')

        expect(result).toEqual('Hello Leo. Bye Leo')
    })

    it("constructUrl()", () => {
        const params: any = {
            term: "kw",
            srt: "D",
            size: 5,
            pinx: 3
        }
        const baseURL = "/native/acount"
        const result = Common.constructUrl(baseURL, params)

        expect(result).toEqual("/native/acount?term=kw&srt=D&size=5&pinx=3")
    })

})