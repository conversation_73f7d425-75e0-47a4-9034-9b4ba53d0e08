const messages = {
  partnerProgram: "Партнёрская программа",
  partnerManagement: "Управление партнёрами",
  helponthispage: "Помощь на этой странице",
  search: "Поиск",
  programs: "Программы",
  account: "Аккаунт",
  partnerPrograms: "Партнёрские программы",
  serverError: "Что-то пошло не так!",
  loading: "Загрузка...",
  yes: "Да",
  no: "Нет",
  benefits: "Преимущества",
  programBenefits: "Преимущества программы",
  newProgram: "Новая программа",
  programTiers: "Уровни программы",
  dragDropMessage: "Перетащите уровни, чтобы изменить порядок",
  tier: "Уровень",
  remove: "Удалить",
  addTier: "Добавить уровень",
  editTier: "Редактировать уровень",
  tierBadge: "Значок уровня",
  tierDescription: "Описание уровня",
  tierName: "Название уровня",
  deleteTier: "Удалить уровень",
  noRecords: "Нет данных для отображения.",
  newBenefit: "Новое преимущество",
  cancel: "Отмена",
  close: "Закрыть",
  addBenefit: "Добавить преимущество",
  saving: "Сохранение...",
  existingBenefitName: "Преимущество с таким именем уже существует. Пожалуйста, выберите другое имя.",
  highestTierReached: "Вы находитесь на самом высоком уровне!",
  failFetchData: "Ошибка: не удалось получить данные. Обратитесь к администратору.",
  failInitialize: "Не удалось инициализировать компоненты",
  failSave: "Не удалось сохранить запись. Повторите попытку или обратитесь к администратору.",
  benefitDuplicateNameError: "Преимущество с таким именем уже существует. Пожалуйста, выберите другое имя.",
  save: "Сохранить",
  deleteBenefit: "Удалить преимущество",
  deleteBenefitMessage: "Вы уверены, что хотите удалить это преимущество и все назначения?",
  delete: "Удалить",
  failDelete: "Не удалось удалить запись. Повторите попытку или обратитесь к администратору.",
  edit: "Редактировать",
  first: "Первый",
  last: "Последний",
  next: "Следующий",
  previous: "Предыдущий",
  editBenefit: "Редактировать преимущество",
  noProgramsAvailable: "Нет доступных программ.",
  enrollments: "Регистрации",
  partnerEnrollments: "Регистрации партнёров",
  newEnrollment: "Новая регистрация",
  allPrograms: "Все программы",
  noTiers: "Нет доступных уровней для выбранной программы. Пожалуйста, выберите другую программу или обратитесь к администратору.",
  editEnrollment: "Редактировать регистрацию",
  addEnrollment: "Добавить регистрацию",
  deleteEnrollmentMessage: "Вы уверены, что хотите удалить эту регистрацию и всю информацию?",
  deleteEnrollment: "Удалить регистрацию",
  duplicateEnrollment: "Этот партнёр уже зарегистрирован в данной программе.",
  noAccountsAvailable: "Нет доступных аккаунтов.",
  deleteProgramMessage: "Вы собираетесь удалить эту программу и все её данные, включая уровни, регистрации и критерии. Это действие необратимо.",
  deleteProgram: "Удалить партнёрскую программу",
  setting: "Настройка",
  settings: "Настройки",
  partnerProgramStatus: "Статус партнёрской программы",
  enablePartnerProgram: "Включить партнёрскую программу",
  partnerProgramIsDisabled: "Партнёрская программа отключена",
  partnerProgramIsActive: "Партнёрская программа активна",
  confirmationDisablingPartnerProgram: "Вы уверены, что хотите отключить партнёрскую программу?",
  confirmationDisablingPartnerProgramMessage: "Видимость уровней и преимуществ будет скрыта.",
  ok: "ОК",
  settingsSaved: "Настройки успешно сохранены!",
  back: "Назад",
  benefit: "Преимущество партнёрской программы",
  benefitTypeError: "Не указан тип преимущества.",
  benefitNameRequired: "Не указано имя преимущества.",
  enrollmentDateMustBeBeforeExpiration: "Дата регистрации должна быть раньше даты истечения срока.",
  editProgram: "Редактировать программу",
  addProgram: "Добавить программу",
  programNameRequired: "Не указано имя программы.",
  tierNameRequired: "Не указано имя уровня.",
  tierInexNameRequired: "Уровень {idx}: имя обязательно.",
  duplicateTierName: "Дублирующееся имя уровня",
  unsavedChangesConfimation: "У вас есть несохранённые изменения. Вы уверены, что хотите закрыть?",
  minTierError: "Необходимо настроить как минимум один уровень.",
  maxTierError: "Можно настроить максимум шесть уровней.",
  duplicateIndexTierName: "Уровень {idx}: дублирующееся имя {tierName}.",
  missingProgramId: "Недопустимая программа",
  program: "Программа",
  newTier: "Новый уровень",
  clone: "Клонировать",
  configure: "Настроить",
  noTiersDefined: "Уровни не определены.",
  criteria: "Критерии программы",
  noCriteriaDefined: "Критерии не определены.",
  addCriterion: "Добавить критерий",
  noBenefitDefined: "Преимущества не определены.",
  atLeastOneTierRequired: "Введите хотя бы один уровень для программы",
  confirm: "Подтвердить",
  noMoreAvailableBenefits: "Больше нет доступных преимуществ для добавления.",
  missingProgramIdOrPartnerAccountId: "Отсутствует информация. Обратитесь в администрацию.",
  noProgramEnrollments: "Вы не зарегистрированы в партнёрской программе. Обратитесь к администратору.",
  noPartnerAccount: "Вы должны быть партнёром, чтобы увидеть эту страницу.",
  certifications: "Сертификаты",
  numberofOpportunities: "Количество возможностей",
  opportunityRevenue: "Доход от возможностей",
  numberofDealRegistrations: "Количество зарегистрированных сделок",
  custom: "Пользовательский",
  configureCriterion: "Настроить критерий",
  createCriterion: "Создать критерий",
  create: "Создать",
  criteriaDuplicateNameError: "Критерий с таким именем уже существует. Пожалуйста, выберите другое имя.",
  quantity: "Количество",
  revenue: "Доход",
  criterionNameRequiredError: "Имя критерия обязательно.",
  criterionTypeRequiredError: "Тип критерия обязателен.",
  trainingCourseRequiredError: "Для сертификатов необходим курс обучения.",
  rollupFieldRequiredError: "Для дохода необходимо поле суммирования.",
  attainmentDateFieldRequired: "Требуется поле даты достижения",
  unitofMeasureRequired: "Требуется единица измерения.",
  editCriterion: "Редактировать критерий",
  missingFields: "Некоторая информация отсутствует",
  trainingCourse: "Курс обучения",
  name: "Имя",
  type: "Тип",
  filterLogic: "Логика фильтра (необязательно)",
  filterLogicPlaceHolder: "например, (1 и 2) или 3",
  reset: "Сбросить",
  addFilter: "Добавить фильтр",
  equals: "равно",
  notEquals: "не равно",
  includes: "включает",
  excludes: "исключает",
  greaterThan: "больше чем",
  lessThan: "меньше чем",
  lessOrEqual: "меньше или равно",
  greaterOrEqual: "больше или равно",
  contains: "содержит",
  notContain: "не содержит",
  startWith: "начинается с",
  notStartWith: "не начинается с",
  partnerAccount: "Аккаунт партнёра",
  enrollmentDate: "Дата регистрации",
  expirationDate: "Дата окончания срока",
}

export default messages