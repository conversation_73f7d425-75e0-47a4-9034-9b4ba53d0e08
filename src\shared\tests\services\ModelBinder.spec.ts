import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from "vitest"
import { create<PERSON><PERSON>, setActivePinia } from "pinia"
import { exchangeInfoMock } from "@/shared/tests/mocks/exchange-info"
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import ModelBinder from "@/shared/services/model-binder"
import DateTimeHelper from "@/shared/services/dateTime-helper"
import type SystemInfoData from "@/shared/services/system-info-data"

describe("ModelBinder", () => {
    const dateTime = new Date('15 December 2023 1:39:08.524 PM')
    let modelBinder: ModelBinder
    let dateTimeHelper: DateTimeHelper
    let systemInfo: SystemInfoData

    beforeAll(async () => {
        const pinia = createPinia()

        setActivePinia(pinia)
        systemInfo = useIrisSystemInfoStore().getData()
        await systemInfo.loadData()
        modelBinder = new ModelBinder()
        dateTimeHelper = new DateTimeHelper()
        vi.spyOn(systemInfo, 'currentLocale', 'get').mockReturnValue('en-CA')
        vi.spyOn(systemInfo, 'preferredCurrency', 'get').mockReturnValue('CAD')
        vi.spyOn(systemInfo, 'isMultiCurrency', 'get').mockReturnValue(true)
        vi.spyOn(systemInfo, 'isDisplayUserCurrency', 'get').mockReturnValue(true)
        vi.spyOn(systemInfo, 'currencyExchangeRates', 'get').mockReturnValue(JSON.parse(exchangeInfoMock))
    })

    beforeEach(() => {
        vi.useFakeTimers()
    })

    afterEach(() => {
        vi.useRealTimers()
    })

    it("Password/Secret", () => {
        const fieldMetaData = {
            FieldType: 'Password'
        }
        const password = 'Password123'
        const result = modelBinder.formatData(password, fieldMetaData)
        const expected = "*".repeat(password.length)

        expect(result).toEqual(expected)
    })

    it("Picklist/GlobalPicklist", () => {
        const fieldMetaData = {
            FieldType: 'Picklist',
            PicklistEntries: [
                {
                    Label: 'Red',
                    Value: 'R'
                },
                {
                    Label: 'Green',
                    Value: 'G'
                },
                {
                    Label: 'Blue',
                    Value: 'B'
                }
            ]
        }
        const result = modelBinder.formatData('R', fieldMetaData)

        expect(result).toEqual('Red')
    })

    it("Currency", () => {
        const fieldMetaData = {
            FieldType: 'Currency'
        }
        const record = {
            CurrencyIsoCode: 'USD'
        }
        const result = modelBinder.formatData(12.98, fieldMetaData, record)

        expect(result).toEqual('USD 12.98 (CAD 16.64)')
    })

    it("Number", () => {
        const fieldMetaData = {
            FieldType: 'Number',
            Precision: 3
        }
        const result = modelBinder.formatData(12.98, fieldMetaData)

        expect(result).toEqual('12.980')
    })

    it("Percent", () => {
        const fieldMetaData = {
            FieldType: 'Percent',
            Precision: 3
        }
        const result = modelBinder.formatData(12.98, fieldMetaData)

        expect(result).toEqual('12.980%')
    })

    it("DateTime (DisplayFormatString: null, TimeDisplayOption: null)", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'DateTime'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('Dec 15, 2023, 1:39 p.m.')
    })

    it("DateTime (DisplayFormatString: null, TimeDisplayOption: 'Minutes')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'DateTime',
            TimeDisplayOption: 'Minutes'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('Dec 15, 2023, 1:39 p.m.')
    })

    it("DateTime (DisplayFormatString: null, TimeDisplayOption: 'Seconds')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'DateTime',
            TimeDisplayOption: 'Seconds'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('Dec 15, 2023, 1:39:08 p.m.')
    })

    it("DateTime (DisplayFormatString: null, TimeDisplayOption: 'Milliseconds')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'DateTime',
            TimeDisplayOption: 'Milliseconds'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('Dec 15, 2023, 1:39:08.524 p.m.')
    })

    it("DateTime (DisplayFormatString: '{0:yyyy-MM-dd h:mm tt}', TimeDisplayOption: null)", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'DateTime',
            DisplayFormatString: '{0:yyyy-MM-dd h:mm tt}'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('2023-12-15 1:39 PM')
    })

    it("Date (DisplayFormatString: '{0:yyyy-MM-dd}')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Date',
            DisplayFormatString: '{0:yyyy-MM-dd}'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('2023-12-15')
    })

    it("Date (DisplayFormatString: null)", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Date'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('Dec 15, 2023')
    })

    it("Time (DisplayFormatString: '{0:h:mm tt}')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Time',
            DisplayFormatString: '{0:h:mm tt}'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('1:39 PM')
    })

    it("Time (DisplayFormatString: null, TimeDisplayOption: 'Minutes')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Time',
            TimeDisplayOption: 'Minutes'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('1:39 p.m.')
    })

    it("Time (DisplayFormatString: null, TimeDisplayOption: 'Seconds')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Time',
            TimeDisplayOption: 'Seconds'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('1:39:08 p.m.')
    })

    it("Time (DisplayFormatString: null, TimeDisplayOption: 'Milliseconds')", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const fieldMetaData = {
            FieldType: 'Time',
            TimeDisplayOption: 'Milliseconds'
        }
        const result = modelBinder.formatData(utc, fieldMetaData)

        expect(result).toEqual('1:39:08.524 p.m.')
    })

    it("TextArea", () => {
        const content = 'John\nSmith'
        const fieldMetaData = {
            FieldType: 'TextArea'
        }
        const result = modelBinder.formatData(content, fieldMetaData)

        expect(result).toEqual('John\nSmith')
    })
})
