<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useIrisPage } from '@/shared/composables/iris-page'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import { RequestMethod } from '@/shared/services/enums'
import IrisCheckBox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisTextBox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisRange from '@/shared/components/form-controls/IrisRange.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import IrisValidationSummary from '@/shared/components/form-controls/IrisValidationSummary.vue'
import DataAccess from '@/shared/services/data-access'
import IrisDateTime from '@/shared/components/form-controls/IrisDateTime.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import I18nHelper from '@/shared/services/i18n-helper'
import LanguageHelper from '@/shared/services/language-helper'
import DatabaseError from '@/shared/services/database-error'
import Try from '@/shared/services/try'
import IrisSortable from '@/shared/services/sortable'
import IrisFileUploader from '@/shared/components/form-controls/IrisFileUploader.vue'

const base = useIrisPage()
const model:any = ref(null)
const carModel: any = ref(null)
const store = useIrisFormControlStore()
const text = ref('Magentrix')
const dateTime = ref(new Date(2024, 4, 3, 15, 19, 23, 192))
const systemInfo = useIrisSystemInfoStore().getData()
const i18nHelper = new I18nHelper()
const languageHelper = new LanguageHelper()
const myForm1 = ref()

function setHidden() {
  const el = document.getElementById("hiddenField") as HTMLInputElement
  const obj = {
    firstName: 'leo"nid',
    lastName: "Jo'hn"
  }

  el.value = JSON.stringify(obj)
}

function printName() {
  const el = document.getElementById("hiddenField") as HTMLInputElement
  const obj = JSON.parse(el.value)

  console.log(obj.firstName + ' ' + obj.lastName)
}

async function updateCar() {
  try {
    // carModel.value.Name = ''
    // carModel.value.Year__c = '2021a'
    // carModel.value.Expired__c = 'false1'
    // carModel.value.Electric__c = 'false1'


    const dataAccess = new DataAccess()
    const response = await dataAccess.edit("Car__c", carModel.value)
    // const data = {
    //   "Name": "Lexus123",
    //   "Brand__c": "07N00000000001C0000",
    //   "Year__c": "2025",
    //   "In_Stock__c": true,
    //   "Electric__c": false,
    //   "Manual__c": false,
    //   "External_URL__c": "Test"
    // }

    console.log('Car Updated Successfully.', response)
  }
  catch (ex) {
    if (ex instanceof DatabaseError) {
      console.log(111, (ex as DatabaseError).message)
      console.log(222, (ex as DatabaseError).errors)
      store.addDbErrors('form1', (ex as DatabaseError).errors)
    }
    else
      console.log(ex)
  }
}

function updateCar2() {
  Try(async () => {
    // carModel.value.Name = ''
    // carModel.value.Year__c = '2021a'
    // carModel.value.Expired__c = 'false1'
    // carModel.value.Electric__c = 'false1'


    const dataAccess = new DataAccess()
    const response = await dataAccess.edit("Car__c", carModel.value)
    // const data = {
    //   "Name": "Lexus123",
    //   "Brand__c": "07N00000000001C0000",
    //   "Year__c": "2025",
    //   "In_Stock__c": true,
    //   "Electric__c": false,
    //   "Manual__c": false,
    //   "External_URL__c": "Test"
    // }

    console.log('Car Updated Successfully.', response)
  })
  .Catch(DatabaseError, (ex: DatabaseError) => {
    console.log(111, ex.message)
    console.log(222, ex.errors)
    store.addDbErrors('form1', ex.errors)
  })
  .Catch(Error, (ex: Error) => console.log(ex))
}

onMounted(async () => {
  const dataAccess = new DataAccess()

  const car = await dataAccess.retrieve('07O0000000000gh0000')
  carModel.value = car.record
  // console.log(car.record)
  // const car = await dataAccess.query('Select Id, Name, Brand__c, Year__c, In_Stock__c, Electric__c, Manual__c, External_URL__c From Car__c Where Id = "07O0000000000gh0000"')
  // carModel.value = car.data[0]
  // delete carModel.value.__Brand__cFilter
  // carModel.value = await dataAccess.query('Select Id, Name, Brand__c, Year__c, In_Stock__c, Electric__c, Manual__c, External_URL__c From Car__c Where Id = "07O0000000000gh0000"')

  base.setPageTitle('Test Page')
  model.value = await dataAccess.execute('iris/GetSampleIrisData', null, RequestMethod.get)
  
  // model.value.opps[0].CloseDate = '2024-09-19T15:34:12.658'
  // model.value.__metadatas.Opportunity.Fields[2].DisplayFormatString = "{0:yyyy-MMMM-dd hh:mm:ss.SSS tt}"

  const sortable = new IrisSortable()
  const el = document.getElementById('items')

  sortable.create(el!)
  sortable.addEventListener('onMove', (e: any) => console.log(e.detail.evt.isTrusted))
})

async function badCode() {
  Try(() => {
    console.clear()
    console.log('Try Started')
    // throw new Error('Regular Error')
    throw new DatabaseError('Datebase Error', [])
    console.log('Try Finished')
  })
  .Catch(DatabaseError, () => console.log('Database Error Handled'))
  .Catch(Error, () => console.log('Regular Error Handled'))
  .Finally(() => console.log('Finally Executed'))

  console.log('Some other code ...')
}

function handleUploadSuccess(args: any) {
  if (args.operation === 'upload') {
      const responseData = JSON.parse(args.e.currentTarget.response)
      
      args.file.newFileName = responseData.fileName
  }
}

function handleUploadRemoving(args: any) {
  if (args.filesData.length > 0) {
    const fileNameToSend = args.filesData[0].newFileName || args.filesData[0].name

    args.customFormData = [
        { 'fileName': fileNameToSend }
    ]
  }
}

function isValidForm() {
  console.log('CV', myForm1.value.isValidForm())
}
</script>

<template>
  <iris-form
    ref="myForm1"
    id="myForm1"
    :on-submit="() => console.log('Form Submitted')"
    @change="(e: Event, isValid: boolean) => console.log('FORM CHANGE', isValid)"
    @dirty="() => console.log('FORM DIRTY')">

    <label for=""txt1>Required</label>&nbsp;
    <input type="text" id="txt1" name="txt1" required /><br>

    <label for=""txt2>Min Length 5</label>&nbsp;
    <input type="text" id="txt2" name="txt2" required minlength="5"/><br>

    <label for=""txt3>No Constraints</label>&nbsp;
    <input type="text" id="txt3" name="txt3"/><br>

    <label for=""txt4>Number Min 3</label>&nbsp;
    <input type="number" id="txt4" name="txt4" required min="3"/>
  </iris-form>
  <button type="button" class="btn btn-primary" @click.prevent="isValidForm">Check Form Validity</button>
  <br><br><br>

  <!-- File Uploader -->
  <IrisFileUploader
    id='fu1'
    label='Profile Picture'
    saveUrl='https://dev.magentrix.com/iris/upload'
    removeUrl='https://dev.magentrix.com/iris/remove'
    :required='true'
    :multiple="true"
    :show-file-count="true"
    @upload-success="handleUploadSuccess"
    @upload-failure="console.log('Upload failed')"
    @file-removing="handleUploadRemoving"
  ></IrisFileUploader>
  <!-- boxCss="border-4 border-indigo-500 bg-red-200 rounded-md" -->
  <br>

  <!-- try catch -->
  <button class="btn btn-primary" @click="badCode">BAD CODE</button>

  <!-- Menu -->
  <nav class="my-3">
    <RouterLink to="/">Leo Test</RouterLink> |
    <RouterLink to="/global-search">Global Search</RouterLink> | 
    <RouterLink to="/sam-test">Sam Test</RouterLink> |
    <RouterLink to="/nafis-test">Nafis Test</RouterLink> |
    <RouterLink to="/websocket-test">Websocket Test</RouterLink> |
    <RouterLink to="/setup/ai-assistant-setting">AI Tools Settings</RouterLink> |
    <RouterLink to="/setup/partner-program/settings">Partner Program</RouterLink>
  </nav>

  <!-- Sortable -->
  <ul id="items" class="mt-5 mb-5">
    <li style="cursor: pointer;">item 1</li>
    <li style="cursor: pointer;">item 2</li>
    <li style="cursor: pointer;">item 3</li>
  </ul>

  <hr>
  <IrisForm id="form1" :onSubmit="() => updateCar2()" v-if="carModel">
    <IrisTextBox id="t20" :value="carModel" field="Name"></IrisTextBox>
    <IrisTextBox id="t21" :value="carModel" field="Year__c"></IrisTextBox>
    <IrisCheckBox id="chk20" :value="carModel" field="In_Stock__c"></IrisCheckBox>
    <IrisCheckBox id="chk21" :value="carModel" field="Expired__c"></IrisCheckBox>
    <IrisCheckBox id="chk22" :value="carModel" field="Electric__c"></IrisCheckBox>

    <button type="submit" class="btn btn-primary my-3">Submit</button>
      
    <IrisValidationSummary form-id="form1" class="my-3"></IrisValidationSummary>
  </IrisForm>
  <hr>

  <div class="my-5 p-5" v-if="model">
    <input type="hidden" id="hiddenField" />
    <button @click="setHidden()">SET</button>
    <button @click="printName()">PRINT</button>

    <div style="color: red; padding: 2rem; margin: 2rem; font-size: 2rem; font-weight: bold; border: 0.2rem solid black; border-radius: 1rem; text-align: center;">
      User name: {{ systemInfo?.userInfo.user.name }}
    </div>
    <button type="button" class="btn-primary" @click="console.log(systemInfo!.userInfo.user.name)">Click me</button>

    <!-- <IrisDateTime id="dt110"></IrisDateTime> -->
    <IrisDateTime id="dt2" type="date" :value="model.opps[0]" field="CloseDate"></IrisDateTime>
    {{ model.opps[0].CloseDate }}
    <button class="btn-primary" @click="model.opps[0].CloseDate = new Date()">Change Date</button>
    <button class="btn-primary" @click="console.log(model.opps[0].CloseDate)">Show Date</button>
    <!-- <IrisSimpleDatePicker id="s10" type="day" value="18" :min="5" :max="20"></IrisSimpleDatePicker>
    <IrisSimpleDatePicker id="s11" type="year" value="2020" :min="2018" :max="2022"></IrisSimpleDatePicker>
    <IrisSimpleDatePicker id="s12" type="month" value="2"></IrisSimpleDatePicker> -->

    <!-- <IrisDateTime id="dt1" type="date-time" :value="model.opps[0]" field="CloseDate" mode="edit" date-format="dd-MMM-yyyy" time-format="hh:mm t"  :localize-time="false" :auto-hide="true" @on-change="console.log($event)"  ></IrisDateTime> -->
    <!-- <IrisDateTime id="dt2" type="time" :value="dateTime" time-format="HH:mm:ss.SSS"></IrisDateTime> -->
  </div>

  <h2 class="text-2xl">IrisIcon</h2>
  <IrisIcon name="magnifying-glass" width="3rem" height="3rem"></IrisIcon>

  <h2 class="text-2xl">Tailwind Floating Label</h2>

  <div class="relative mx-3 my-3">
    <input type="text" id="first_name" class="iris-textbox peer" placeholder="" required />
    <label for="first_name" class="iris-floating-label">First name</label>
  </div>

  <h2 class="text-2xl">Tailwind Input Group</h2>

  <div class="iris-input-group-left m-3">
    <span class="iris-input-group-text">http://</span>
    <input type="text" class="iris-textbox" placeholder="www.example.com" />
  </div>

  <div class="iris-input-group-right m-3">
    <input type="text" class="iris-textbox" placeholder="www.example.com" />
    <span class="iris-input-group-text">http://</span>
  </div>
  
  <div class="m-5" v-if="model">

    <IrisForm id="f1" :onSubmit="() => console.log('Form submitted successfully.')">
      <IrisRange id="rage1" label="Test" :value="80"
        size="large" hint="showing value of 80" :min="10" :max="300" mode="disabled"/>

      <IrisRange id="Contact_Salary" :value="model.contact" 
          field="Salary" hint="Salary of a possible user." :display-labels="true" required/>
      
      <IrisMultiPicklist id="mp" :value="model.contact" field="MultiSelect_Picklist__c" />
     <br/>

     {{ model.contact.MultiSelect_Picklist__c }}

      <IrisTextBox
        type="text"
        id="t1"
        value="leonid"
        :tab-index=5
        required
        place-holder="Test"
        auto-correct
        auto-complete
        auto-capitalize
        :max-length=20
        add-on-text="Hello"
        add-on-icon="fa-solid fa-dollar-sign"
        add-on-text-alignment="right"
        aria-label="This is a test."
        label="Magentrix"
        hint="This is an iris text box."
        mode="edit"
        size="default"
        @on-change="console.log($event)"
        @on-key-press="console.log($event)"
        @on-blur="console.log('Blur')">
      </IrisTextBox>
      <br/>
      <IrisTextBox id="t2" :value="model.role" field="Name"></IrisTextBox>
      <IrisTextBox id="t3" :value="model.role.DefaultApp" field="Name" :floating-label="true" label="Default App"></IrisTextBox>
      <IrisCheckBox id="chk4" :value="model.role" field="TwoFactorEnabled" hint="This is from role entity" required></IrisCheckBox>

      <button type="submit" class="btn btn-primary">Submit</button>
      
      <IrisValidationSummary form-id="f1" class="my-3"></IrisValidationSummary>
    </IrisForm>

    <br>
    <form id="f1">
      <IrisCheckBox id="chk100" :value="false" label="Test1" mode="edit" output-mode="checkbox" required />
    </form>

    <form id="f2">
      <IrisCheckBox id="chk101" :value="false" label="Test2" mode="edit" output-mode="checkbox" required @on-change="console.log($event)" />
    </form>

    <IrisForm id="f3">
      <IrisCheckBox id="chk102" :value="false" label="Test3" mode="edit" output-mode="checkbox" required />
    </IrisForm>
    
    <IrisValidationSummary form-id="f3" class="my-3"></IrisValidationSummary>

    <IrisCheckBox id="chk103" :value="true" label="Test" mode="edit" output-mode="toggle" required/>
    <hr>
    <IrisCheckBox id="chk105" :value="true" label="Test" mode="html" output-mode="checkbox" />
    <IrisCheckBox id="chk106" :value="true" label="Test" mode="html" output-mode="toggle" />
    <hr>
    <IrisCheckBox id="chk108" :value="true" label="Test" mode="disabled" output-mode="checkbox" />
    <IrisCheckBox id="chk109" :value="true" label="Test" mode="disabled" output-mode="toggle" />
    <hr>
    <IrisCheckBox id="chk1011" :value="true" label="Test" required />
    <hr>
    <IrisCheckBox id="chk1012" :value="model.role" field="TwoFactorEnabled" hint="This is from role entity" />
  </div>

 
  
  <h3 class="my-3">Components</h3>
  <div class="ms-3" v-if="model">
    <IrisCheckBox
      id="2FA"
      :value="model.role"
      field="TwoFactorEnabled"
      hint="This is an Iris checkbox."
      :tab-index="2"
      required
      @onChange="console.log($event)"></IrisCheckBox>
    <h2>{{ model.role.TwoFactorEnabled }}</h2>

    <!-- <IrisCheckBox :value="true" label="Test" required /> -->
  </div>

  <h3 class="my-3">Locales and Languages</h3>
  <div class="my-3">
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('en')">Language: English</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('fr')">Language: French</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('de')">Language: German</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('es')">Language: Spanish</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('it')">Language: Italian</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('ja')">Language: Japanese</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('zh')">Language: Chinese</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('pt')">Language: Portuguese</button>
    <button class="ms-2 bg-red-500 p-2 rounded" @click="i18nHelper.changeLanguage('ru')">Language: Russian</button>

    <button class="ms-2 bg-blue-500 p-2 rounded" @click="i18nHelper.changeLocale('en-US')">Locale: English (US)</button>
    <button class="ms-2 bg-blue-500 p-2 rounded" @click="i18nHelper.changeLocale('en-CA')">Locale: English (Canada)</button>
    <button class="ms-2 bg-blue-500 p-2 rounded" @click="i18nHelper.changeLocale('fr-CA')">Locale: French (Canada)</button>
    <button class="ms-2 bg-blue-500 p-2 rounded" @click="i18nHelper.changeLocale('fr-FR')">Locale: French (French)</button>
  </div>

  <p>{{ $t('messages.searchResult') }}</p>
  <p>{{ $t('messages.hello') }}</p>
  <p>{{ $t('messages.bye', { name: 'Magentrix' }) }}</p>
  <p>{{ $t('messages.hi', ['John', 'Smith']) }}</p>

  <p>{{ $n(12.99, 'currency', { currencyDisplay: 'code' }) }}</p>
  <p>{{ $n(0.98, 'decimal') }}</p>
  <p>{{ $n(0.98, 'percent') }}</p>

  <p>{{ $d(new Date(), 'shortDate') }}</p>
  <p>{{ $d(new Date(), 'longDate') }}</p>
  <p>{{ $d(new Date(), 'shortTime') }}</p>
  <p>{{ $d(new Date(), 'longTime') }}</p>
  <p>{{ $d(new Date(), 'shortDateTime') }}</p>
  <p>{{ $d(new Date(), 'longDateTime') }}</p>
  
  <p>{{ $d(new Date('2024-September-09'), { year: 'numeric', month: 'short', day: 'numeric' }) }}</p>
  <p>{{ $d(new Date('2024-September-09'), { year: 'numeric', month: 'long', day: 'numeric' }) }}</p>

  <h3 class="mt-5">SCSS</h3>
  <div class="test-scss-color-green">Hello Iris!</div>
  <div class="test-scss-color-blue">Hello Iris!</div>
  <div class="test-scss-color-green-new">Hello Iris!</div>
</template>

<style lang="scss">
@use '@/shared/assets/scss/custom.scss' as *;

$test-scss-color-blue: lightblue;

.test-scss-color-green-new {
  background-color: $test-scss-color-green;
  color: red;
}

.test-scss-color-blue {
  background-color: $test-scss-color-blue;
}
</style>
