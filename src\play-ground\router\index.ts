const routes = [
    {
        path: '/',
        name: 'test',
        component: () => import('@/play-ground/views/LeoTest.vue'),
        meta: { requiresAuth: false },
    },
    {
        path: "/sam-test",
        name: "sam-test",
        component: () => import('@/play-ground/views/SamTest.vue'),
        meta: { requiresAuth: false },
    },
    {
        path: "/sam-layout",
        name: "sam-layout",
        component: () => import('@/play-ground/views/SamLayout.vue'),
        meta: { requiresAuth: false },
    },
    {
        path: "/nafis-test",
        name: "nafis-test",
        component: () => import('@/play-ground/views/NafisTest.vue'),
        meta: { requiresAuth: false },
    },
    {
        path: "/websocket-test",
        name: "websocket-test",
        component: () => import('@/play-ground/views/WebsocketTestView.vue'),
        meta: { requiresAuth: false },
    },
    {
        path: "/icon-test",
        name: "icon-test",
        component: () => import('@/modules/ide/views/Icons.vue'),
	meta: { requiresAuth: false }
    },
    {
        path: "/setup-test",
        name: "setup-test",
        component: () => import('@/modules/ide/views/Icons.vue'),
        meta: { layout: "Setup" }
    }
]

export default routes