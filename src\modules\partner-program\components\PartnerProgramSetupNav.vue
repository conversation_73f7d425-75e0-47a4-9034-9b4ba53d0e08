<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { TabRouteData } from '@/shared/services/tab-data'
import LanguageHelper from '@/shared/services/language-helper'
import IrisRouteTabs from '@/shared/components/general-controls/IrisRouteTabs.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'

const props = defineProps({
    selectedTab: {
        type:String,
        required: true
    }
})

const languageHelper = new LanguageHelper()
const _items = ref<TabRouteData[]>()
const route = useRoute()

onMounted(() => {
    initialize()
})

// Sets all tab items' selected property to false.
// Finds the tab item matching the selectedTab prop and sets its selected property to true.
function initialize() {
    const disabled = route.query.disabled as string | undefined

    _items.value = []

    if (disabled != "1" ) {
        _items.value.push({ id:"programs", label:languageHelper.getMessage("programs"), path:"/setup/partner-program/programs" } as TabRouteData)
        _items.value.push({ id:"enrollments", label:languageHelper.getMessage("enrollments"), path:"/setup/partner-program/enrollments" } as TabRouteData)
        _items.value.push({ id:"benefits", label:languageHelper.getMessage("benefits"), path:"/setup/partner-program/benefits" } as TabRouteData)
    }
    _items.value.push({ id:"settings", label:languageHelper.getMessage("settings"), path:"/setup/partner-program/settings" } as TabRouteData)

    _items.value?.forEach(f => f.selected = false)
    const selectedItem = _items.value?.find(f => f.id == props.selectedTab)

    if (selectedItem)
        selectedItem.selected = true
}

watch(() => route.query, (newVal) => {
    initialize()
})

</script>
<template>
    <header class="flex justify-between mb-8">
        <div>
            <div class="inline-flex items-center gap-4 mb-2">
                <iris-link :path="'/setup/home'" class="font-bold text-text-color dark:text-text-color-dark">
                    <iris-icon name="arrow-left" />
                </iris-link>  
                <h1 class="text-2xl font-bold">{{ languageHelper.getMessage("partnerProgram") }}</h1>
            </div>
            <!-- Breadcrumb -->
            <div class="text-sm text-text-color-400 dark:text-text-color-400-dark">
                <nav class="flex justify-between" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center text-sm">
                        <li>
                            <iris-icon name="home" class="home-icon" />
                        </li>
                        <span class="mx-2">/</span>
                        <li>
                            {{ languageHelper.getMessage("partnerManagement") }}
                        </li>
                        <span class="mx-2">/</span>
                        <li>
                            {{ languageHelper.getMessage("partnerProgram") }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
        <a class="self-center  hover:text-primary-hover" target="_blank" href="https://help.magentrix.com/doc/index/?Group=partner-program-about">{{ languageHelper.getMessage("helponthispage") }}</a>
    </header>
    <div class="w-full p-3 mb-3 bg-bg-color-100 dark:bg-bg-color-100-dark rounded">
        <iris-route-tabs v-if="_items && _items.length" :items="_items!" mode="Pills"/>
    </div>
</template>