type DebouncedFunction<F extends (...args: any[]) => any> = F & {
  cancel: () => void;
  flush: () => ReturnType<F> | undefined;
  pending: () => boolean;
};

class Debounce<F extends (...args: any[]) => any> {
  private func: F;
  private wait: number;
  private options: {
    leading?: boolean;
    maxWait?: number;
    trailing?: boolean;
  };

  private lastArgs?: Parameters<F>;
  private lastThis?: any;
  private maxWait?: number = 0;
  private result?: ReturnType<F>;
  private timerId?: number;
  private lastCallTime?: number;
  private lastInvokeTime: number = 0;
  private leading: boolean = false;
  private maxing: boolean = false;
  private trailing: boolean = true;
  private useRAF: boolean = false;

  constructor(func: F, wait: number = 0, options: DebounceOptions = {}) {
    this.func = func;
    this.wait = wait;
    this.options = options;

    this.useRAF = !wait && wait !== 0 && typeof window.requestAnimationFrame === 'function';

    if (typeof func !== 'function')
      throw new TypeError('Expected a function');

    this.wait = +wait || 0;

    if (this.isObject(options)) {
      this.leading = !!options.leading;
      this.maxing = 'maxWait' in options;
      this.maxWait = this.maxing ? Math.max(+options.maxWait! || 0, wait) : this.maxWait;
      this.trailing = 'trailing' in options ? !!options.trailing : this.trailing;
    }
  }

  private isObject(value : any) : boolean {
    const type = typeof value;
    return value != null && (type === 'object' || type === 'function')
  }

  private invokeFunc(time: number): ReturnType<F> | undefined {
    const args = this.lastArgs;
    const thisArg = this.lastThis;

    this.lastArgs = this.lastThis = undefined;
    this.lastInvokeTime = time;
    this.result = this.func.apply(thisArg, args!);
    return this.result;
  }

  private startTimer(pendingFunc: () => void, milliseconds: number): any {
    if (this.useRAF) {
        window.cancelAnimationFrame(this.timerId!);
        return window.requestAnimationFrame(pendingFunc);
    }

    return setTimeout(pendingFunc, milliseconds);
  }

  private cancelTimer(id: number | undefined): void {
    if (this.useRAF)
        window.cancelAnimationFrame(id!);
    else
      clearTimeout(id!);
  }

  private leadingEdge(time: number): ReturnType<F> | undefined {
    this.lastInvokeTime = time;
    this.timerId = this.startTimer(this.timerExpired.bind(this), this.wait);
    return this.leading ? this.invokeFunc(time) : this.result;
  }

  private remainingWait(time: number): number {
    const timeSinceLastCall = time - this.lastCallTime!;
    const timeSinceLastInvoke = time - this.lastInvokeTime;
    const timeWaiting = this.wait - timeSinceLastCall;

    return this.maxing ? Math.min(timeWaiting, this.maxWait! - timeSinceLastInvoke) : timeWaiting;
  }

  private shouldInvoke(time: number): boolean {
    const timeSinceLastCall = time - this.lastCallTime!;
    const timeSinceLastInvoke = time - this.lastInvokeTime;

    return (
      this.lastCallTime === undefined ||
      timeSinceLastCall >= this.wait ||
      timeSinceLastCall < 0 ||
      (this.maxing && timeSinceLastInvoke >= this.maxWait!)
    );
  }

  private timerExpired(): ReturnType<F> | undefined {
    const time = Date.now();

    if (this.shouldInvoke(time))
      return this.trailingEdge(time);
    
    this.timerId = this.startTimer(this.timerExpired.bind(this), this.remainingWait(time));
    return undefined;
  }

  private trailingEdge(time: number): ReturnType<F> | undefined {
    this.timerId = undefined;

    if (this.trailing && this.lastArgs)
      return this.invokeFunc(time);
    
    this.lastArgs = this.lastThis = undefined;
    return this.result;
  }

  cancel(): void {
    if (this.timerId !== undefined)
      this.cancelTimer(this.timerId);
    
    this.lastInvokeTime = 0;
    this.lastArgs = this.lastCallTime = this.lastThis = this.timerId = undefined;
  }

  flush(): ReturnType<F> | undefined {
    return this.timerId === undefined ? this.result : this.trailingEdge(Date.now());
  }

  pending(): boolean {
    return this.timerId !== undefined;
  }

  debounced: DebouncedFunction<F> = ((...args: Parameters<F>): ReturnType<F> | undefined => {

    const time = Date.now();
    const isInvoking = this.shouldInvoke(time);

    this.lastArgs = args;
    this.lastThis = this;
    this.lastCallTime = time;

    if (isInvoking) {

      if (this.timerId === undefined)
        return this.leadingEdge(this.lastCallTime);
      
      if (this.maxing) {
        this.timerId = this.startTimer(this.timerExpired.bind(this), this.wait);
        return this.invokeFunc(this.lastCallTime);
      }
    }

    if (this.timerId === undefined)
      this.timerId = this.startTimer(this.timerExpired.bind(this), this.wait);

    return this.result;
  }) as DebouncedFunction<F>;

}

interface DebounceOptions {
  leading?: boolean;
  maxWait?: number;
  trailing?: boolean;
}

export default Debounce;


/*
import isObject from './isObject.js';
import root from './.internal/root.js';

export class debounceHelper {
    debounce(func : Function, wait : number, options : any) : void {
        let lastArgs : any
        let lastThis : any
        let maxWait : number = 0
        let result : any
        let timerId : any
        let lastCallTime : any
        let lastInvokeTime = 0;
        let leading = false;
        let maxing = false;
        let trailing = true;

        // Bypass `requestAnimationFrame` by explicitly setting `wait=0`.
        const useRAF = !wait && wait !== 0 && typeof root.requestAnimationFrame === 'function';

        if (typeof func !== 'function') {
            throw new TypeError('Expected a function');
        }

        wait = +wait || 0;

        if (isObject(options)) {
            leading = !!options.leading;
            maxing = 'maxWait' in options;
            maxWait = maxing ? Math.max(+options.maxWait || 0, wait) : maxWait;
            trailing = 'trailing' in options ? !!options.trailing : trailing;
        }

        function invokeFunc(time : number) {
            const args = lastArgs;
            const thisArg = lastThis;

            lastArgs = lastThis = undefined;
            lastInvokeTime = time;
            result = func.apply(thisArg, args);
            return result;
        }

        function startTimer(pendingFunc : Function, milliseconds : number) {
            if (useRAF) {
                root.cancelAnimationFrame(timerId);
                return root.requestAnimationFrame(pendingFunc);
            }
            // eslint-disable-next-line @typescript-eslint/no-implied-eval
            return setTimeout(pendingFunc, milliseconds);
        }

        function cancelTimer(id : string) {
            if (useRAF) {
                root.cancelAnimationFrame(id);
                return;
            }
            clearTimeout(id);
        }

        function leadingEdge(time : any) {
            // Reset any `maxWait` timer.
            lastInvokeTime = time;
            // Start the timer for the trailing edge.
            timerId = startTimer(timerExpired, wait);
            // Invoke the leading edge.
            return leading ? invokeFunc(time) : result;
        }

        function remainingWait(time : any) {
            const timeSinceLastCall = time - lastCallTime;
            const timeSinceLastInvoke = time - lastInvokeTime;
            const timeWaiting = wait - timeSinceLastCall;

            return maxing ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
        }

        function shouldInvoke(time : any) {
            const timeSinceLastCall = time - lastCallTime;
            const timeSinceLastInvoke = time - lastInvokeTime;

            // Either this is the first call, activity has stopped and we're at the
            // trailing edge, the system time has gone backwards and we're treating
            // it as the trailing edge, or we've hit the `maxWait` limit.
            return (
                lastCallTime === undefined ||
                timeSinceLastCall >= wait ||
                timeSinceLastCall < 0 ||
                (maxing && timeSinceLastInvoke >= maxWait)
            );
        }

        function timerExpired() {
            const time = Date.now();

            if (shouldInvoke(time))
                return trailingEdge(time);

            // Restart the timer.
            timerId = startTimer(timerExpired, remainingWait(time));
            return undefined;
        }

        function trailingEdge(time : any) {
            timerId = undefined;

            // Only invoke if we have `lastArgs` which means `func` has been
            // debounced at least once.
            if (trailing && lastArgs)
                return invokeFunc(time);
            
            lastArgs = lastThis = undefined;
            return result;
        }

        function cancel() {
            if (timerId !== undefined)
                cancelTimer(timerId);
            
            lastInvokeTime = 0;
            lastArgs = lastCallTime = lastThis = timerId = undefined;
        }

        function flush() {
            return timerId === undefined ? result : trailingEdge(Date.now());
        }

        function pending() {
            return timerId !== undefined;
        }

        function debounced(...args) {
            const time = Date.now();
            const isInvoking = shouldInvoke(time);

            lastArgs = args;
            lastThis = this;
            lastCallTime = time;

            if (isInvoking) {

                if (timerId === undefined)
                    return leadingEdge(lastCallTime);
                
                if (maxing) {
                    // Handle invocations in a tight loop.
                    timerId = startTimer(timerExpired, wait);
                    return invokeFunc(lastCallTime);
                }
            }

            if (timerId === undefined)
                timerId = startTimer(timerExpired, wait);
            
            return result;
        }

        debounced.cancel = cancel;
        debounced.flush = flush;
        debounced.pending = pending;
        return debounced;
    }
}*/