<script setup lang="ts">
import { ref, reactive, computed, watchEffect, watch } from 'vue'
import { DropDownItem } from '@/shared/services/dropdown-item'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import IrisCheckbox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import LanguageHelper from '@/shared/services/language-helper'

interface Filter {
  SequenceNo: number
  FieldId: string
  Operator: string
  Value: string
}

interface Field {
  Id: string
  Name: string
  Label: string
  Type: string
  Entries?: Array<{ Label: string; Value: string }>
}

const props = defineProps<{
  fields: Field[]
  existingFilters?: {
    FilterLogic: string
    Filters: { FieldId: string; Operator: string; Value: string; SequenceNo: string }[]
  }
}>()

const languageHelper = new LanguageHelper()
const filterLogic = ref(props.existingFilters?.FilterLogic || '')
const filters = ref<Filter[]>(
  props.existingFilters?.Filters?.length
    ? props.existingFilters.Filters
        .sort((a, b) => parseInt(a.SequenceNo) - parseInt(b.SequenceNo))
        .map((f) => ({
          SequenceNo: parseInt(f.SequenceNo),
          FieldId: f.FieldId,
          Operator: f.Operator,
          Value: f.Value
        }))
    : [{ SequenceNo: 1, FieldId: '', Operator: '', Value: '' }]
)
const showLookupModal = ref(false)
const currentLookupFilter = ref<Filter | null>(null)
const lookupSelectedValue = ref('')

const operatorOptionsMap: Record<string, string[]> = {
  CheckBox: [languageHelper.getMessage('equals'), languageHelper.getMessage('notEquals')],
  PicklistMultiSelect: [
    languageHelper.getMessage('equals'),
    languageHelper.getMessage('notEquals'),
    languageHelper.getMessage('includes'),
    languageHelper.getMessage('excludes')
  ],
  Default: [
    languageHelper.getMessage('equals'), languageHelper.getMessage('notEquals'),
    languageHelper.getMessage('greaterThan'), languageHelper.getMessage('lessThan'),
    languageHelper.getMessage('lessOrEqual'), languageHelper.getMessage('greaterOrEqual'),
    languageHelper.getMessage('contains'), languageHelper.getMessage('notContain'),
    languageHelper.getMessage('startWith'), languageHelper.getMessage('notStartWith')
  ]
}

const fieldItems = computed(() =>
  props.fields.map(f => new DropDownItem(f.Label, f.Id))
)

const predefinedDateOptions = computed((): DropDownItem[] => [
  { label: languageHelper.getMessage('today'), value: 'TODAY', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('yesterday'), value: 'YESTERDAY', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('tomorrow'), value: 'TOMORROW', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('thisMonth'), value: 'THIS MONTH', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('lastMonth'), value: 'LAST MONTH', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('nextMonth'), value: 'NEXT MONTH', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('last30Days'), value: 'LAST 30 DAYS', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('next30Days'), value: 'NEXT 30 DAYS', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('last90Days'), value: 'LAST 90 DAYS', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('next90Days'), value: 'NEXT 90 DAYS', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('thisFiscalYear'), value: 'THIS FISCAL YEAR', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('lastFiscalYear'), value: 'LAST FISCAL YEAR', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('nextFiscalYear'), value: 'NEXT FISCAL YEAR', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('thisYear'), value: 'THIS YEAR', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('lastYear'), value: 'LAST YEAR', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('nextYear'), value: 'NEXT YEAR', color: '', selected: false, helperText: '', removable: false }
])


const operatorItemsMap = reactive<Record<number, DropDownItem[]>>({})

filters.value.forEach(filter => {
  watch(
    () => filter.FieldId,
    () => {
      filter.Operator = ''
      filter.Value = ''
    }
  )
})

watchEffect(() => {
  filters.value.forEach(filter => {
    const field = props.fields.find(f => f.Id === filter.FieldId)
    const ops = operatorOptionsMap[field?.Type || 'Default'] || operatorOptionsMap.Default
    const existing = operatorItemsMap[filter.SequenceNo]?.map(i => i.value) || []
    if (JSON.stringify(existing) !== JSON.stringify(ops)) {
      operatorItemsMap[filter.SequenceNo] = ops.map(op => new DropDownItem(op, op))
    }
  })
})

defineExpose({ getJson, setFilters })

function setFilters(data: typeof props.existingFilters) {
  filterLogic.value = data?.FilterLogic || ''
  filters.value = data?.Filters?.map((f, idx) => ({
    SequenceNo: parseInt(f.SequenceNo),
    FieldId: f.FieldId,
    Operator: f.Operator,
    Value: f.Value
  })) || [{ SequenceNo: 1, FieldId: '', Operator: '', Value: '' }]
}

function getJson() {
  const filterData = filters.value
    .filter(f => f.FieldId && f.Operator)
    .map(f => ({
      FieldId: f.FieldId,
      Operator: f.Operator,
      Value: f.Value,
      SequenceNo: f.SequenceNo.toString()
    }))

  return {
    FilterLogic: filterLogic.value || '',
    Filters: filterData
  }
}

function addFilter() {
  filters.value.push({ SequenceNo: filters.value.length + 1, FieldId: '', Operator: '', Value: '' })
}

function resetFilter(index: number) {
  const f = filters.value[index]
  f.FieldId = ''
  f.Operator = ''
  f.Value = ''
}

function selectedFieldType(filter: Filter) {
  return props.fields.find(f => f.Id === filter.FieldId)?.Type
}

function valueItemsFor(filter: Filter) {
  const field = props.fields.find(f => f.Id === filter.FieldId)
  return field?.Entries?.map(e => new DropDownItem(e.Label, e.Value)) || []
}

function openLookup(filter: Filter) {
  currentLookupFilter.value = filter
  lookupSelectedValue.value = filter.Value
  showLookupModal.value = true
}

function closeLookup() {
  showLookupModal.value = false
  currentLookupFilter.value = null
  lookupSelectedValue.value = ''
}

function applyLookup() {
  if (currentLookupFilter.value) {
    currentLookupFilter.value.Value = lookupSelectedValue.value
  }
  closeLookup()
}

</script>

<template>
    <div class="field-filters">
        <div v-for="(filter, index) in filters" :key="filter.SequenceNo" class="filter-row">
            <span>{{ filter.SequenceNo }} -</span>
            <div class="filter-flex-group">
                <iris-picklist
                :id="`field-${filter.SequenceNo}`"
                :items="fieldItems"
                :value="filter.FieldId"
                mode="edit"
                :showLabel="false"
                @onChange="val => { filter.FieldId = val; filter.Operator = ''; filter.Value = '' }"
                />

                <iris-picklist
                :id="`op-${filter.SequenceNo}`"
                :items="operatorItemsMap[filter.SequenceNo]"
                :value="filter.Operator"
                mode="edit"
                :showLabel="false"
                @onChange="val => filter.Operator = val"
                />
                <IrisMultiPicklist
                    v-if="['Picklist', 'CheckBox', 'PicklistMultiSelect'].includes(selectedFieldType(filter) ?? '')"
                    :id="`value-${filter.SequenceNo}`"
                    :items="valueItemsFor(filter)"
                    :value="filter.Value ? filter.Value.replace(/,/g, ';') : ''"
                    mode="edit"
                    :showLabel="false"
                    @onChange="val => filter.Value = val ? val.replace(/;/g, ',') : ''"
                    />

                <template v-else-if="['Date', 'DateTime'].includes(selectedFieldType(filter) ?? '')">
                    <div class="date-filter-combo" style="display: flex; gap: 10px; align-items: center;">
                        <!-- Date text input -->
                        <IrisTextbox
                            :id="`value-text-${filter.SequenceNo}`"
                            :field="props.fields.find(f => f.Id === filter.FieldId)?.Name || ''"
                            :value="filter.Value"
                            mode="edit"
                            :showLabel="false"
                            @onChange="val => filter.Value = val"
                        />

                        <!-- Lookup Button to open modal -->
                        <button class="btn btn-light btn-sm" @click="openLookup(filter)">
                            <iris-icon name="magnifying-glass" width="1rem" height="1rem" class="cursor-pointer"/>
                        </button>
                    </div>
                </template>
                <IrisTextbox
                    v-else
                    :id="`value-${filter.SequenceNo}`"
                    :field="props.fields.find(f => f.Id === filter.FieldId)?.Name || ''"
                    :value="filter.Value"
                    mode="edit"
                    :showLabel="false"
                    @onChange="val => filter.Value = val"
                />
            </div>

            <button @click="resetFilter(index)" class="btn btn-outline-secondary btn-sm">
                {{ languageHelper.getMessage('reset') }}
            </button>
        </div>
        <div class="mt-2">
        <IrisTextbox
            id="filter-logic"
            field="FilterLogic"
            :value="filterLogic"
            :label="languageHelper.getMessage('filterLogic')"
            mode="edit"
            :showLabel="true"
            :placeHolder="languageHelper.getMessage('filterLogicPlaceHolder')"
            @onChange="val => filterLogic = val"
        />
        </div>

        <button class="btn btn-light mt-3" @click="addFilter">
        {{ languageHelper.getMessage('addFilter') }}
        </button>
    </div>
    <iris-modal
      id="date-lookup-modal"
      :show="showLookupModal"
      @onHide="closeLookup"
      title="Select Date Range"
    >
        <template #content>
            <ul class="list-group">
                <li v-for="option in predefinedDateOptions" :key="option.value" class="list-group-item hoverable p-1">
                <label>
                    <input type="radio" name="dateLookup" :value="option.value" v-model="lookupSelectedValue" />
                    {{ option.label }}
                </label>
                </li>
            </ul>
        </template>
        <template #footer>
            <button class="btn btn-light" @click="closeLookup">Cancel</button>
            <button class="btn btn-primary" @click="applyLookup">Ok</button>
        </template>
    </iris-modal>
</template>

<style scoped>
.field-filters {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
}
.filter-row {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  flex-wrap: wrap;
  margin-bottom: 12px;
}
.filter-flex-group {
  display: flex;
  gap: 10px;
  flex: 1 1 0;
}
.filter-flex-group > * {
  flex: 1 1 0;
  min-width: 0;
  max-width: 220px;
}
</style>