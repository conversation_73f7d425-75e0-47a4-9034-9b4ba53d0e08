import { nextTick } from 'vue'
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { FormControlModeEnum, ComponentSizeEnum } from '@/shared/services/form-control-enums'
import IrisAssetField from '@/shared/components/form-controls/IrisAssetField.vue'

// Update Syncfusion mocks with all required components
vi.mock('@syncfusion/ej2-vue-filemanager', () => ({
  FileManagerComponent: {
    template: '<div class="e-filemanager"></div>'
  },
  Toolbar: vi.fn(),
  NavigationPane: vi.fn(),
  DetailsView: vi.fn(),
  Inject: vi.fn(),
  FileManagerPlugin: vi.fn()
}))

vi.mock('@syncfusion/ej2-vue-popups', () => ({
  DialogComponent: {
    template: '<div class="e-dialog"><slot></slot></div>'
  }
}))

// Mock window.getComputedStyle with more complete implementation
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ({
    getPropertyValue: (prop: string) => '',
    setProperty: vi.fn(),
    removeProperty: vi.fn(),
    getPropertyPriority: vi.fn(),
    item: vi.fn(),
    length: 0
  })
})

describe.skip('IrisAssetField.vue', () => {
  const defaultProps = {
    id: 'test-asset-field',
    value: '',
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('renders correctly with default props', () => {
    const wrapper = mount(IrisAssetField, {
      props: defaultProps,
    })
    expect(wrapper.find('.iris-asset-field').exists()).toBe(true)
    expect(wrapper.find('input').attributes('placeholder')).toBe('Select an asset')
  })

  it('handles different display modes correctly', () => {
    // Test HTML mode
    const htmlWrapper = mount(IrisAssetField, {
      props: {
        ...defaultProps,
        mode: FormControlModeEnum.html,
        value: 'test.pdf'
      }
    })
    expect(htmlWrapper.find('span').text()).toBe('test.pdf')

    // Test text mode
    const textWrapper = mount(IrisAssetField, {
      props: {
        ...defaultProps,
        mode: FormControlModeEnum.text,
        value: 'test.pdf'
      }
    })
    expect(textWrapper.text()).toBe('test.pdf')
  })

  it('emits onChange event when value changes', async () => {
    const wrapper = mount(IrisAssetField, {
      props: defaultProps
    })

    const vm = wrapper.vm as any
    vm.tempAsset = {
      isFile: true,
      relativePath: 'test.pdf'
    }
    vm.onOk()
    await nextTick()

    expect(wrapper.emitted('onChange')).toBeTruthy()
    expect(wrapper.emitted('onChange')![0]).toEqual(['test.pdf'])
  })

  it('applies correct size classes', () => {
    const smallWrapper = mount(IrisAssetField, {
      props: {
        ...defaultProps,
        size: ComponentSizeEnum.small
      }
    })
    expect(smallWrapper.find('input').classes()).toContain('iris-textbox-sm')

    const largeWrapper = mount(IrisAssetField, {
      props: {
        ...defaultProps,
        size: ComponentSizeEnum.large
      }
    })
    expect(largeWrapper.find('input').classes()).toContain('iris-textbox-lg')
  })

  it('shows/hides modal correctly', async () => {
    const wrapper = mount(IrisAssetField, {
      props: defaultProps
    })

    const lookupButton = wrapper.find('button')
    await lookupButton.trigger('click')
    expect(wrapper.vm.showModal).toBe(true)

    await wrapper.vm.onCancel()
    expect(wrapper.vm.showModal).toBe(false)
  })

  it('handles asset selection', async () => {
    const wrapper = mount(IrisAssetField, {
      props: defaultProps
    })

    const testFile = { isFile: true, relativePath: 'test.pdf' }
    await wrapper.vm.handleAssetsSelect([testFile])
    expect(wrapper.vm.tempAsset).toEqual(testFile)
  })
})