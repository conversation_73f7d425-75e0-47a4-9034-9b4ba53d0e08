<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisPage } from '@/shared/composables/iris-page'
import DataAccess from '@/shared/services/data-access'
import LanguageHelper from '@/shared/services/language-helper'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisSection from '@/shared/components/form-controls/IrisSection.vue'
import <PERSON>Field from '@/shared/components/form-controls/IrisField.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'

/* --- Constants and Refs --- */
const route = useRoute()
const base = useIrisPage()
const languageHelper = new LanguageHelper()
const dataAccess = new DataAccess()
const id = route.params.id as string
const benefit = ref<any>(null)
const isLoading = ref(false)
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')


/* --- Lifecycle Hooks --- */
onMounted(async () => {
  base.setPageTitle(languageHelper.getMessage('partnerProgram'))

  if (!id) {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage('serverError')
    return
  }

  isLoading.value = true
  showErrorMessage.value = false

  try {
    const payload = {
      benefitId: id
    }
    
    const response = await dataAccess.execute(
      `/partnerprogrambenefit/getbenefitbyid`,
      payload
    )

    if (response?.benefit) {
      benefit.value = response.benefit
    } else {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage('failFetchData')
    }
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage('failFetchData')
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <partner-program-setup-nav selected-tab="benefits" />
  <div class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm">
    <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
      {{ serverErrorMessage }}
    </iris-alert>
    <h2 class="text-xl font-semibold mt-4 mb-4">{{ languageHelper.getMessage("benefit") }}</h2>
    <div v-if="isLoading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
    <div v-else-if="benefit">
      <iris-section id="benefit-detail" cols="two" :flush="true" :collapsible="false">
          <iris-field id="benefit-name" :value="benefit" field="Name" :mode="FormControlModeEnum.html"/>
          <iris-field id="benefit-benefitType" :value="benefit" field="BenefitType" :mode="FormControlModeEnum.html"/>
          <iris-textbox id="benefit-description" :value="benefit" field="Description" :mode="FormControlModeEnum.html"/>
          <iris-field id="benefit-createdOn" :value="benefit" field="CreatedOn" :mode="FormControlModeEnum.html"/>
          <iris-field id="benefit-ModifiedOn" :value="benefit" field="ModifiedOn" :mode="FormControlModeEnum.html"/>
      </iris-section>
    </div>
  </div>
</template>