<script setup lang="ts">
import { inject, computed, ref, onMounted, onUnmounted } from 'vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

// 1. Props
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  // Unique identifier for this section. Crucial for defaultOpenId and state management.
  id: {
    type: [String, Number],
    required: true,
  },
});

// 2. Inject from parent IrisAccordion
const parentAccordionId = inject<string | undefined>('parentAccordionId', undefined);
const isSectionOpenFunc = inject<(id: string | number) => boolean>('isSectionOpen', () => () => false);
const toggleSectionFunc = inject<(id: string | number) => void>('toggleSection', () => () => {});
const registerSection = inject<(id: string | number) => void>('registerSection', () => {});
const unregisterSection = inject<(id: string | number) => void>('unregisterSection', () => {});

if (!parentAccordionId || typeof isSectionOpenFunc !== 'function' || typeof toggleSectionFunc !== 'function') {
  console.warn('IrisAccordionSection must be used inside an IrisAccordion component that provides necessary context.');
}

const isOpen = computed(() => isSectionOpenFunc(props.id));
const sectionGeneratedIdPrefix = computed(() => {
  if (!parentAccordionId) {
    // Fallback if not in a proper parent, though this shouldn't happen in normal use.
    // Using a random prefix to avoid collisions if multiple "orphan" sections exist.
    return `orphan-accordion-section-${Math.random().toString(36).slice(2, 7)}`;
  }
  return parentAccordionId;
});
const sectionHeadingId = computed(() => `accordion-${sectionGeneratedIdPrefix.value}-heading-${props.id}`);
const sectionBodyId = computed(() => `accordion-${sectionGeneratedIdPrefix.value}-body-${props.id}`);

// Register with parent on mount
onMounted(() => {
  if (registerSection) 
    registerSection(props.id);
});

// Unregister on unmount
onUnmounted(() => {
  if (unregisterSection)
    unregisterSection(props.id);
});

function handleToggle() {
  toggleSectionFunc(props.id);
};

</script>

<template>
  <div class="iris-accordion-section-item flowbite-accordion-item">
    <div :id="sectionHeadingId">
      <button
        type="button"
        @click="handleToggle"
        :aria-expanded="isOpen"
        :aria-controls="sectionBodyId"
        class="w-full flex items-center justify-between py-3 border-b border-border-color px-4"
      >
        <span>
          <slot name="title" :title="props.title" :isOpen="isOpen">
            {{ props.title }}
          </slot>
        </span>
        <IrisIcon
          name="chevron-up"
          :class="[
            isOpen ? 'rotate-180' : '',
            'w-3',
            'h-3',
            'shrink-0',
            'transition-transform',
          ].join(' ')"
        />
      </button>
    </div>
    <div
      :id="sectionBodyId"
      :class="{ hidden: !isOpen }"
      :aria-labelledby="sectionHeadingId"
      class="overflow-hidden"
      role="region"
    >
      <div class="py-3 px-4">
        <slot :isOpen="isOpen"></slot>
      </div>
    </div>
  </div>
</template>