const messages = {
    invalidCurrency: "無効な通貨コード: {currencyCode}",
    endpointRequired: "エンドポイントが必要です",
    queryRequired: "クエリが必要です",
    idRequired: "IDが必要です",
    entityNameRequired: "エンティティ名が必要です",
    dataRequired: "データが必要です",
    invalidTime: "無効な時間形式",
    pingFailed: "Pingに失敗しました！",
    invalidTimeout: "無効なタイムアウト",
    createUserSessionFailed: "ユーザーセッションの作成に失敗しました",
    isRequired: "{label} が必要です",
    notKnownProp: "不明なプロパティです",
    invalidProp: "[{prop}] プロパティの値が無効です",
    invalidLookupField: "無効な検索フィールド [{field}]",
    invalidMaxLength: "[{prop}] プロパティの最大長は {length} 文字です",
    pageTitleRequired: "ページタイトルが必要です",
    pageTitleMaxLength: "ページタイトルの最大長は {maxTitleLength} 文字です",
    invalidItem: "無効な {item}",
    invalidMaxRange: "最大値は最小値より大きくなければなりません。",
    invalidRange: "値は {min} から {max} の間でなければなりません。",
    remove: "削除",
    select: "選択",
    selectAll: "すべて選択",
    close: "閉じる",
    search: "検索",
    showAllResults: '"{search}" のすべての結果を表示',
    loading: "読み込み中...",
    recentRecords: "最近の記録",
    required: "必須",
    duplicateId: "重複したID: {id}",
    results: "結果",
    all: "すべて",
    getWithData: "モデルはGETリクエストでは無視されます。",
    singleSelectedColumnOnly: "ソートには1つの列のみ選択できます。",
    months: "1月,2月,3月,4月,5月,6月,7月,8月,9月,10月,11月,12月",
    year: "年",
    month: "月",
    day: "日",
    selectDate: "日付を選択",
    error: "エラー",
    emailsDisabled: "このポータルではすべてのメールが無効です。メールを有効にするには、ドキュメントを参照してください。",
    noDataToDisplay : "表示するデータがありません。"
}

export default messages