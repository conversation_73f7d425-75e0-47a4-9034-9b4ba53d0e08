<script setup lang="ts">
import { onMounted, ref, computed, getCurrentInstance } from 'vue'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { constants } from '@/shared/services/constants'

const props = defineProps({
    field: String,
    tabIndex: Number,
    value: [Number, Object], // Progress value
    max: { 
        type: Number, 
        default: 100 // Default progress bar total value
    },
    id: {
        type: String,
        required: true
    },
    label: { // Optional label
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.html,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    size: {
        type: String,
        default: "medium",
        validator: (value: string) => {
            return ["small", "medium", "large", "extralarge"].includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    displayValue: {
        type: Boolean,
        default: false
    },
    displayValueMode: {
        type: String,
        default: "inside",
        validator: (value: string) => {
            return ["inside", "outside"].includes(value)
        }
    },
})

// Composables
const base = useIrisFormControl(props)

// Refs
const $el = ref<HTMLElement>()
const _value = ref(base.getValue() || 0)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _max = ref(props.max)
const isVisible = ref(true)

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value)
        initialize(false)
})

// Computed
const progressPercentage = computed(() =>  props.max ? Math.min((_value.value / props.max) * 100, 100) : 0)

const progressAttributes = computed(() => {
    const size = props.size.toLowerCase()

    const classMap = {
        'h-6 text-xl': size === 'extralarge',
        'h-4 text-base': size === 'large',
        'h-2.5 text-sm': size === 'medium',
        'h-1.5 text-xs': size === 'small'
    };

    const styleMap = {
        minHeight:  size === 'extralarge' ? '1.5rem' :
                    size === 'large' ? '1rem' :
                    size === 'medium' ? '.625rem' :
                    '.375rem',
    };

    const textInsideMap = {
        'text-sm': size === 'extralarge',
        'text-xs': size === 'large' ||
                    size === 'medium' || 
                    size === 'small'
    };

    return {
        class: classMap,
        style: styleMap,
        textInside: textInsideMap
    };
})

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()
}

function setPropValues() {
    _value.value = base.getValue() || 0
    _label.value = props.label
    _mode.value = props.mode
    _max.value = props.max || 100
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))

    if (props.max < _value.value)
        throw new Error(base.languageHelper.getMessage('invalidMax'))

    if (_value.value < 0)
        throw new Error(base.languageHelper.getMessage('invalidValue'))
}
</script>

<template>
    <div class="w-full" v-if='isVisible'>
        <template v-if="mode == FormControlModeEnum.html">
            <div class="flex">
                <span v-if="displayValueMode == `outside`" class="iris-label">{{ progressPercentage.toFixed(0) }}%</span>
                <label v-if="_label && showLabel" :class="[progressAttributes.textInside, 'ml-auto' , 'text-text-color-400']">{{ _label }}</label>
            </div>
            <div class="w-full rounded-full bg-bg-color-200 dark:bg-bg-color-200-dark" 
            :class="progressAttributes.class"
            >
                <div class="rounded-full text-center font-medium bg-primary text-primary-text leading-none"
                    :class="progressAttributes.class"
                    :style="{ width: progressPercentage.toFixed(0) + '%'}"
                > 
                    <span v-if="displayValue && displayValueMode == `inside`" :class="progressAttributes.textInside">
                        {{ progressPercentage.toFixed(0) }}%
                    </span>
                </div>
            </div>
        </template>
        <template v-else-if="mode == FormControlModeEnum.text">
            {{ progressPercentage.toFixed(0) }}%
        </template>
    </div>
</template>