/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx,vue}",
    "./node_modules/flowbite/**/*.js"
  ],
  theme: {
    extend: {
      colors: {
        'text-color': 'var(--mag-page-text-color)',
        'page-base': 'var(--mag-base)',
        'text-color-100': 'var(--mag-base-100)',
        'text-color-200': 'var(--mag-base-200)',
        'text-color-300': 'var(--mag-base-300)',
        'text-color-400': 'var(--mag-base-400)',
        'text-color-500': 'var(--mag-base-500)',

        'bg-color': 'var(--mag-page-bg-color)',
        'bg-color-100' : 'var(--mag-page-bg-100)',
        'bg-color-200' : 'var(--mag-page-bg-200)',
        'bg-color-300' : 'var(--mag-page-bg-300)',
        'border-color': 'var(--mag-element-border-color)',
        'border-muted': 'var(--mag-element-border-muted-color)',

        primary: 'var(--mag-primary-color)',
        'primary-hover': 'var(--mag-primary-hover-color)',
        'primary-text': 'var(--mag-primary-text-color)',
        'border-focus': 'var(--mag-element-border-focus-color)',

        secondary: 'var(--mag-secondary-color)',
        'secondary-text': 'var(--mag-secondary-text-color)',
        'secondary-border': 'var(--mag-secondary-border-color)',
        'secondary-hover': 'var(--mag-secondary-hover-color)',

        'nav-bg': 'var(--mag-nav-bg-color)',
        'nav-text': 'var(--mag-nav-text-color)',
        'nav-bg-hover': 'var(--mag-nav-hover-bg-color)',
        'nav-bg-active': 'var(--mag-nav-active-bg-color)',

        'header-bg': 'var(--mag-header-bg-color)',
        'header-bg-100': 'var(--mag-header-bg-300)',
        'header-bg-200': 'var(--mag-header-bg-200)',
        'header-text': 'var(--mag-header-text-color)',

        'text-color-dark': 'var(--mag-page-text-color)',
        'page-base-dark': 'var(--mag-base)',
        'text-color-100-dark': 'var(--mag-base-100)',
        'text-color-200-dark': 'var(--mag-base-200)',
        'text-color-300-dark': 'var(--mag-base-300)',
        'text-color-400-dark': 'var(--mag-base-400)',
        'text-color-500-dark': 'var(--mag-base-500)',

        'bg-color-dark': 'var(--mag-page-bg-color)',
        'bg-color-100-dark' : 'var(--mag-page-bg-100)',
        'bg-color-200-dark' : 'var(--mag-page-bg-200)',
        'bg-color-300-dark' : 'var(--mag-page-bg-300)',
        'border-color-dark': 'var(--mag-element-border-color)',
        'border-muted-dark': 'var(--mag-element-border-muted-color)',

        'nav-bg-dark': 'var(--mag-nav-bg-color)',
        'nav-text-dark': 'var(--mag-nav-text-color)',
        'nav-bg-hover-dark': 'var(--mag-nav-hover-bg-color)',
        'nav-bg-active-dark': 'var(--mag-nav-active-bg-color)',

        'header-bg-dark': 'var(--mag-header-bg-color)',
        'header-text-dark': 'var(--mag-header-text-color)',
      },
      placeholderColor: {
        'header-text-color': 'var(--header-text-color)',
        'header-text-dark': 'var(--header-text-dark)',
        'text-color-400': 'var(--text-color-400)',
        'text-color-400-dark': 'var(--text-color-400-dark)',
      },
      backgroundImage: {
        'nav-primary-gradient' : 'linear-gradient(90deg,var(--mag-primary-color) 5%, var(--mag-nav-bg-color) 100%)',
      },
      fontFamily: {
        sans: ['var(--mag-primary-font)'],
        serif: ['var(--mag-heading-font)']
      },
      borderColor: {
        DEFAULT: 'var(--mag-element-border-color)',
        'hover': 'var(--mag-element-border-focus-color)',
        'focus': 'var(--mag-element-border-focus-color)',
      },
      borderRadius: { 
        DEFAULT: 'var(--mag-border-radius)', // Default border radius
        md: 'var(--mag-border-radius)',     // Override the rounded-md class
        lg: 'var(--mag-border-radius)',     // Override the rounded-lg class
      },
      boxShadow: {
        DEFAULT: 'var(--mag-box-shadow)',
        md: 'var(--mag-box-shadow)',
        lg: 'var(--mag-box-shadow)',
      },
      maxWidth: {
        page: 'var(--mag-page-width)',
      }
    },
  },
  plugins: [
    function({ addBase, theme }) {
      addBase({
        '*::before': {
          boxSizing: 'border-box',
          borderWidth: '0',
          borderStyle: 'solid',
          borderColor: theme('colors.border-color'),
        },
        '*::after': {
          boxSizing: 'border-box',
          borderWidth: '0',
          borderStyle: 'solid',
          borderColor: theme('colors.border-color'),
        },
      });
    },
    require('flowbite/plugin')
  ],
}

