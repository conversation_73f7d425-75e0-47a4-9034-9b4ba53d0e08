import { provide } from 'vue'
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import IrisAccordionSection from '@/shared/components/general-controls/IrisAccordionSection.vue'

// Mock the IrisIcon component
const MockIrisIcon = {
  name: 'IrisIcon',
  props: ['name', 'class'],
  template: '<span :class="$props.class" data-testid="iris-icon" :data-icon="name"></span>'
};

// Helper to create a wrapper component that provides accordion context
const createAccordionProvider = (options: {
  parentAccordionId?: string;
  isSectionOpen?: (id: string | number) => boolean;
  toggleSection?: (id: string | number) => void;
  registerSection?: (id: string | number) => void;
  unregisterSection?: (id: string | number) => void;
} = {}) => {
  return {
    components: {
      IrisAccordionSection,
      IrisIcon: MockIrisIcon
    },
    setup() {
      provide('parentAccordionId', options.parentAccordionId || 'test-accordion-123');
      provide('isSectionOpen', options.isSectionOpen || (() => false));
      provide('toggleSection', options.toggleSection || (() => {}));
      provide('registerSection', options.registerSection || (() => {}));
      provide('unregisterSection', options.unregisterSection || (() => {}));
    },
    template: `
      <IrisAccordionSection 
        :id="sectionId" 
        :title="title"
        v-bind="$attrs"
      >
        <template #title="{ title, isOpen }" v-if="customTitle">
          <span data-testid="custom-title">{{ title }} - {{ isOpen ? 'Open' : 'Closed' }}</span>
        </template>
        <div data-testid="section-content">Test content</div>
      </IrisAccordionSection>
    `,
    props: ['sectionId', 'title', 'customTitle']
  };
};

describe.skip('IrisAccordionSection', () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe.skip('Basic Props and Rendering', () => {
    it('should render with required props', () => {
      const registerSection = vi.fn();
      const ProviderComponent = createAccordionProvider({ registerSection });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(wrapper.find('button').text()).toContain('Test Section');
      expect(wrapper.find('[data-testid="section-content"]').text()).toBe('Test content');
    });

    it('should accept string or number id', () => {
      const registerSection = vi.fn();
      const ProviderComponent = createAccordionProvider({ registerSection });
      
      // Test with string ID
      const wrapper1 = mount(ProviderComponent, {
        props: {
          sectionId: 'string-id',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      // Test with number ID
      const wrapper2 = mount(ProviderComponent, {
        props: {
          sectionId: 123,
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(registerSection).toHaveBeenCalledWith('string-id');
      expect(registerSection).toHaveBeenCalledWith(123);
    });
  });

  describe.skip('Parent Accordion Integration', () => {
    it('should register with parent on mount', () => {
      const registerSection = vi.fn();
      const ProviderComponent = createAccordionProvider({ registerSection });
      
      mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(registerSection).toHaveBeenCalledWith('section-1');
    });

    it('should unregister with parent on unmount', () => {
      const unregisterSection = vi.fn();
      const ProviderComponent = createAccordionProvider({ unregisterSection });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      wrapper.unmount();
      expect(unregisterSection).toHaveBeenCalledWith('section-1');
    });

    it('should warn when used without proper parent context', () => {
      // Mount without accordion provider
      mount(IrisAccordionSection, {
        props: {
          id: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'IrisAccordionSection must be used inside an IrisAccordion component that provides necessary context.'
      );
    });
  });

  describe.skip('Open/Close State Management', () => {
    it('should show as closed by default', () => {
      const isSectionOpen = vi.fn().mockReturnValue(false);
      const ProviderComponent = createAccordionProvider({ isSectionOpen });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      const button = wrapper.find('button');
      const contentDiv = wrapper.find('[role="region"]');

      expect(button.attributes('aria-expanded')).toBe('false');
      expect(contentDiv.classes()).toContain('hidden');
    });

    it('should show as open when parent indicates it is open', () => {
      const isSectionOpen = vi.fn().mockReturnValue(true);
      const ProviderComponent = createAccordionProvider({ isSectionOpen });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      const button = wrapper.find('button');
      const contentDiv = wrapper.find('[role="region"]');

      expect(button.attributes('aria-expanded')).toBe('true');
      expect(contentDiv.classes()).not.toContain('hidden');
    });

    it('should call toggleSection when button is clicked', async () => {
      const toggleSection = vi.fn();
      const ProviderComponent = createAccordionProvider({ toggleSection });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      await wrapper.find('button').trigger('click');
      expect(toggleSection).toHaveBeenCalledWith('section-1');
    });
  });

  describe.skip('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      const ProviderComponent = createAccordionProvider({
        parentAccordionId: 'test-accordion-123'
      });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      const button = wrapper.find('button');
      const heading = wrapper.find('[id^="accordion-test-accordion-123-heading-"]');
      const content = wrapper.find('[role="region"]');

      expect(button.attributes('aria-expanded')).toBeDefined();
      expect(button.attributes('aria-controls')).toBe(content.attributes('id'));
      expect(content.attributes('aria-labelledby')).toBe(heading.attributes('id'));
      expect(content.attributes('role')).toBe('region');
    });

    it('should generate proper IDs based on parent accordion ID', () => {
      const ProviderComponent = createAccordionProvider({
        parentAccordionId: 'my-custom-accordion'
      });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-42',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      const heading = wrapper.find('[id^="accordion-my-custom-accordion-heading-"]');
      const content = wrapper.find('[id^="accordion-my-custom-accordion-body-"]');

      expect(heading.attributes('id')).toBe('accordion-my-custom-accordion-heading-section-42');
      expect(content.attributes('id')).toBe('accordion-my-custom-accordion-body-section-42');
    });

  });

  describe.skip('Slots', () => {
    it('should render default title slot', () => {
      const ProviderComponent = createAccordionProvider();
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Default Title'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(wrapper.find('button span').text()).toBe('Default Title');
    });

    it('should render custom title slot with props', () => {
      const isSectionOpen = vi.fn().mockReturnValue(true);
      const ProviderComponent = createAccordionProvider({ isSectionOpen });
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Custom Title',
          customTitle: true
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(wrapper.find('[data-testid="custom-title"]').text()).toBe('Custom Title - Open');
    });

    it('should render default content slot with isOpen prop', () => {
      const ProviderComponent = createAccordionProvider();
      
      const wrapper = mount(ProviderComponent, {
        props: {
          sectionId: 'section-1',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      expect(wrapper.find('[data-testid="section-content"]').exists()).toBe(true);
    });
  });

  describe.skip('Edge Cases', () => {
    it('should work with different ID types consistently', () => {
      const isSectionOpen = vi.fn();
      const toggleSection = vi.fn();
      
      const ProviderComponent = createAccordionProvider({ 
        isSectionOpen, 
        toggleSection 
      });
      
      // Test with string ID
      const wrapper1 = mount(ProviderComponent, {
        props: {
          sectionId: 'string-id',
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      wrapper1.find('button').trigger('click');
      expect(isSectionOpen).toHaveBeenCalledWith('string-id');
      expect(toggleSection).toHaveBeenCalledWith('string-id');

      // Test with number ID
      const wrapper2 = mount(ProviderComponent, {
        props: {
          sectionId: 42,
          title: 'Test Section'
        },
        global: {
          components: {
            IrisIcon: MockIrisIcon
          }
        }
      });

      wrapper2.find('button').trigger('click');
      expect(isSectionOpen).toHaveBeenCalledWith(42);
      expect(toggleSection).toHaveBeenCalledWith(42);
    });
  });
});