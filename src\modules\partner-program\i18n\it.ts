const messages = {
  partnerProgram: "Programma Partner",
  partnerManagement: "Gestione Partner",
  helponthispage: "<PERSON><PERSON> in questa pagina",
  search: "Cerca",
  programs: "Programmi",
  account: "Account",
  partnerPrograms: "Programmi Partner",
  serverError: "Qualcosa è andato storto!",
  loading: "Caricamento...",
  yes: "Sì",
  no: "No",
  benefits: "Vantaggi",
  programBenefits: "Vantaggi del Programma",
  newProgram: "Nuovo Programma",
  programTiers: "Livelli del Programma",
  dragDropMessage: "Trascina e rilascia i livelli per riordinarli",
  tier: "Livello",
  remove: "Rimuovi",
  addTier: "Aggiung<PERSON> Livello",
  editTier: "Modifica Livello",
  tierBadge: "Badge del Livello",
  tierDescription: "Descrizione del Livello",
  tierName: "Nome del Livello",
  deleteTier: "Elimina Livello",
  noRecords: "<PERSON><PERSON>un dato da visualizzare.",
  newBenefit: "<PERSON>uovo Vantaggio",
  cancel: "<PERSON><PERSON><PERSON>",
  close: "<PERSON><PERSON>",
  addBenefit: "Aggiungi Vantaggio",
  saving: "Salvataggio in corso...",
  existingBenefitName: "Esiste già un vantaggio con questo nome. Scegli un nome diverso.",
  highestTierReached: "Sei nel livello più alto!",
  failFetchData: "Errore: impossibile recuperare i dati. Contatta l'amministratore",
  failInitialize: "Errore nell'inizializzazione dei componenti",
  failSave:"Errore nel salvataggio del record. Riprova o contatta l'amministratore.",
  benefitDuplicateNameError: "Esiste già un vantaggio con questo nome. Scegli un nome diverso.",
  save: "Salva",
  deleteBenefit: "Elimina Vantaggio",
  deleteBenefitMessage: "Sei sicuro di voler eliminare questo vantaggio e tutte le assegnazioni?",
  delete: "Elimina",
  failDelete:"Errore nell'eliminazione del record. Riprova o contatta l'amministratore.",
  edit: "Modifica",
  first: "Primo",
  last: "Ultimo",
  next: "Successivo",
  previous: "Precedente",
  editBenefit: "Modifica Vantaggio",
  noProgramsAvailable: "Nessun programma disponibile.",
  enrollments: "Iscrizioni",
  partnerEnrollments: "Iscrizioni Partner",
  newEnrollment: "Nuova Iscrizione",
  allPrograms: "Tutti i Programmi",
  noTiers: "Nessun livello disponibile per il programma selezionato. Seleziona un altro programma o contatta l'amministratore.",
  editEnrollment: "Modifica Iscrizione",
  addEnrollment: "Aggiungi Iscrizione",
  deleteEnrollmentMessage: "Sei sicuro di voler eliminare questa iscrizione e tutte le informazioni?",
  deleteEnrollment: "Elimina Iscrizione",
  duplicateEnrollment: "Questo partner ha già un'iscrizione attiva in questo programma.",
  noAccountsAvailable: "Nessun account disponibile.",
  deleteProgramMessage: "Stai per eliminare questo programma e tutti i suoi dati, inclusi livelli, iscrizioni e criteri. Questa azione non può essere annullata.",
  deleteProgram: "Elimina Programma Partner",
  setting: "Impostazione",
  settings: "Impostazioni",
  partnerProgramStatus: "Stato del Programma Partner",
  enablePartnerProgram: "Abilita Programma Partner",
  partnerProgramIsDisabled: "Il Programma Partner è disabilitato",
  partnerProgramIsActive: "Il Programma Partner è attivo",
  confirmationDisablingPartnerProgram: "Sei sicuro di voler disabilitare il Programma Partner?",
  confirmationDisablingPartnerProgramMessage: "La visibilità dei livelli e dei vantaggi sarà nascosta.",
  ok: "OK",
  settingsSaved: "Impostazioni salvate con successo!",
  back: "Indietro",
  benefit: "Vantaggio del Programma Partner",
  benefitTypeError: "Il tipo di vantaggio è obbligatorio.",
  benefitNameRequired: "Il nome del vantaggio è obbligatorio.",
  enrollmentDateMustBeBeforeExpiration: "La data di iscrizione deve essere precedente alla data di scadenza.",
  editProgram: "Modifica Programma",
  addProgram: "Aggiungi Programma",
  programNameRequired: "Il nome del programma è obbligatorio.",
  tierNameRequired: "Il nome del livello è obbligatorio.",
  tierInexNameRequired: "Livello {idx}: il nome è obbligatorio.",
  duplicateTierName: "Nome del livello duplicato",
  unsavedChangesConfimation: "Hai modifiche non salvate. Sei sicuro di voler chiudere?",
  minTierError: "Devi configurare almeno un livello.",
  maxTierError: "Puoi configurare al massimo sei livelli.",
  duplicateIndexTierName: "Livello {idx}: nome duplicato {tierName}.",
  missingProgramId: "Programma non valido",
  program: "Programma",
  newTier: "Nuovo Livello",
  clone: "Clona",
  configure: "Configura",
  noTiersDefined: "Nessun livello definito.",
  criteria: "Criteri del Programma",
  noCriteriaDefined: "Nessun criterio definito.",
  addCriterion: "Aggiungi Criterio",
  noBenefitDefined: "Nessun vantaggio definito.",
  atLeastOneTierRequired: "Inserisci almeno un livello per il programma",
  confirm: "Conferma",
  noMoreAvailableBenefits: "Nessun altro vantaggio disponibile da aggiungere.",
  missingProgramIdOrPartnerAccountId: "Informazioni mancanti. Contatta l'amministrazione.",
  noProgramEnrollments: "Non sei iscritto a un programma partner. Contatta l'amministratore.",
  noPartnerAccount: "Devi essere un partner per visualizzare questa pagina.",
  certifications: "Certificazioni",
  numberofOpportunities: "Numero di Opportunità",
  opportunityRevenue: "Entrate da Opportunità",
  numberofDealRegistrations: "Numero di Registrazioni di Offerte",
  custom: "Personalizzato",
  configureCriterion: "Configura Criterio",
  createCriterion: "Crea Criterio",
  create: "Crea",
  criteriaDuplicateNameError: "Esiste già un criterio con questo nome. Scegli un nome diverso.",
  quantity: "Quantità",
  revenue: "Entrate",
  criterionNameRequiredError: "Il nome del criterio è obbligatorio.",
  criterionTypeRequiredError: "Il tipo di criterio è obbligatorio.",
  trainingCourseRequiredError: "È richiesto un corso di formazione per le certificazioni.",
  rollupFieldRequiredError: "È richiesto un campo di aggregazione per le entrate.",
  attainmentDateFieldRequired: "È richiesto il campo della data di conseguimento",
  unitofMeasureRequired: "È richiesta l'unità di misura.",
  editCriterion: "Modifica Criterio",
  missingFields: "Mancano alcune informazioni",
  trainingCourse: "Corso di Formazione",
  name: "Nome",
  type: "Tipo",
  filterLogic: "Logica del Filtro (opzionale)",
  filterLogicPlaceHolder: "es. (1 e 2) o 3",
  reset: "Reimposta",
  addFilter: "Aggiungi Filtro",
  equals: "uguale a",
  notEquals: "diverso da",
  includes: "include",
  excludes: "esclude",
  greaterThan: "maggiore di",
  lessThan: "minore di",
  lessOrEqual: "minore o uguale a",
  greaterOrEqual: "maggiore o uguale a",
  contains:"contiene",
  notContain: "non contiene",
  startWith: "inizia con",
  notStartWith: "non inizia con",
  partnerAccount: "Account Partner",
  enrollmentDate: "Data di Iscrizione",
  expirationDate: "Data di Scadenza",
}

export default messages