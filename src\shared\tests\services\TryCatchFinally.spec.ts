import { describe, it, expect } from "vitest"
import Try from "@/shared/services/try"
import DatabaseError from "@/shared/services/database-error"

describe("Try Catch Finally", async () => {
    it("Try Catch Finally (No Error)", async () => {
        let result = ''

        Try(() => {
            result += 'Try'
          })
          .Catch(Error, () => result += 'Error')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryFinally')
    })

    it("Try Catch Finally (Regular Error - Single Catch)", async () => {
        let result = ''

        Try(() => {
            result += 'TryStart'
            throw new Error('Regular Error')
            result += 'TryEnd'
          })
          .Catch(Error, () => result += 'Error')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryStartErrorFinally')
    })

    it("Try Catch Finally (Regular Error - Multi Catch)", async () => {
        let result = ''

        Try(() => {
            result += 'TryStart'
            throw new Error('Regular Error')
            result += 'TryEnd'
          })
          .Catch(DatabaseError, () => result += 'DatabaseError')
          .Catch(Error, () => result += 'Error')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryStartErrorFinally')
    })

    it("Try Catch Finally (Database Error - Multi Catch)", async () => {
        let result = ''

        Try(() => {
            result += 'TryStart'
            throw new DatabaseError('DatabaseError', [])
            result += 'TryEnd'
          })
          .Catch(DatabaseError, () => result += 'DatabaseError')
          .Catch(Error, () => result += 'Error')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryStartDatabaseErrorFinally')
    })

    it("Try Catch Finally (Database Error - Base Catch)", async () => {
        let result = ''

        Try(() => {
            result += 'TryStart'
            throw new DatabaseError('DatabaseError', [])
            result += 'TryEnd'
          })
          .Catch(Error, () => result += 'Error')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryStartErrorFinally')
    })

    it("Try Catch Finally (Wrong order multi catch)", async () => {
        let result = ''

        Try(() => {
            result += 'TryStart'
            throw new DatabaseError('DatabaseError', [])
            result += 'TryEnd'
          })
          .Catch(Error, () => result += 'Error')
          .Catch(DatabaseError, () => result += 'DatabaseError')
          .Finally(() => result += 'Finally')
        
        expect(result).toEqual('TryStartErrorFinally')
    })

    // it("Try Catch Finally (Database Error - No Proper Catch)", async () => {
    //     let result = ''

    //     const badFunc = () => {
    //         Try(() => {
    //             result += 'TryStart'
    //             throw new Error('Regular Error')
    //             result += 'TryEnd'
    //           })
    //           .Catch(DatabaseError, () => result += 'DatabaseError')
    //           .Finally(() => result += 'Finally')
    //     }
        
    //     expect(badFunc).toThrow('Regular Error')
    // })
})