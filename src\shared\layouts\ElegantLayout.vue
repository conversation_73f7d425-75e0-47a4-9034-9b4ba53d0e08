<script setup lang="ts">
    import { onMounted } from 'vue'
    import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
    import IrisUserDropDown from '@/shared/components/theming-controls/IrisUserDropDown.vue'
    import IrisNavbarMenu from '@/shared/components/theming-controls/IrisNavbarMenu.vue'
    import IrisAppLauncher from '@/shared/components/theming-controls/IrisAppLauncher.vue'
    import IrisLanguagePicker from '@/shared/components/theming-controls/IrisLanguagePicker.vue'
    import IrisSocialLinks from '@/shared/components/theming-controls/IrisSocialLinks.vue'
    import IrisFooterLinks from '@/shared/components/theming-controls/IrisFooterLinks.vue'
    import IrisGlobalSearchBox from '@/modules/global-search/components/IrisGlobalSearchBox.vue'
    import IrisInstanceType from '@/shared/components/theming-controls/IrisInstanceType.vue'
    import IrisCustomFooter from '@/shared/components/theming-controls/IrisCustomFooter.vue'
    import IrisLogo from '@/shared/components/theming-controls/IrisLogo.vue'

    const props = defineProps({
        showMenu: {
            type: Boolean,
            default: true
        },
        isFloatingMenu: {
            type: Boolean,
            default: false
        }
    })

    const systemInfo = useIrisSystemInfoStore().getData()

    onMounted(() => {
        document.body.classList.add('bg-bg-color-100')
        document.body.classList.add('dark:bg-bg-color-100-dark')
    })
</script>

<template>
    <div class="sitePage bg-bg-color-100 p-0 md:p-4">
        <div class="max-w-page mx-auto border border-border-color rounded overflow-hidden">
            <div class='siteHeaderContainer std-theme-siteHeaderContainer'>
                <div class="site-page-header bg-nav-bg dark:bg-nav-bg-dark">
                    <div class="bg-header-bg dark:bg-header-bg-dark p-4" v-if="systemInfo.userInfo">
                        <div class="flex justify-end" v-if="systemInfo.company.instance.instanceName">
                            <IrisInstanceType :value="systemInfo.company.instance" class=" mb-3"/>
                        </div>
                        <div class="flex items-center justify-between flex-wrap">
                            <div class="siteHeader-logo h-12 max-h-12 max-w-32 sm:max-w-52 overflow-hidden">
                                <IrisLogo 
                                    :url="systemInfo.company.theme.logoUrl" 
                                    :logo-click-url="systemInfo.company.logoClickUrl" 
                                    class="site-logo-img h-full flex items-center justify-center"/>
                            </div>
                            <div class="siteHeader-items flex items-center gap-4">
                                <div v-if="systemInfo.company.system.globalSearch">
                                    <IrisGlobalSearchBox class="text-header-text dark:text-header-text-dark"/>
                                </div>
                                <IrisAppLauncher 
                                    :value="systemInfo.apps" 
                                    v-if="showMenu" 
                                    class="p-0.5 text-header-text dark:text-header-text-dark
                                    opacity-80 hover:opacity-100
                                    rounded focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark"/>
                                <IrisLanguagePicker 
                                    class="p-0.5 text-header-text dark:text-header-text-dark
                                    opacity-80 hover:opacity-100 rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"
                                    :value="systemInfo.company.langauges" 
                                    :showLabel=false
                                    :showFlags="systemInfo.company.system.languageFlagsEnabled" 
                                    :selected="systemInfo.userInfo.user.lang"/>
                                <IrisUserDropDown 
                                    :value="systemInfo.userInfo" 
                                    :showUserName="false" 
                                    class="p-0.5 text-header-text dark:text-header-text-dark opacity-80 hover:opacity-100 
                                    rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"/>
                            </div>
                        </div>
                    </div>
                    <div class="site-main-nav block w-full border-b border-border-muted dark:border-border-muted-dark" v-if="systemInfo.menuItems && showMenu">
                        <IrisNavbarMenu :hasBg=true :value="systemInfo.menuItems"/>
                    </div>
                </div>
            </div>
            <div class="siteBody bg-bg-color dark:bg-bg-color-dark">
                <div class="p-4 min-h-[550px]">
                    <RouterView />
                </div>
            </div>
            <div class="py-10 border-t border-border-color dark:border-border-color-dark">
                <div class="footer p-4">
                    <IrisCustomFooter :value="systemInfo.company.customFooterCells" :alignment="systemInfo.company.customFooterCellsAlignment"/>
                    <div class="flex flex-col items-center gap-10 flex-wrap">
                        <div>
                            <IrisSocialLinks :value="systemInfo.company.socialLinks" class="inline-flex items-center p-4 text-primary bg-bg-color-200 dark:bg-bg-color-200-hover-dark opacity-80 hover:opacity-100 rounded-full" />
                        </div>
                        <div>
                            <IrisFooterLinks :value="systemInfo.company.footerData" class="text-primary hover:text-primary-hover"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>