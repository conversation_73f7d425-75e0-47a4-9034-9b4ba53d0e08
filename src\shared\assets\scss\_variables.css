:root {
  --mag-nav-text-color: #ffffff;
  --mag-page-text-color: #333333;
  --mag-nav-bg-color: #4e285c;
  --mag-page-bg-color: #f5f5f5;
  --mag-primary-color: #b00382;
  --mag-secondary-color: #0b07f2;
  --mag-header-bg-color: #222222;
  --mag-primary-font: "EkMukta";
  --mag-heading-font: "Lato";
  --mag-page-width: 1320px;
  --mag-box-shadow: 0 .375rem .75rem transparent;
  --mag-border-radius: 0.5rem;
  --mag-base-200: #333333;
  --mag-base-300: #555555;
  --mag-base-400: #777777;
  --mag-base-500: #999999;
  --mag-base-100: #262626;
  --mag-base: #1a1a1a;
  --mag-page-bg-100: white;
  --mag-page-bg-200: #ebebeb;
  --mag-page-bg-300: #e1e1e1;
  --mag-element-border-muted-color: #e6e6e6;
  --mag-element-border-color: #d6d6d6;
  /* $lighten-color: lighten-shade($base-color, 70%);
  $darken-color: darken-shade($base-color, 70%);

  $lighten-contrast-ratio: calculate-contrast-ratio($base-color, $lighten-color);
  $darken-contrast-ratio: calculate-contrast-ratio($base-color, $darken-color); */
  --mag-primary-text-color: black;
  /* @if $color-name == "mag-primary-text-color" {
    @debug $blackness-value;
    @debug $lighten-color;
    @debug $darken-color;
    @debug $lighten-contrast-ratio;
    @debug $darken-contrast-ratio;
    @debug $text-color;
  } */
  --mag-primary-hover-color: #65024a;
  --mag-element-border-focus-color: #fb05b9;
  /* $lighten-color: lighten-shade($base-color, 70%);
  $darken-color: darken-shade($base-color, 70%);

  $lighten-contrast-ratio: calculate-contrast-ratio($base-color, $lighten-color);
  $darken-contrast-ratio: calculate-contrast-ratio($base-color, $darken-color); */
  --mag-secondary-text-color: black;
  /* @if $color-name == "mag-primary-text-color" {
    @debug $blackness-value;
    @debug $lighten-color;
    @debug $darken-color;
    @debug $lighten-contrast-ratio;
    @debug $darken-contrast-ratio;
    @debug $text-color;
  } */
  --mag-secondary-hover-color: #0805a8;
  --mag-secondary-border-color: #4f4cfa;
  /* $lighten-color: lighten-shade($base-color, 70%);
  $darken-color: darken-shade($base-color, 70%);

  $lighten-contrast-ratio: calculate-contrast-ratio($base-color, $lighten-color);
  $darken-contrast-ratio: calculate-contrast-ratio($base-color, $darken-color); */
  --mag-header-text-color: #d5d5d5;
  /* @if $color-name == "mag-primary-text-color" {
    @debug $blackness-value;
    @debug $lighten-color;
    @debug $darken-color;
    @debug $lighten-contrast-ratio;
    @debug $darken-contrast-ratio;
    @debug $text-color;
  } */
  --mag-header-bg-200: #2c2c2c;
  --mag-header-bg-300: #363636;
  --mag-nav-hover-bg-color: #7b3f91;
  --mag-nav-active-bg-color: #211127;
}
