import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { ModelError } from '@/shared/services/model-error'
import type { DatabaseErrorType } from '@/shared/services/types'

export const useIrisFormControlStore = defineStore('irisFormControlStore', () => {
  const controls = ref<{ id: String, formId: String | null, errors: Function, addDbErrors: Function, validator: Function }[]>([])

  function add(id: string, formId: string | null, errors: Function, addDbErrors: Function, validator: Function = () => {}): void {
    const item = controls.value.find(x => x.id.toString() === id)

    if (!item)
      controls.value.push({ id, formId, errors, addDbErrors, validator })
  }

  function addDbErrors(formId: string, dbErrors: DatabaseErrorType[]) {
    const items = controls.value.filter(x => x.formId === formId)

    items.forEach(x => x.addDbErrors(dbErrors))
  }

  function remove(id: string): void {
    controls.value = controls.value.filter(x => x.id != id)
  }

  function getErrors(formId: string | null = null): ModelError[] {
    const filtered = formId ? controls.value.filter(x => x.formId == formId) : controls.value
    var result: ModelError[] = []

    filtered.forEach(x => {
      result = result.concat(x.errors())
    })

    return result
  }

  function hasErrors(formId: string): boolean {
    const result = controls.value.filter(x => x.formId == formId).length > 0
    
    return result
  }

  function resetErrors(formId: string | null = null) : void {
     if (formId)
      controls.value = controls.value.filter(x => x.formId != formId)
     else
      controls.value = []
  }

  function isFormValid(formId: string): boolean {
    let result = true
    const filtered = controls.value.filter(x => x.formId == formId)

    filtered.forEach(component => result = component.validator() && result)

    return result
  }

  return { controls, add, addDbErrors, remove, getErrors, hasErrors, resetErrors, isFormValid }
})
