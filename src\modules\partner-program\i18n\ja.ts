const messages = {
  partnerProgram: "パートナープログラム",
  partnerManagement: "パートナー管理",
  helponthispage: "このページのヘルプ",
  search: "検索",
  programs: "プログラム",
  account: "アカウント",
  partnerPrograms: "パートナープログラム",
  serverError: "問題が発生しました！",
  loading: "読み込み中...",
  yes: "はい",
  no: "いいえ",
  benefits: "特典",
  programBenefits: "プログラム特典",
  newProgram: "新しいプログラム",
  programTiers: "プログラム階層",
  dragDropMessage: "階層をドラッグ＆ドロップして並び替えてください",
  tier: "階層",
  remove: "削除",
  addTier: "階層を追加",
  editTier: "階層を編集",
  tierBadge: "階層バッジ",
  tierDescription: "階層の説明",
  tierName: "階層名",
  deleteTier: "階層を削除",
  noRecords: "表示するデータがありません。",
  newBenefit: "新しい特典",
  cancel: "キャンセル",
  close: "閉じる",
  addBenefit: "特典を追加",
  saving: "保存中...",
  existingBenefitName: "この名前の特典はすでに存在します。別の名前を選んでください。",
  highestTierReached: "最高階層に到達しました！",
  failFetchData: "エラー：データを取得できません。管理者に連絡してください。",
  failInitialize: "コンポーネントの初期化に失敗しました",
  failSave: "レコードの保存に失敗しました。再試行するか、管理者に連絡してください。",
  benefitDuplicateNameError: "この名前の特典はすでに存在します。別の名前を選んでください。",
  save: "保存",
  deleteBenefit: "特典を削除",
  deleteBenefitMessage: "この特典とすべての割り当てを削除してもよろしいですか？",
  delete: "削除",
  failDelete: "レコードの削除に失敗しました。再試行するか、管理者に連絡してください。",
  edit: "編集",
  first: "最初",
  last: "最後",
  next: "次",
  previous: "前",
  editBenefit: "特典を編集",
  noProgramsAvailable: "利用可能なプログラムはありません。",
  enrollments: "登録",
  partnerEnrollments: "パートナー登録",
  newEnrollment: "新しい登録",
  allPrograms: "すべてのプログラム",
  noTiers: "選択したプログラムには利用可能な階層がありません。別のプログラムを選択するか、管理者に連絡してください。",
  editEnrollment: "登録を編集",
  addEnrollment: "登録を追加",
  deleteEnrollmentMessage: "この登録とすべての情報を削除してもよろしいですか？",
  deleteEnrollment: "登録を削除",
  duplicateEnrollment: "このパートナーはすでにこのプログラムに登録されています。",
  noAccountsAvailable: "利用可能なアカウントがありません。",
  deleteProgramMessage: "このプログラムと階層、登録、基準などすべてのデータを削除しようとしています。この操作は元に戻せません。",
  deleteProgram: "パートナープログラムを削除",
  setting: "設定",
  settings: "設定",
  partnerProgramStatus: "パートナープログラムのステータス",
  enablePartnerProgram: "パートナープログラムを有効にする",
  partnerProgramIsDisabled: "パートナープログラムは無効です",
  partnerProgramIsActive: "パートナープログラムは有効です",
  confirmationDisablingPartnerProgram: "パートナープログラムを無効にしてもよろしいですか？",
  confirmationDisablingPartnerProgramMessage: "階層の表示と特典が非表示になります。",
  ok: "OK",
  settingsSaved: "設定が正常に保存されました！",
  back: "戻る",
  benefit: "パートナープログラム特典",
  benefitTypeError: "特典の種類は必須です。",
  benefitNameRequired: "特典名は必須です。",
  enrollmentDateMustBeBeforeExpiration: "登録日は有効期限より前でなければなりません。",
  editProgram: "プログラムを編集",
  addProgram: "プログラムを追加",
  programNameRequired: "プログラム名は必須です。",
  tierNameRequired: "階層名は必須です。",
  tierInexNameRequired: "階層 {idx}：名前が必要です。",
  duplicateTierName: "重複した階層名",
  unsavedChangesConfimation: "保存されていない変更があります。閉じてもよろしいですか？",
  minTierError: "少なくとも1つの階層を構成する必要があります。",
  maxTierError: "最大6つの階層を構成できます。",
  duplicateIndexTierName: "階層 {idx}：名前 {tierName} が重複しています。",
  missingProgramId: "有効なプログラムではありません",
  program: "プログラム",
  newTier: "新しい階層",
  clone: "複製",
  configure: "設定",
  noTiersDefined: "階層が定義されていません。",
  criteria: "プログラム基準",
  noCriteriaDefined: "基準が定義されていません。",
  addCriterion: "基準を追加",
  noBenefitDefined: "特典が定義されていません。",
  atLeastOneTierRequired: "プログラムに少なくとも1つの階層を入力してください",
  confirm: "確認",
  noMoreAvailableBenefits: "追加できる特典はもうありません。",
  missingProgramIdOrPartnerAccountId: "情報が不足しています。管理者に連絡してください。",
  noProgramEnrollments: "パートナープログラムに登録されていません。管理者に連絡してください。",
  noPartnerAccount: "このページを表示するにはパートナーである必要があります。",
  certifications: "認定資格",
  numberofOpportunities: "機会の数",
  opportunityRevenue: "機会の収益",
  numberofDealRegistrations: "案件登録の数",
  custom: "カスタム",
  configureCriterion: "基準を設定",
  createCriterion: "基準を作成",
  create: "作成",
  criteriaDuplicateNameError: "この名前の基準はすでに存在します。別の名前を選択してください。",
  quantity: "数量",
  revenue: "収益",
  criterionNameRequiredError: "基準名は必須です。",
  criterionTypeRequiredError: "基準の種類は必須です。",
  trainingCourseRequiredError: "認定資格にはトレーニングコースが必要です。",
  rollupFieldRequiredError: "収益には集計フィールドが必要です。",
  attainmentDateFieldRequired: "達成日フィールドが必要です",
  unitofMeasureRequired: "測定単位が必要です。",
  editCriterion: "基準を編集",
  missingFields: "情報が不足しています",
  trainingCourse: "トレーニングコース",
  name: "名前",
  type: "種類",
  filterLogic: "フィルターロジック（任意）",
  filterLogicPlaceHolder: "例：（1 と 2）または 3",
  reset: "リセット",
  addFilter: "フィルターを追加",
  equals: "等しい",
  notEquals: "等しくない",
  includes: "含む",
  excludes: "含まない",
  greaterThan: "より大きい",
  lessThan: "より小さい",
  lessOrEqual: "以下",
  greaterOrEqual: "以上",
  contains: "含む",
  notContain: "含まない",
  startWith: "で始まる",
  notStartWith: "で始まらない",
  partnerAccount: "パートナーアカウント",
  enrollmentDate: "登録日",
  expirationDate: "有効期限"
}

export default messages
