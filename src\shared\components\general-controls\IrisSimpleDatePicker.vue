<script setup lang="ts">
import { ref, watch, onMounted, type PropType } from 'vue'
import LanguageHelper from '@/shared/services/language-helper'

const languageHelper = new LanguageHelper()

// Props
const props = defineProps({
    value: [Number, String],
    min: Number,
    max: Number,
    id: {
        type: String,
        required: true
    },
    type: {
        type: String as PropType<'year' | 'month' | 'day'>,
        required: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: String | Number) => true
})

// Refs
const _value = ref()
const years = ref<number[]>([])
const months = ref<string[]>([])
const days = ref<number[]>([])

// Hooks
onMounted(() => {
    if (props.type == 'year')
        years.value = getYears()
    else if (props.type == 'month')
        months.value = getMonths()
    else if (props.type == 'day')
        days.value = getDays()

    _value.value = getValue()
})

// Watches
watch(_value, () => {
    const newValue = getValue()

    if (_value.value.toString() != newValue)
        emits('onChange', _value.value!)
})

watch(() => props.value, () => {
    const newValue = getValue()

    if (_value.value.toString() != newValue)
        _value.value = newValue
})

// Functions
function getValue() {
    let value = props.value ? props.value : -1

    value = Number(value)

    if (props.type == 'year' && !years.value.includes(value))
        value = -1
    else if (props.type == 'month' && (value < 0 || value > 11))
        value = -1
    else if (props.type == 'day' && !days.value.includes(value))
        value = -1

    const result = value == -1 ? '' : props.value

    return result
}

function getRange(minValue: number, maxValue: number) {
    var min = props.min ? props.min : minValue
    var max = props.max ? props.max : maxValue

    min = Math.max(min, minValue)
    max = Math.min(max, maxValue)

    return [min, max]
}

function getYears() {
    const [min, max] = getRange(1940, 2099)
    const result: number[] = []

    for (var y = min; y <= max; y++)
        result.push(y)

    return result
}

function getMonths() {
    const allMonths = languageHelper.getMessage('months')
    const aryMonths = allMonths.split(',')
    const result: string[] = []

    aryMonths.forEach(x => result.push(x))

    return result
}

function getDays() {
    const [min, max] = getRange(1, 31)
    const result: number[] = []

    for (var d = min; d <= max; d++)
        result.push(d)

    return result
}
</script>

<template>
    <div class="w-32" v-if="type == 'year'">
        <select :id="id" v-model="_value" 
        class="border-border-color text-sm rounded-lg focus:ring-border-focus focus:border-border-focus block 
        w-full p-2.5 placeholder-text-color-400 dark:border-border-color-dark dark:placeholder-text-color-400-dark
        text-text-color bg-bg-color-200 dark:text-text-color-dark dark:bg-bg-color-200-dark">
            <option value="">- {{ languageHelper.getMessage('year') }} -</option>
            <option v-for="year in years" :value="year">{{ year }}</option>
        </select>
    </div>

    <div class="w-32" v-if="type == 'month'">
        <select :id="id" v-model="_value" 
        class="border-border-color text-sm rounded-lg focus:ring-border-focus focus:border-border-focus block 
        w-full p-2.5 placeholder-text-color-400 dark:border-border-color-dark dark:placeholder-text-color-400-dark
        text-text-color bg-bg-color-200 dark:text-text-color-dark dark:bg-bg-color-200-dark">
            <option value="">- {{ languageHelper.getMessage('month') }} -</option>
            <option v-for="(month, index) in months" :value="index">{{ month }}</option>
        </select>
    </div>

    <div class="w-32" v-if="type == 'day'">
        <select :id="id" v-model="_value" 
        class="border-border-color text-sm rounded-lg focus:ring-border-focus focus:border-border-focus block
         w-full p-2.5 placeholder-text-color-400 dark:border-border-color-dark dark:placeholder-text-color-400-dark
        text-text-color bg-bg-color-200 dark:text-text-color-dark dark:bg-bg-color-200-dark">
            <option value="">- {{ languageHelper.getMessage('day') }} -</option>
            <option v-for="day in days" :value="day">{{ day }}</option>
        </select>
    </div>
</template>