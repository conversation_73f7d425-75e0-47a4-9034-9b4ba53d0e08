<script setup lang="ts">
import { getCurrentInstance, onMounted, onUnmounted, ref, watch } from 'vue'
import { FormControlModeEnum, TextboxTypeEnum, ComponentSizeEnum, HorizontalAlignmentEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { constants } from '@/shared/services/constants'
import IrisTextboxHelper from '@/shared/components/helpers/IrisTextboxHelper.vue'
import Common from '@/shared/services/common'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
import ModelBinder from '@/shared/services/model-binder'

// Props
const props = defineProps({
    field: String,
    tabIndex: Number,
    required: <PERSON><PERSON><PERSON>,
    readOnly: <PERSON><PERSON><PERSON>,
    placeHolder: String,
    rows: Number,
    autoCorrect: Bo<PERSON>an,
    autoCapitalize: Bo<PERSON>an,
    autoComplete: Boolean,
    maxLength: Number,
    decimalPlaces: Number,
    addOnText: String,
    addOnIcon: String,
    ariaLabel: String,
    floatingLabel: Boolean,
    value: [String, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    type: {
        type: String,
        default: TextboxTypeEnum.text,
        validator: (value: string) => {
            return (<any>Object).values(TextboxTypeEnum).includes(value)
        }
    },
    size: {
        type: String,
        default: ComponentSizeEnum.default,
        validator: (value: string) => {
            return (<any>Object).values(ComponentSizeEnum).includes(value)
        }
    },
    addOnAlignment: {
        type: String,
        default: HorizontalAlignmentEnum.left,
        validator: (value: string) => {
            return (<any>Object).values(HorizontalAlignmentEnum).includes(value)
        }
    },
    visible: {
        type: Boolean,
        default: true
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true,
    onKeyPress: (value: string) => true,
    onBlur: null
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()

// Refs
const $el = ref<HTMLElement>()
const _value = ref(base.getValue())
const _required = ref(props.required)
const _readOnly = ref(props.readOnly)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _type = ref(props.type)
const _maxLength = ref(props.maxLength)

// Vars
const modelBinder = new ModelBinder()
const refs = { _value, _required, _readOnly, _mode, _maxLength }
const methods = { getInputType, isInputGroup, isTextType, isValid: base.isValid }
const fieldTypeToType = {
    Text: TextboxTypeEnum.text,
    TextArea: TextboxTypeEnum.textArea,
    Number: TextboxTypeEnum.number,
    Percent: TextboxTypeEnum.percent,
    Currency: TextboxTypeEnum.currency,
    Url: TextboxTypeEnum.url,
    Email: TextboxTypeEnum.email,
    Phone: TextboxTypeEnum.phone,
    Password: TextboxTypeEnum.password,
    Secret: TextboxTypeEnum.secret
}

// Hooks
onMounted(() => {
    base.addInputFieldSymbol(getCurrentInstance())
    const formId = base.getParentFormId($el.value!)
    store.add(base.uniqueId, formId, base.getErrors, base.addDatabaseErrors, validate)
    initialize()
})

onUnmounted(() => {
    store.remove(base.uniqueId)
})

// Watches
watch(_value, () => {
    if (base.isBound())
        (<any>props.value)[props.field!] = _value.value

    emits('onChange', _value.value)
    autoCapitalize()
})

watch(() => [
    props.value, 
    props.field,
    props.required, 
    props.readOnly, 
    props.label,
    props.mode, 
    props.type, 
    props.maxLength
], () => initialize())

// Functions
function initialize() {
    setPropValues()
    validateProps()
    autoCapitalize()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label
        _type.value = (<any>fieldTypeToType)[base.fieldMetadata.value.FieldType]

        if (!_type.value)
            _type.value = fieldTypeToType.Text

        if (base.fieldMetadata.value.Length > 0)
            _maxLength.value = base.fieldMetadata.value.Length

            if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _readOnly.value = props.readOnly
        _label.value = props.label
        _mode.value = props.mode
        _type.value = props.type
        _maxLength.value = props.maxLength
    }
}

function validateProps(): void {
    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(addErrors: boolean = true): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true

    var errors: ModelError[] = []
    const field = props.field ?? props.id

    if (_required.value && !_value.value) {
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value ?? props.id })
        errors.push(new ModelError(field, message))
    }

    if ((_type.value == TextboxTypeEnum.number || _type.value == TextboxTypeEnum.percent || _type.value == TextboxTypeEnum.currency) && _value.value && !Common.isNumber(_value.value)) {
        const message = base.languageHelper.getMessage('invalidProp', { prop: 'value' })
        errors.push(new ModelError(field, message))
    }

    if (_type.value == TextboxTypeEnum.email && _value.value && !Common.isValidEmail(_value.value)) {
        const message = base.languageHelper.getMessage('invalidProp', { prop: 'value' })
        errors.push(new ModelError(field, message))
    }

    if (_type.value == TextboxTypeEnum.url && _value.value && !Common.isValidUrl(_value.value)) {
        const message = base.languageHelper.getMessage('invalidProp', { prop: 'value' })
        errors.push(new ModelError(field, message))
    }

    if (_type.value == TextboxTypeEnum.phone && _value.value && !Common.isValidPhone(_value.value)) {
        const message = base.languageHelper.getMessage('invalidProp', { prop: 'value' })
        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    if (addErrors)
        base.addErrors(errors)

    return result
}

function isInputGroup() {
    const result = _mode.value == FormControlModeEnum.edit && (props.addOnText || props.addOnIcon)
    return result
}

function isTextType() {
    const result = _type.value == TextboxTypeEnum.text || _type.value == TextboxTypeEnum.textArea
    return result
}

function  getInputType() { 
    var result = 'text'

    if (_type.value == TextboxTypeEnum.password)
      result = 'password'
    else if (_type.value == TextboxTypeEnum.secret && _mode.value != FormControlModeEnum.edit)
      result = 'password'

    return result
}

function autoCapitalize() {
    if (props.autoCapitalize && (props.type === TextboxTypeEnum.text || props.type == TextboxTypeEnum.textArea))
        _value.value = Common.toTitleCase(_value.value)
}

function formatValue() {
    var result = _value.value

    if (_mode.value == FormControlModeEnum.text || _mode.value == FormControlModeEnum.html) {
        var fieldMetadata = base.isBound() ? base.fieldMetadata.value : null

        if (!fieldMetadata)
            switch (_type.value) {
                case TextboxTypeEnum.currency:
                    fieldMetadata = { FieldType: 'Currency', Precision: Common.numParts(_value.value).decimal?.length }
                    break
                case TextboxTypeEnum.number:
                    fieldMetadata = { FieldType: 'Number', Precision: Common.numParts(_value.value).decimal?.length }
                    break
                case TextboxTypeEnum.percent:
                    fieldMetadata = { FieldType: 'Percent', Precision: Common.numParts(_value.value).decimal?.length }
                    break
                case TextboxTypeEnum.password:
                    fieldMetadata = { FieldType: 'Password' }
                    break
                case TextboxTypeEnum.secret:
                    fieldMetadata = { FieldType: 'Secret' }
                    break
            }

        const record = base.isBound() ? props.value : null
        
        result = modelBinder.formatData(_value.value, fieldMetadata, record) 
    }

    return result
}

function isLink() {
    const result = _mode.value == FormControlModeEnum.html && base.isBound() && base.fieldMetadata.value.IsNameField
    return result
}

function formatTextAreaValue(): string {
    if (!_value.value)
        return ''

    var result = Common.htmlEncode(_value.value)
    result = result.replace(/\n/g, '<br />')

    return result
}

function nameLinkClicked(path: string) : void {
    if (path)
        window.location.href = `${path}?retUrl=${encodeURIComponent(window.location.href)}`
}
</script>

<template>
    <div ref="$el" v-if="visible">
        <div v-if="!base.isBound() || base.fieldMetadata.value.IsReadable">
            <!-- Regular Label -->
            <div class="mb-1" v-if="showLabel && _label && !floatingLabel">
                <label :for="id" class="iris-label-inline me-2">{{ _label }}
                    <span class="inline iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
                </label>
            </div>

            <!-- Text or Html -->
            <template v-if="_mode == 'text' || _mode == 'html'">
                <template v-if="_value">
                    <!-- Email -->
                    <template v-if="_type == 'email'">
                        <!-- Email (Text) -->
                        <template v-if="_mode == 'text'">
                            <span>{{ _value }}</span>
                        </template>
                        <!-- Email (Html) -->
                        <template v-else>
                            <a :href="'mailto:' + _value" target="_blank" class="text-primary hover:text-primary-hover">{{ _value }}</a>
                        </template>
                    </template>
                    <!-- Url -->
                    <template v-if="_type == 'url'">
                        <!-- Url (Text) -->
                        <template v-if="_mode == 'text'">
                            <span>{{ _value }}</span>
                        </template>
                        <!-- Url (Html) -->
                        <template v-else>
                            <IrisLink :path="_value" class="text-primary hover:text-primary-hover"></IrisLink>
                        </template>
                    </template>
                    
                    <!-- No Url or Email -->
                    <template v-if="_type != 'email' && _type != 'url'">
                        <!-- Text -->
                        <span v-if="_mode == 'text'">{{ formatValue() }}</span>
                        <!-- HTML -->
                        <template v-else>
                            <span v-if="isLink()">
                                <a class="text-primary hover:text-primary-hover" @click.prevent="nameLinkClicked(base.getId())" :href="base.getId()">{{ formatValue() }}</a>
                            </span>
                            <template v-else>
                                <template v-if="_type == 'textArea'">
                                    <span v-html="formatTextAreaValue()"></span>
                                </template>
                                <template v-else>
                                    <span>{{ formatValue() }}</span>
                                </template>
                            </template>
                        </template>
                    </template>
                </template>
            </template>

            <!-- No Text or Html -->
            <template v-else>
                <!-- Text Area -->
                <template v-if="_type == 'text-area'">
                    <IrisTextboxHelper type="TextArea" :fields="props" :emits="emits" :refs="refs" :methods="methods" @on-focus="base.isTouched = true" @on-blur="validate()"></IrisTextboxHelper>
                </template>
                
                <!-- Text Input -->
                <template v-else>
                    <!-- With Input Group -->
                    <template v-if="isInputGroup()">
                        <template v-if="addOnAlignment == HorizontalAlignmentEnum.left">
                            <div class="iris-input-group-left">
                                <span v-if="!addOnIcon" class="iris-input-group-text">
                                    <!-- Add on text -->
                                    <span >{{ addOnText }}</span>
                                    <!-- Add on icon -->
                                    <!-- <span v-if="addOnIcon"><IrisIcon :name='addOnIcon'/></span> -->
                                </span>
                                <div class="relative w-full">
                                    <div v-if="addOnIcon" class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                        <span><IrisIcon :name='addOnIcon'/></span>
                                    </div>
                                    
                                    <IrisTextboxHelper type="Text" :hasIconOnLeft="addOnIcon? true : false" :fields="props" :emits="emits" :refs="refs" :methods="methods" @on-focus="base.isTouched = true" @on-blur="validate()"></IrisTextboxHelper>
                                    <label :for="id" class="iris-floating-label" v-if="showLabel && _label && floatingLabel">{{ _label }}</label>
                                </div>
                            </div>
                        </template>
  
                        <template v-if="addOnAlignment == HorizontalAlignmentEnum.right">
                            <div class="iris-input-group-right">
                                <div class="relative w-full">
                                    <IrisTextboxHelper type="Text" :hasIconOnRight="addOnIcon? true : false" :fields="props" :emits="emits" :refs="refs" :methods="methods" @on-focus="base.isTouched = true" @on-blur="validate()"></IrisTextboxHelper>
                                    <label :for="id" class="iris-floating-label" v-if="showLabel && _label && floatingLabel">{{ _label }}</label>
                                    <div v-if="addOnIcon" class="absolute inset-y-0 end-0 flex items-center pe-3 pointer-events-none">
                                        <span><IrisIcon :name='addOnIcon'/></span>
                                    </div>
                                </div>
                                <span v-if="!addOnIcon" class="iris-input-group-text">
                                    <!-- Add on text -->
                                    <span >{{ addOnText }}</span>
                                    <!-- Add on icon -->
                                    <!-- <span v-if="addOnIcon"><i :class="addOnIcon"></i></span> -->
                                </span>
                            </div>
                        </template>
                    </template>

                    <!-- Without Input Group -->
                    <template v-else>
                        <div class="relative">
                            <IrisTextboxHelper type="Text" :fields="props" :emits="emits" :refs="refs" :methods="methods" @on-focus="base.isTouched = true" @on-blur="validate()"></IrisTextboxHelper>
                            <label :for="id" class="iris-floating-label" v-if="showLabel && _label && floatingLabel">{{ _label }}</label>
                        </div>
                    </template>
                </template>
            </template>

            <!-- Hint -->
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>

            <!-- Errors -->
            <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
        </div>
    </div>
</template>