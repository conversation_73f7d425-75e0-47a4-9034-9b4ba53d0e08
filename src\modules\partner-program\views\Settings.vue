<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { RequestMethod } from '@/shared/services/enums'
import { useIrisPage } from '@/shared/composables/iris-page'
import DataAccess from '@/shared/services/data-access'
import LanguageHelper from '@/shared/services/language-helper'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisToast from '@/shared/components/general-controls/IrisToast.vue'
import IrisCheckBox from '@/shared/components/form-controls/IrisCheckbox.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'

/* --- Interfaces --- */
interface SettingModel {
    IsEnabled: boolean
}

/* --- Constants and Refs --- */
const languageHelper = new LanguageHelper()
const base = useIrisPage()
const dataAccess = new DataAccess()
const router = useRouter()
const isLoading = ref(false)
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')
const showDisableConfirmation = ref(false)
const showAlert = ref(false)
const alertMessage = ref('')
const alertType = ref<'danger' | 'info' | 'success' | 'warning'>("success")
const settingModel = ref<SettingModel>({
    IsEnabled: false
})

/* --- Lifecycle Hooks --- */
onMounted(async () => {
    base.setPageTitle(languageHelper.getMessage('partnerProgram'))

    await loadData()
})

/* --- Functions --- */
async function loadData() {
    isLoading.value = true
    showErrorMessage.value = false
    try {
        const endpoint = `/partnerprogram/loadsetting`

        const response = await dataAccess.execute(endpoint)

        if (!response.setting) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            isLoading.value = false
            return
        }

        settingModel.value = response.setting

    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    } finally {
        isLoading.value = false
    }
}

async function saveSetting() {
    isLoading.value = true
    try {
        const url = "/partnerprogram/savesetting"

        await dataAccess.execute(url, settingModel.value, RequestMethod.post)

        alertMessage.value = languageHelper.getMessage('settingsSaved')
        alertType.value = "success"
        showAlert.value = true
        isLoading.value = false

        if (!settingModel.value.IsEnabled) 
            router.push('/setup/partner-program/settings?disabled=1')
        else 
            router.push('/setup/partner-program/programs')
        
    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage('failSave')
        isLoading.value = false
    }
}

function changeEnable(value: boolean) {
    if (!value && settingModel.value.IsEnabled) {
        settingModel.value.IsEnabled = value
        showDisableConfirmation.value = true
    } else
        settingModel.value.IsEnabled = value

}

async function saveEnableSetting() {
    settingModel.value.IsEnabled = false
    showDisableConfirmation.value = false

    const url = "/partnerprogram/savesetting"
    await dataAccess.execute(url, settingModel.value , RequestMethod.post)

    if (!settingModel.value.IsEnabled) 
        router.push('/setup/partner-program/settings?disabled=1')
    else 
        router.push('/setup/partner-program/programs')
}

function cancelDisableClicked() {
    settingModel.value.IsEnabled = true
    showDisableConfirmation.value = false
}

</script>
<template>
    <partner-program-setup-nav selected-tab="settings" />
    <div
        class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark rounded">
        <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
            {{ serverErrorMessage }}
        </iris-alert>
        <iris-toast v-model:show="showAlert" :message="alertMessage" :type="alertType" :duration="3000" />
        <h2 class="text-xl font-semibold mb-4">{{ languageHelper.getMessage("setting") }}</h2>
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <div v-else
            class="w-full p-6 mb-4 bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm">
            <h5 class="mb-2 font-bold text-text-color-dark dark:text-text-color-dark-dark">{{
                languageHelper.getMessage("partnerProgramStatus") }}</h5>
            <div class="flex justify-between items-end">
                <iris-checkBox id="pt_enabled" :value="settingModel.IsEnabled"
                    :label="languageHelper.getMessage('enablePartnerProgram')" mode="edit" output-mode="toggle"
                    @onChange="changeEnable" />
                <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 font-medium p-2 text-xs rounded"
                    v-if="!settingModel.IsEnabled">{{ languageHelper.getMessage("partnerProgramIsDisabled") }}</span>
                <span class="bg-bg-color-200 text-xs font-medium p-2 rounded" v-else>{{
                    languageHelper.getMessage("partnerProgramIsActive") }}</span>
            </div>
        
            <div class="text-right mt-4">
                <button type="button"
                    class="btn-primary px-4 py-2 rounded hover:bg-primary-hover dark:hover:bg-primary-hover"
                    @click="saveSetting">{{ languageHelper.getMessage("save") }}</button>
            </div>
        </div>
    </div>
    <iris-modal :show="showDisableConfirmation" size="small" id="disable_confirmation" :closable=true
        @onHide="showDisableConfirmation = false">
        <template #content>
            <div>
                <h3 class="mb-5 font-bold text-xl">{{ languageHelper.getMessage("confirmationDisablingPartnerProgram")
                    }}</h3>
                <div class="text-text-color-300 space-y-3 mb-4">
                    {{ languageHelper.getMessage("confirmationDisablingPartnerProgramMessage") }}
                </div>
            </div>
        </template>
        <template #footer>
            <div class="flex flex-1 justify-between gap-4">
                <button class="flex-1 btn-light" type="button" @click="cancelDisableClicked">
                    {{ languageHelper.getMessage('cancel') }}</button>
                <button ref="$btn" class="flex-1 btn-danger" type="button" @click="saveEnableSetting">
                    {{ languageHelper.getMessage('confirm') }}</button>
            </div>
        </template>
    </iris-modal>
</template>