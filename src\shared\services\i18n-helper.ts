import { createI18n } from 'vue-i18n'
import { DateTimeFormat, CurrencySymbolFormat } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import { locales, getLanguageInfo } from '@/i18n/locales'

export const i18n = createI18n({
    locale: 'en-US',
    fallbackLocale: 'en-US',
    legacy: false,
    messages: locales,
    numberFormats: locales,
    datetimeFormats: locales
} as any)

export default class I18nHelper {
    get i18n() { return i18n }

    getCurrentLocale(): string {
        const { locale } = this.i18n.global as any
        const result: string = locale.value

        return result
    }

    changeLocale(newLocale: string): void {
        const { locale } = this.i18n.global as any
        
        if (locale.value != newLocale)
            locale.value = newLocale
    }

    changeLanguage(language: string): void {
        const newMessages = getLanguageInfo(language)

        for (const [key, value] of Object.entries(locales)) {
            const localInfo = this.i18n.global.getLocaleMessage(key)

            localInfo.messages = newMessages
            this.i18n.global.setLocaleMessage(key, localInfo)
        }
    }

    resetLocale(): void {
        const systemInfo = useIrisSystemInfoStore().getData()
        this.changeLocale(systemInfo.currentLocale)
    }

    getMessage(key: string, params: any = null, language: string | null = null): string {
        let result: string

        if (language) {
            const messages: any = getLanguageInfo(language)

            result = messages[key]

            if (params) {
                if (typeof params === 'object') {
                    if (params instanceof Array) {
                        // Array
                        var cnt = 0

                        params.forEach(x => result = result.replace(new RegExp(`\\{${cnt++}\\}`, 'g'), x))
                    }
                    else {
                        // Just an object
                        for (const [k, v] of Object.entries(params))
                            result = result.replace(new RegExp(`\\{${k}\\}`, 'g'), v as string)
                    }
                }
            }
        }
        else {
            const { t } = this.i18n.global as any
            const fullKey = `messages.${key}`

            result = t(fullKey, params)
        }

        return result
    }

    getLocalesByCurrencyIsoCode(currencyCode: string): string[] {
        const result: string[] = []

        for (const [key, value] of Object.entries(this.i18n.global.numberFormats.value))
            if ((value as any).currency.currency === currencyCode)
                result.push(key)

        return result
    }

    getCurrencySymbolByLocale(locale: string | null = null, format: CurrencySymbolFormat = CurrencySymbolFormat.symbol): string {
        const { n } = this.i18n.global as any
        const result: string = n(0, 'currency', locale, { currencyDisplay: format}).replace(/\d./g, '').trim()

        return result
    }

    formatCurrency(value: number, locale: string | null = null, format: CurrencySymbolFormat = CurrencySymbolFormat.symbol, currentDecimalPlaces: number | undefined = undefined): string {
        const { n } = this.i18n.global as any
        const result: string = n(value, 'currency', locale, { 
            currencyDisplay: format,
            minimumFractionDigits: currentDecimalPlaces,
            maximumFractionDigits: currentDecimalPlaces
        })

        return result
    }

    formatNumber(value: number, locale: string | null = null, isPercentage: boolean = false,
        {
            minimumIntegerDigits = undefined,
            minimumFractionDigits = undefined,
            maximumFractionDigits = undefined,
            minimumSignificantDigits = undefined,
            maximumSignificantDigits = undefined
        } = {}): string {
        const { n } = this.i18n.global as any
        const key = isPercentage ? 'percent' : 'decimal'
        const result: string = n(value, key, locale,
            {
                minimumIntegerDigits,
                minimumFractionDigits,
                maximumFractionDigits,
                minimumSignificantDigits,
                maximumSignificantDigits
            })

        return result
    }

    formatDateTime(value: Date, format: DateTimeFormat, locale: string | null = null): string {
        const { d } = this.i18n.global as any
        const result: string = d(value, format, locale)

        return result
    }

    customFormatDateTime(value: Date, format: any, locale: string | null = null): string {
        const { d } = this.i18n.global as any
        const result: string = d(value, format, locale)

        return result
    }
}
