import type { DatabaseErrorType } from "@/shared/services/types"

export default class DatabaseError extends Error {
    errors: DatabaseErrorType[]

    constructor(message: string, errors: DatabaseErrorType[]) {
        super(message)
        this.name = this.constructor.name
        this.errors = errors
        Object.setPrototypeOf(this, DatabaseError.prototype)

        if (Error.captureStackTrace)
            Error.captureStackTrace(this, this.constructor)
    }
}
