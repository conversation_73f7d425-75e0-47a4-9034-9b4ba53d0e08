<script setup lang="ts">
import { computed } from 'vue'
import IrisProgressBar from '@/shared/components/form-controls/IrisProgressBar.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps<{
  criterion: any
  tierId: string
  evaluationData: any[]
}>()

const tierDetail = computed(() => {

  if (!Array.isArray(props.evaluationData) || props.evaluationData.length === 0) {
    return null
  }

  const criterionEvaluationEntry = props.evaluationData.find(
    (evalItem: any) =>
      evalItem.CriterionId === props.criterion.Id  
    )

 if (!criterionEvaluationEntry) {
    return null
  }

  const tierSpecificDetail = criterionEvaluationEntry.TierDetails?.find(
    (detail: any) => detail.TierId === props.tierId
  )

  if (tierSpecificDetail) {
    return {
      Status: tierSpecificDetail.Status,
      TargetValue: tierSpecificDetail.TargetValue,
      PartnerActualValue: criterionEvaluationEntry.PartnerActualValue
    }
  }

  return null
})

</script>

<template>
  <template v-if="tierDetail">
    <span v-if="tierDetail.Status === 'Met'" title="Met" class="status-icon">
      <iris-icon name="circle-check" class="inline w-5 h-5"/>       
    </span>
    <iris-progress-bar v-else-if="tierDetail.Status === 'In Progress'"
        :id="`criteria-${criterion.Id}-tier-${tierId}`"
        :value="tierDetail.PartnerActualValue"
        :max="tierDetail.TargetValue"
        :label="`>=`+tierDetail.TargetValue"
        
        size="large"
        :displayValue="true"
    />
    <span v-else-if="tierDetail.Status === 'Not Started'" :title="`Target: ${tierDetail.TargetValue}`" class="status-target">
        >= {{ tierDetail.TargetValue }}
    </span>
    <span v-else :title="tierDetail.Status" class="status-other">
       {{ tierDetail.Status }}
    </span>
  </template>
  <span v-else class="status-nodata">-</span>
</template>