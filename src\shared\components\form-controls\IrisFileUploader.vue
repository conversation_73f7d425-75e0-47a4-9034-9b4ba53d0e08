<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, onUnmounted, ref } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { constants } from '@/shared/services/constants'
import { ModelError } from '@/shared/services/model-error'
import '@syncfusion/ej2-base/styles/material.css'
import '@syncfusion/ej2-inputs/styles/material.css'
import '@syncfusion/ej2-buttons/styles/material.css'
import '@syncfusion/ej2-vue-inputs/styles/material.css'

interface Props {
  id: string
  label: string
  saveUrl: string
  removeUrl: string
  required?: boolean
  caption?: string
  buttonText?: string
  buttonCss?: string
  boxCss?: string
  autoUpload?: boolean
  multiple?: boolean
  maxFileSize?: number
  allowedExtensions?: string
  showFileCount?: boolean
}

// Constants
const defaultCaptionCss = 'e-file-drop'
const defaultButtonCss = 'e-css e-btn'
const defaultBoxCss = 'file-uploader'

// Props
const props = withDefaults(defineProps<Props>(), {
  required: false,
  caption: 'Or drop files here',
  buttonText: 'Browse ...',
  buttonCss: defaultButtonCss,
  boxCss: defaultBoxCss,
  autoUpload: true,
  multiple: false,
  maxFileSize: 31457280, // 30MB
  allowedExtensions: '',
  showFileCount: false
})

// Emits
const emit = defineEmits<{
  (e: 'upload-success', args: any): void
  (e: 'upload-failure', args: any): void
  (e: 'file-removing', args: any): void
  (e: 'file-selected', args: any): void
}>()

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()

// Refs
const $el = ref<HTMLElement>()
const $uploader = ref()
const asyncSettings = ref({ saveUrl: props.saveUrl, removeUrl: props.removeUrl })
const fileCount = ref(0)

// Hooks
const labelText = computed(() => {
  var result = props.label

  if (props.showFileCount)
    result += ` (${fileCount.value})`

  return result
})

onMounted(() => {
  // Validate Props
  validateProps()

  // Caption
  const captionQuery = document.getElementsByClassName(defaultCaptionCss)

  if (captionQuery && captionQuery.length > 0)
    captionQuery[0].innerHTML = props.caption

  // Button
  const buttonQuery = document.getElementsByClassName(defaultButtonCss)

  if (buttonQuery && buttonQuery.length > 0) {
    const btn = buttonQuery[0] as HTMLButtonElement

    btn.innerHTML = props.buttonText
    btn.className = props.buttonCss
    btn.addEventListener('blur', () => validate())
  }

  // Box
  const boxQuery = document.getElementsByClassName(defaultBoxCss)

  if (boxQuery && boxQuery.length > 0)
    boxQuery[0].className = props.boxCss

  // Add to store
  const formId = base.getParentFormId($el.value!)
  store.add(base.uniqueId, formId, base.getErrors, base.addDatabaseErrors, validate)
})

onUnmounted(() => {
    store.remove(base.uniqueId)
})

// Functions
function validateProps(): void {
    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(addErrors: boolean = true): boolean {
    var errors: ModelError[] = []

    if (props.required && fileCount.value == 0) {
      const message = base.languageHelper.getMessage('isRequired', { label: props.label })

      errors.push(new ModelError(props.label, message))
    }

    const result = errors.length == 0

    if (addErrors)
        base.addErrors(errors)

    return result
}

function updateFileCount(value: number): void {
  fileCount.value += value

  if (fileCount.value < 0)
    fileCount.value = 0
}

function handleSuccess(args: any): void {
  if (args.operation === 'upload')
    updateFileCount(1)
  else if (args.operation === 'remove')
    updateFileCount(-1)

  validate()
  emit('upload-success', args)
}

function handleFailure(args: any): void {
  emit('upload-failure', args)
}

function handleRemoving(args: any): void {
  emit('file-removing', args)
}

function handleSelected(args: any): void {
  emit('file-selected', args)
}
</script>

<template>
  <div ref="$el" class="file-uploader">
    <label>{{ labelText }}</label>
    <ejs-uploader
        ref="$uploader"
        :asyncSettings="asyncSettings"
        :autoUpload="props.autoUpload"
        :multiple="props.multiple"
        :maxFileSize="props.maxFileSize"
        :allowedExtensions="props.allowedExtensions"
        @success="handleSuccess"
        @failure="handleFailure"
        @removing="handleRemoving"
        @selected="handleSelected"
    ></ejs-uploader>
  </div>

  <!-- Errors -->
  <div class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
</template>
