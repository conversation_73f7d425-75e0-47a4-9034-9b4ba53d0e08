<script setup lang="ts">
import { ref, computed } from 'vue'
import Svg from '@/shared/assets/data/svg.json'

// Props
const props = defineProps({
    name: {
        type: String,
        required: true
    },
    width: {
        type: String,
        default: '1rem'
    },
    height: {
        type: String,
        default: '1rem'
    },
    viewBox: {
        type: String,
        default: null
    }
})
const data = computed(() => (Svg as any)[props.name])
const isHovered = ref(false)
</script>

<template>
    <svg 
    xmlns="http://www.w3.org/2000/svg" 
    class="inline" 
    :class="$attrs.class" 
    :viewBox="props.viewBox || data?.viewBox" 
    :width="width" 
    :height="height" 
    v-html="data?.content"
    @mouseover="isHovered = true"
    @mouseleave="isHovered = false"
    v-if="data"></svg>
</template>