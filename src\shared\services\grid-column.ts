import { FormControlModeEnum } from "@/shared/services/form-control-enums"

export class GridColumn {
    label : string = ''
    name : string = ''
    sortExpression : string = ''
    sortDirection: string = 'A'
    visible: boolean = true
    selected: boolean = false
    component: string = ''
    mode: string = FormControlModeEnum.html
    width: string = ''
    data: Object | undefined = undefined

    reverseSortDirection() : void {
        if (this.sortDirection == "A")
            this.sortDirection = "D"
        else
            this.sortDirection = "A"
    }

    static create(data: object) : GridColumn {
        const column = new GridColumn();

        for (const prop in data) {
            if (column.hasOwnProperty(prop)) {
                // Use value from data object if present
                (column as any)[prop] = (data as any)[prop];
            }
        }

        return column;
    }
}