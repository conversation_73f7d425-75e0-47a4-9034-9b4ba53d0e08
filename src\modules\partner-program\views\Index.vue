<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useIrisPage } from '@/shared/composables/iris-page'
import LanguageHelper from '@/shared/services/language-helper'
import PartnerProgramDropdown from '@/modules/partner-program/components/PartnerProgramDropDown.vue'
import PartnerStatus from '@/modules/partner-program/components/PartnerStatus.vue'

const languageHelper = new LanguageHelper()
const base = useIrisPage()
const selectedProgramId = ref<string>('')
const partnerAccountId = ref<string>('')
const noEnrollment = ref(false)

onMounted(() => {
    base.setPageTitle(languageHelper.getMessage('partnerProgram'))
})

function partnerProgramChanged(response: { partnerAccountId: string, selectedProgramId: string }) {
    partnerAccountId.value = response.partnerAccountId
    selectedProgramId.value = response.selectedProgramId
}

</script>
<template>
    <PartnerStatus 
        id="partnerStatus" 
        :programId="selectedProgramId" 
        :partnerAccountId="partnerAccountId">
        <template 
            v-slot:PartnerProgramDropdown>
            <PartnerProgramDropdown 
                id="partnerProgramDropdown" 
                :partnerAccountId="partnerAccountId"
                :selectedProgramId="selectedProgramId" 
                @onChange="partnerProgramChanged"
                @onLoaded="partnerProgramChanged"
                @onEmpty="noEnrollment = true"/>
        </template>
    </PartnerStatus>
</template>