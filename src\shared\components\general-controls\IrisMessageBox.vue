<script setup lang="ts">
import { onMounted, ref, watch, type PropType } from 'vue'
import { ModalSizeEnum } from '@/shared/services/form-control-enums'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import Common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'

const props = defineProps({
    show: {
        type: Boolean,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    title: {
        type: String,
        required: false
    },
    primaryButtonLabel: {
        type: String,
        default: "Ok"
    },
    cancelButtonLabel: {
        type: String,
        default: "Cancel"
    },
    icon: {
        type: String,
        default: "circle-info"
    },
    size: {
        type: String,
        default: "small",
        validator: (value: string) => {
            return (<any>Object).values(ModalSizeEnum).includes(value)
        }
    },
    type: {
        type: String as PropType<'Message' | 'Confirm'>,
        required: true,
        validator: (value: string) => {
            return ['Message', 'Confirm'].includes(value);
        }
    },
    closable: {
        type: Boolean,
        default: true
    },
    fullWidthButtons: {
        type: Boolean,
        default: false
    }
})

// Emits
const emits = defineEmits({
    onPrimaryClick: () => true,
    onCancelClick: () => true
})

const _icon = ref(props.icon)
const _id = Common.newGuid()
const languageHelper = new LanguageHelper()
const $btn = ref<HTMLElement>()

watch(() => [props.icon, props.type], () => initialize())

onMounted(() => {
    initialize()
})

function initialize() {
    _icon.value = props.icon

    if (!_icon.value || _icon.value == undefined) {
        if (props.type == 'Message')
            _icon.value = "circle-info"
        else
            _icon.value = "question-mark"
    }
}

function primaryButtonClicked() {
    emits('onPrimaryClick')
}

function cancelClicked() {
    emits('onCancelClick')
}

function modalShown() {
    setTimeout(function() {
        $btn.value?.focus()
    }, 300)
}
</script>

<template>
<iris-modal :show="show" :size="size" :id="_id" :closable="closable" @onHide="cancelClicked" @onShow="modalShown">
    <template #content>
        <div>
            <button type="button"
                @click="cancelClicked"
                class="absolute top-3 end-2.5 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center text-text-color-400  hover:bg-bg-color-300 hover:text-text-color dark:hover:bg-bg-color-300-dark dark:hover:text-text-color-dark">
                <IrisIcon name="xmark" class="w-5 h-5"/>
                <span class="sr-only">{{ languageHelper.getMessage('close') }}</span>
            </button>
            <div class="text-center mt-1">
                <IrisIcon :name="_icon!" width="3.5rem" height="3.5rem"/>
            </div>
            <h4 v-if="title" class="text-center font-semibold text-lg mt-3">{{ title }}</h4>
            <p class="text-center mt-3">{{ message }}</p>
        </div> 
    </template>
    <template #footer>
        <button class="btn btn-light" :class="{ 'w-full' : fullWidthButtons }" type="button"
            @click="cancelClicked" v-if="type == 'Confirm'">{{ cancelButtonLabel }}</button>
        <button ref="$btn" class="btn btn-primary" :class="{ 'w-full' : fullWidthButtons }" type="button"
            @click="primaryButtonClicked">{{ primaryButtonLabel }}</button>
    </template>
</iris-modal>
</template>