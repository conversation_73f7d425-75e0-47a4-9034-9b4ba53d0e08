export default class FloatingMenuHelper {

    static calcSiteBodyPaddingTop(includePaddingTop: boolean) {
        const siteBody = document.querySelector('.siteBody') as HTMLElement | null

        if (!siteBody) {
            console.error('.siteBody element not found')
            return
        }

        const siteHeaderContainer = document.querySelector('.siteHeaderContainer')
        const uiMobile = document.querySelector('.ui-mobile')
        const siteBodyPaddingTop = parseInt(window.getComputedStyle(siteBody).paddingTop, 10)
        let offTop = 0

        if (siteHeaderContainer) {
            const fixedElements = document.querySelectorAll('.std-theme-siteHeaderContainer *')

            fixedElements.forEach(element => {
                const computedStyle = window.getComputedStyle(element)
            
                if (computedStyle.position === 'fixed') {
                    offTop = (element as HTMLElement).offsetHeight
                    this.calcSiteBodyPaddingTopPX(includePaddingTop, siteBodyPaddingTop, offTop)
                }
            })
        } else if (uiMobile) {
            const mobileNavbar = document.querySelector('.mobile-device .navbar-fixed-top') as HTMLElement

            if (mobileNavbar) {
                offTop = mobileNavbar.offsetHeight
                this.calcSiteBodyPaddingTopPX(includePaddingTop, siteBodyPaddingTop, offTop)
            }
        }
    }

    private static calcSiteBodyPaddingTopPX(includePaddingTop: boolean, siteBodyPaddingTop: number, offTop: number) {
        if (offTop > 0) {
            const siteBody = document.querySelector('.siteBody') as HTMLElement
            const root = document.documentElement

            siteBody.style.paddingTop = `${offTop + (includePaddingTop ? siteBodyPaddingTop : 0)}px`
            root.style.setProperty('--mag-floating-menu-height', `${offTop + (includePaddingTop ? siteBodyPaddingTop : 0)}px`)

            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (event) {
                    const targetId = (event.currentTarget as HTMLAnchorElement).getAttribute('href')!.substring(1)
                    document.querySelectorAll(`a[name^="${targetId}"]`).forEach(target => {
                        target.classList.add('mag-anchor-targeted-section')
                    })
                })
            })
        }
    }
}