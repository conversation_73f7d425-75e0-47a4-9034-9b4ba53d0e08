#Script Section
Do not use semicolons to end the statements.
Separate var declaration form code following it
For one-liner blocks, avoid using curly braces
For pure functions (functions that are not passed as arguments), use the function keyword. Do not use arrow function format.
Use arrow functions for parameters that accept functions. Do not use the function keyword.
Since we are using Typescript, leverage type safety by specifying the type of the parameters for your functions as well as the return type.
Watches are expensive. Use them only when really needed.
Avoid using the onUpdated hook, unless really required.
Use meaningful names for your variables and functions.
var rslt = true
var result = true
 
#Template section
Use kebab casing for multi-word elements and attributes to conform to HTML 5 standards.
<iris-textbox :max-length='20'>
 
#Style section
Specify the scoped keyword if you intend to restrict the style to a specific component.
@use imports should come first.

#Importing rules

List all the named imports first, and then the default imports.

No semicolons are needed at the end of each import.
No line breaks between imports.
Try to sort your imports like this:
-Vue framework imports
-3rd party imports
-Local imports
-CSS imports

import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { relativeTime } from 'dayjs/plugin/relativeTime'
import Common from '@/shared/services/common'
import '@syncfusion/ej2-base/styles/tailwind.css'
 

# Order of logic

In the component script section, the order of the logic should be arranged like the following:

Props
Emits
Refs
Vars
Lifecycle Hooks
Computed
Watches
Functions

const function is considered in Vars, so avoid using const functions and use functions instead