<script setup lang="ts">
import Common from '@/shared/services/common'

const props = defineProps({
    url: {
        type: String,
        required: true
    },
    logoClickUrl : {
        type: String,
        required: false
    }
}) 
</script>
<template>
    <a :href="logoClickUrl">
        <img :src="Common.getBaseUrl() + url" itemprop="logo" alt='Logo' style="background-image:none;" class="max-h-full max-w-full object-contain"/>
    </a>
</template>