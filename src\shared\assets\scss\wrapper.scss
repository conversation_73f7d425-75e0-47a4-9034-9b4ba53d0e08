// Privates
%iris-input-group-text-base {
    @apply inline-flex items-center border border-border-color px-3 text-text-color-400 dark:border-border-color-dark dark:text-text-color-400-dark sm:text-sm;
}

// Label
.iris-label {
    @apply block mb-2 text-sm font-medium text-text-color-200 dark:text-text-color-200-dark;
}

.iris-label-inline {
    @apply mb-2 text-sm font-medium text-text-color-200 dark:text-text-color-200-dark;
}

// Floating Label
.iris-floating-label {
    @apply absolute text-sm text-text-color-200 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-bg-color-200 px-2 peer-focus:px-2 peer-focus:text-primary peer-focus:bg-bg-color peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1 dark:text-text-color-200-dark dark:bg-bg-color-200-dark dark:peer-focus:bg-bg-color-dark
}

// Textbox
.iris-textbox {
    @apply bg-bg-color-200 border border-border-color text-text-color-200 text-sm rounded-lg focus:ring-1 focus:ring-primary focus:border-border-focus block w-full dark:bg-bg-color-200-dark dark:border-border-color-dark dark:text-text-color-200-dark
}

.iris-textbox-sm {
    @apply block w-full p-2 border-border-color text-text-color-200 rounded-lg bg-bg-color-200 text-xs focus:ring-1 focus:ring-primary focus:border-border-focus dark:border-border-color-dark dark:text-text-color-200-dark dark:bg-bg-color-200-dark
}

.iris-textbox-lg {
    @apply block w-full p-4 border-border-color text-text-color-200 rounded-lg bg-bg-color-200 text-base focus:ring-1 focus:ring-primary focus:border-border-focus dark:border-border-color-dark dark:text-text-color-200-dark dark:bg-bg-color-200-dark
}

.iris-textbox-invalid {
    @apply bg-bg-color-200 border border-red-500 text-red-900 placeholder-red-700 text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full dark:text-red-500 dark:placeholder-red-500 dark:border-red-500 dark:bg-bg-color-200-dark
}

// Text Area
.iris-textarea {
    @apply block p-4 w-full text-sm text-text-color-200 bg-bg-color-200 rounded-lg border border-border-color focus:ring-primary focus:border-border-focus dark:text-text-color-200-dark dark:bg-bg-color-200-dark dark:border-border-color-dark
}

.iris-textarea-wrapper {@apply rounded-lg overflow-hidden}

// Input Group (Left)
.iris-input-group-left {
    > .iris-input-group-text {
        @extend %iris-input-group-text-base;
        @apply rounded-l-md border-r-0;
    }
    > .iris-textbox:not(.ps-10), .relative .iris-textbox:not(.ps-10) {
        @apply rounded-l-none;
    }
    @apply flex rounded-md shadow-sm;
}

// Input Group (Right)
.iris-input-group-right {
    > .iris-input-group-text {
        @extend %iris-input-group-text-base;
        @apply rounded-r-md border-l-0;
    }
    > .iris-textbox:not(.pe-10), .relative .iris-textbox:not(.pe-10) {
        @apply rounded-r-none;
    }
    @apply flex rounded-md shadow-sm;
}

// Input Hint
.iris-input-hint {
    @apply mt-2 text-sm text-text-color-400 dark:text-text-color-400-dark;
}

// Checkbox
.iris-checkbox {
    @apply w-4 h-4 text-primary bg-bg-color border-border-color rounded-sm focus:ring-primary focus:ring-2 dark:bg-bg-color-dark dark:border-border-color-dark;
}

// Checkbox Label
.iris-checkbox-label {
    @apply ms-2 text-sm font-medium text-text-color-200 dark:text-text-color-200-dark;
}

// Alerts
%iris-alert {
    @apply p-4 mb-4 text-sm rounded-lg
}

.iris-alert-primary {
    @extend %iris-alert;
    @apply text-primary bg-text-color-100 dark:bg-text-color-100-dark;
}

.iris-alert-danger {
    @extend %iris-alert;
    @apply text-red-800 bg-red-50 dark:bg-gray-800 dark:text-red-400;
}

.iris-alert-success {
    @extend %iris-alert;
    @apply text-green-800 bg-green-50 dark:bg-gray-800 dark:text-green-400;
}

.iris-alert-warning {
    @extend %iris-alert;
    @apply text-yellow-800 bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300;
}

.iris-alert-light {
    @extend %iris-alert;
    @apply text-text-color-400 bg-bg-color-100 dark:text-text-color-400-dark dark:bg-bg-color-100-dark
}

// Buttons
.btn-light {
    @apply text-text-color-100 bg-bg-color-200 border border-border-color focus:outline-none hover:bg-bg-color-300 focus:ring-4 focus:ring-border-color dark:text-text-color-100-dark dark:bg-bg-color-200-dark dark:border-border-color-dark
    font-medium rounded-lg text-sm px-5 py-2.5
}

.btn-primary {
    @apply text-primary-text bg-primary hover:bg-primary-hover focus:ring-4 focus:ring-primary-hover 
    font-medium rounded-lg text-sm px-5 py-2.5
    focus:outline-none
}

.btn-sm {
    @apply px-3 py-2
}

.btn-xs {
    @apply px-2 py-1
}

.iris-toggle {
    > .iris-required {
        align-self: flex-start;
    }
}

.iris-required {
    > .iris-icon {
        @apply absolute ms-1 top-1
    }
    @apply relative
}

body {
    @apply text-text-color dark:text-text-color-dark;
}

.text-responsive {
    @apply overflow-hidden w-full truncate block
}

:not(.emoji-menu .popover-content)::-webkit-scrollbar { width: 12px; background: var(--mag-bg-color-200); }
:not(.emoji-menu .popover-content)::-webkit-scrollbar-thumb { background: var(--mag-base-400); -webkit-border-radius: 1ex; -webkit-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.75); }
:not(.emoji-menu .popover-content)::-webkit-scrollbar-corner { background: var(--mag-bg-color-200); }

h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
}

//rich text BS styles
.alert {padding: 15px;margin-bottom: 20px;border: 1px solid transparent;border-radius: 0.5rem;}
.alert-success {background-color: #dff0d8;border-color: #d0e9c6;color: #3c763d;}
.alert-info {background-color: #d9edf7;border-color: #bcdff1;color: #31708f;}
.alert-warning {background-color: #fcf8e3;border-color: #faf2cc;color: #8a6d3b;}
.alert-danger {background-color: #f2dede;border-color: #ebcccc;color: #a94442;}
.well {min-height: 20px;padding: 19px;margin-bottom: 20px;background-color: var(--mag-page-bg-color);
    border: 1px solid var(--mag-page-bg-200);border-radius: 0.5rem;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);}
.well-sm {padding: 9px;border-radius: 0.5rem;}
.well-lg {padding: 24px;border-radius: 0.5rem;}
.well {
    background-image: -webkit-linear-gradient(top, var(--mag-page-bg-200) 0%, var(--mag-page-bg-color) 100%);
    background-image: -o-linear-gradient(top, var(--mag-page-bg-200) 0%, var(--mag-page-bg-color) 100%);
    background-image: linear-gradient(to bottom, var(--mag-page-bg-200) 0%, var(--mag-page-bg-color) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2f2f2', endColorstr='#ffffffff', GradientType=0);
    border-color: var(--mag-element-border-muted-color);
    -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);
}
.text-muted {color: var(--mag-base-400);}
.text-success {color: #3c763d;}
.text-info {color: #31708f;}
.text-warning {color: #8a6d3b;}
.text-danger {color: #a94442;}
.lead {margin-bottom: 20px;font-size: 16px;font-weight: 300;line-height: 1.4;}
@media (min-width: 768px) {
    .lead {
        font-size: 21px;
    }
}

pre {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 1.42857143;
    word-break: break-all;
    word-wrap: break-word;
    color: var(--mag-page-text-color);
    background-color: var(--mag-page-bg-300);
    border: 1px solid var(--mag-element-border-muted-color);
    border-radius: 0.5rem;
}
.label {
    display: inline;
    padding: 0.2em 0.6em 0.3em;
    font-size: 75%;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25em;
}
.label-default {
    background-color: var(--mag-base-400);
    color: var(--mag-page-bg-color);
}
.label-primary {
    background-color: var(--mag-primary-color);
    color: var(--mag-primary-text-color);
}
.label-success {
    background-color: #5cb85c;
}
.label-info {
    background-color: #5bc0de;
}
.label-warning {
    background-color: #f0ad4e;
}
.label-danger {
    background-color: #d9534f;
}
code {
    padding: 2px 4px;
    font-size: 90%;
    color: var(--mag-page-text-color);
    background-color: var(--mag-page-bg-300);
    border-radius: 0.5rem;
}
