<script setup lang="ts">
import { constants } from '@/shared/services/constants'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    value: [Number, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    step: { 
        type: Number,
        validator: (value: number) => {
            return value > 0;
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})
</script>

<template>

</template>