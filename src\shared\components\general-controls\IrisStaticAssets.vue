<script setup lang="ts">
import { ref, computed, provide, onMounted, type PropType } from 'vue'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Auth from '@/shared/services/auth'
import Common from '@/shared/services/common'
import { LogLevel } from '@/shared/services/enums'
import '@syncfusion/ej2-base/styles/tailwind.css'
import '@syncfusion/ej2-icons/styles/tailwind.css'
import "@syncfusion/ej2-buttons/styles/tailwind3.css"
import '@syncfusion/ej2-vue-navigations/styles/tailwind.css'
import '@syncfusion/ej2-vue-filemanager/styles/tailwind.css'
import '@syncfusion/ej2-inputs/styles/tailwind.css'
import '@syncfusion/ej2-popups/styles/tailwind.css'
import '@syncfusion/ej2-grids/styles/tailwind.css'
import '@syncfusion/ej2-layouts/styles/tailwind.css'
import '@syncfusion/ej2-splitbuttons/styles/tailwind.css'
import {
  FileManagerComponent as EjsFileManager,
  Toolbar,
  NavigationPane,
  DetailsView,
  ToolbarItemsDirective as EToolbaritems,
  ToolbarItemDirective as EToolbaritem,
  type BeforeSendEventArgs,
  type FailureEventArgs,
} from '@syncfusion/ej2-vue-filemanager'

// Register required Syncfusion modules
provide('filemanager', [Toolbar, NavigationPane, DetailsView])

// -------------------------------------------------------------
// Types & props
// -------------------------------------------------------------
interface FileInfo {
  name: string
  size: number
  type: string
  [key: string]: unknown
}

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  allowedExtensions: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  allowDragAndDrop: {
    type: Boolean,
    default: false
  },
  showSelectButton: {
    type: Boolean,
    default: false
  },
  multiSelect: {
    type: Boolean,
    default: true
  },
  ajaxSettings: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({
      url: '/sys/staticasset/operations',
      downloadUrl: '/sys/staticasset/download',
      uploadUrl: '/sys/staticasset/upload',
      getImageUrl: '/sys/staticasset/getimage'
    })
  }
})

const emit = defineEmits<{
  (e: 'onSelect', files: FileInfo[]): void
}>()

// -------------------------------------------------------------
// FileManager ref & AJAX settings
// -------------------------------------------------------------
const fileManager = ref<InstanceType<typeof EjsFileManager>>()
const systemInfo = useIrisSystemInfoStore().getData()
const baseUrl = systemInfo.env.baseUrl
const computedAjaxSettings = computed(() => ({
  url: baseUrl + props.ajaxSettings.url,
  uploadUrl: baseUrl + props.ajaxSettings.uploadUrl,
  downloadUrl: baseUrl + props.ajaxSettings.downloadUrl,
  getImageUrl: baseUrl + props.ajaxSettings.getImageUrl
}))
const uploadSettings = { autoClose: true };

function onBeforeSend(args: BeforeSendEventArgs) {

  if (args.ajaxSettings && Auth.sessionInfo && Auth.sessionInfo.token) {
      (args.ajaxSettings as any).beforeSend = function(args: any) {
          args.httpRequest.setRequestHeader('Authorization', `Bearer ${Auth.sessionInfo.token}`);
      };
  }

  // Add allowedExtensions to payload if there are any
  if (props.allowedExtensions.length > 0 && args.ajaxSettings) {
    try {
      let data = (args.ajaxSettings as any)?.data;

      if (typeof data === 'string')
        data = JSON.parse(data);
      
      if (!data || typeof data !== 'object')
        data = {};     

      data.allowedExtensions = props.allowedExtensions.join(',');
      (args.ajaxSettings as any).data = JSON.stringify(data);
    } 
    catch (error) {
      Common.log('Failed to add allowedExtensions to payload:', LogLevel.warning, error);
    }
  }
}

function onFileLoad(args: any) {
  if (!props.allowedExtensions.length)
    return

  const ext = args?.fileDetails?.name?.split('.').pop()?.toLowerCase()

  if (ext && !props.allowedExtensions.map(e => e.toLowerCase()).includes(ext))
    args.cancel = true
}

function onFileSelect(args: any) {
  if (!fileManager.value)
    return

  const selected = (fileManager.value as any).getSelectedFiles?.() ?? []
  const hasSelection = Array.isArray(selected) && selected.length > 0

  if (hasSelection)
    emit('onSelect', selected as FileInfo[])
}

function onFailure(args: FailureEventArgs) {
  setTimeout(() => {
    const span = document.querySelector('.e-file-status.e-upload-fails')
    const err = args.error as any

    if (err && err.statusText && span)
      span.textContent = err.statusText
  }, 0)
}

onMounted(() => {
})
</script>

<template>
  <EjsFileManager
    ref="fileManager"
    :id="id+'-filemanager'"
    :uploadSettings="uploadSettings"
    :ajaxSettings="computedAjaxSettings"
    :allowMultiSelection="multiSelect"
    :showItemCheckBoxes="multiSelect"
    :allowDragAndDrop="allowDragAndDrop"
    :locale="systemInfo.currentLanguage"
    @fileLoad="onFileLoad"
    @fileSelect="onFileSelect"
    @beforeSend="onBeforeSend"
    @failure="onFailure"
  >
     <e-toolbaritems>
      <e-toolbaritem name="NewFolder"></e-toolbaritem>
      <e-toolbaritem name="Upload"></e-toolbaritem>
      <e-toolbaritem name="SortBy"></e-toolbaritem>
      <e-toolbaritem name="Refresh"></e-toolbaritem>
      <e-toolbaritem name="Cut"></e-toolbaritem>
      <e-toolbaritem name="Copy"></e-toolbaritem>
      <e-toolbaritem name="Paste"></e-toolbaritem>
      <e-toolbaritem name="Delete"></e-toolbaritem>
      <!-- Only show Download if in prod -->
      <e-toolbaritem
        v-if="systemInfo.env.production"
        name="Download"
      ></e-toolbaritem>
      <e-toolbaritem name="Rename"></e-toolbaritem>
      <e-toolbaritem name="Selection" v-if="showSelectButton"></e-toolbaritem>
      <e-toolbaritem name="View"></e-toolbaritem>
     </e-toolbaritems>
  </EjsFileManager>
</template>

