<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { FormControlModeEnum, PlacementEnum } from '@/shared/services/form-control-enums'
import { constants } from '@/shared/services/constants'
import { DateTimePickerTypeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store';
import IrisSimpleDatepicker from '@/shared/components/general-controls/IrisSimpleDatePicker.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import DateTimeHelper from '@/shared/services/dateTime-helper'
import ModelBinder from '@/shared/services/model-binder'

const dateTimeHelper = new DateTimeHelper()
const modelBinder = new ModelBinder()

// Props
const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    readOnly: <PERSON><PERSON>an,
    ariaLabel: String,
    dateFormat: String,
    timeFormat: String,
    yearAndMonthFormat: String,
    todayButton : Boolean,
    value: [Date, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    orientation: {
        type: String,
        default: PlacementEnum.bottom,
        validator: (value: string) => {
            return (<any>Object).values(PlacementEnum).includes(value)
        }
    },
    visible: {
        type: Boolean,
        default: true
    },
    type: {
        type: String,
        default: DateTimePickerTypeEnum.date,
        validator: (value: string) => {
            return (<any>Object).values(DateTimePickerTypeEnum).includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    localizeTime: {
        type: Boolean,
        default: false
    },
    autoHide: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: Date | null) => true
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const systemInfo = useIrisSystemInfoStore().getData()

// Refs
const $el = ref<HTMLElement>()
const _value = ref<Date | null>(getValue())
const _required = ref(props.required)
const _readOnly = ref(props.readOnly)
const _label = ref(props.label)
const _mode = ref(props.mode)
const dateValue = ref<string>()
const timeValue = ref<string>()
const yearValue = ref<string>()
const monthValue = ref<string>()

// Types
type formatsType = { dateFormat: string, timeFormat: string, dateTimeFormat: string, yearMonthFormat: string }

// Vars
var formats: formatsType

// Hooks
onMounted(() => {
    if (base.isDupId())
        return

    const formId = base.getParentFormId($el.value!)

    store.add(base.uniqueId, formId, base.getErrors, validate)
    formats = getDateTimePatternsAsDayJsFormat()
    initialize(false)
    deconstructValue()
})

onUnmounted(() => {
    store.remove(base.uniqueId)
})

// Watches
watch(_value, () => {
    if (base.isBound() && _value.value && dateTimeHelper.isValidDateTime(_value.value) && (<any>props.value)[props.field!]?.toString() != _value.value?.toString())
        (<any>props.value)[props.field!] = _value.value

    emits('onChange', _value.value)
    setTimeout(() => {
        // This timeout is required otherwise an error happens when there are more than 100 instances of IrisDateTime component in a page
        validate()
    }, 0)
    deconstructValue()
})

watch(() => (<any>props.value)[props.field!], () => {
    if ((<any>props.value)[props.field!]?.toString() != _value.value?.toString())
        _value.value = new Date((<any>props.value)[props.field!])
})

watch(() => [
    props.value,
    props.field,
    props.required,
    props.readOnly,
    props.label,
    props.mode
], () => initialize(true))

// Functions
function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation)
        validate()
}

function setPropValues() {
    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _readOnly.value = props.readOnly
        _label.value = props.label
        _mode.value = props.mode
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(addErrors: boolean = true): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true

    var errors: ModelError[] = []

    if (_required.value && (!_value.value || !dateTimeHelper.isValidDateTime(_value.value))) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value ?? props.id })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    if (addErrors)
        base.addErrors(errors)

    return result
}

function getValue(): Date | null {
    const value = base.getValue()

    if (!value)
        return null

    const dt = new Date(value)
    let result: Date

    if (base.isBound() && props.localizeTime)
        result = dateTimeHelper.convertToLocal(dt)
    else
        result = dt

    return result
}

function getId() {
    const result: any = {}

    if (props.type == DateTimePickerTypeEnum.date)
        result.dateId = props.id
    else if (props.type == DateTimePickerTypeEnum.time)
        result.timeId = props.id
    else if (props.type == DateTimePickerTypeEnum.dateTime) {
        result.dateId = props.id
        result.timeId = `${props.id}Time`
    }
    else if (props.type == DateTimePickerTypeEnum.yearMonth) {
        result.yearId = props.id
        result.monthId = `${props.id}Month`
    }
        
    return result
}

function isLink() {
    const result = _mode.value == FormControlModeEnum.html && base.isBound() && base.fieldMetadata.value.IsNameField
    return result
}

function getTimeParts(time: string): number[] {
    let newValue = (_value.value && dateTimeHelper.isValidDateTime(_value.value)) ? _value.value : getValue()

    if (!newValue)
        newValue = dateTimeHelper.getNullDateTime()

    const [hour, minute, seconds] = time.split(':')
    var parsedSeconds = newValue.getSeconds()
    var parsedMilliseconds = newValue.getMilliseconds()

    if (seconds) {
        const [s, ms] = seconds.split('.')

        parsedSeconds = parseInt(s)

        if (ms)
            parsedMilliseconds = parseInt(ms)
    }

    const result = [parseInt(hour), parseInt(minute), parsedSeconds, parsedMilliseconds]

    return result
}

function getDateTimePatternsAsDayJsFormat(): formatsType {
    // Date format
    let dateFormat: string

    if (props.dateFormat)
        dateFormat = dateTimeHelper.convertDotNetDateTimeFormatToDayJs(props.dateFormat)
    else if (base.isBound()) {
        const displayFormatString = base.fieldMetadata.value.DisplayFormatString

        if (displayFormatString) {
            const datePartFormatString = dateTimeHelper.splitDisplayFormatString(displayFormatString)[0]
    
            dateFormat = dateTimeHelper.convertDotNetDateTimeFormatToDayJs(datePartFormatString)
        }
        else
            dateFormat = dateTimeHelper.getDateTimePattern().datePattern
    }
    else
        dateFormat = dateTimeHelper.getDateTimePattern().datePattern

    // Time format
    let timeFormat: string

    if (props.timeFormat)
        timeFormat = dateTimeHelper.convertDotNetDateTimeFormatToDayJs(props.timeFormat)
    else if (base.isBound()) {
        const displayFormatString = base.fieldMetadata.value.DisplayFormatString

        if (displayFormatString) {
            const timePartFormatString = dateTimeHelper.splitDisplayFormatString(displayFormatString)[1]

            timeFormat = dateTimeHelper.convertDotNetDateTimeFormatToDayJs(timePartFormatString)
        }
        else
            timeFormat = dateTimeHelper.getDateTimePattern().timePattern
    }
    else
        timeFormat = dateTimeHelper.getDateTimePattern().timePattern

    // Date and Time format
    const dateTimeFormat = `${dateFormat} ${timeFormat}`

    // Year and Month format
    const yearMonthFormat = props.yearAndMonthFormat ? props.yearAndMonthFormat : 'MMMM YYYY'

    return { dateFormat, timeFormat, dateTimeFormat, yearMonthFormat }
}

function deconstructValue() {
    const dt = _value.value
    const [isNullDate, isNullTime] = dateTimeHelper.isNullDateTime(dt)

    dateValue.value = isNullDate ? '' : dateTimeHelper.customFormatDateTime(dt!, formats.dateFormat)
    timeValue.value = isNullTime ? '' : dateTimeHelper.customFormatDateTime(dt!, dateTimeHelper.convertDayJsTimeFormatToHtml5(formats.timeFormat))
    yearValue.value = isNullDate ? '' : dt!.getFullYear().toString()
    monthValue.value = isNullDate ? '' : dt!.getMonth().toString()
}

function reconstructValue(e: any) {
    let newValue = (_value.value && dateTimeHelper.isValidDateTime(_value.value)) ? _value.value : getValue()

    if (!newValue)
        newValue = dateTimeHelper.getNullDateTime()

    if (props.type == DateTimePickerTypeEnum.date) {
        const pattern = dateTimeHelper.convertDayJsDateFormatToFlowbite(formats.dateFormat)
        const date = dateTimeHelper.parse(e.target.value, pattern)

        newValue = new Date(date.getFullYear(), date.getMonth(), date.getDate(), newValue.getHours(), newValue.getMinutes(), newValue.getSeconds(), newValue.getMilliseconds())
    }
    else if (props.type == DateTimePickerTypeEnum.time) {
        const [hour, minute, second, milliSecond] = getTimeParts(e.target.value)

        newValue = new Date(newValue.getFullYear(), newValue.getMonth(), newValue.getDate(), hour, minute, second, milliSecond)
    }
    else if (props.type == DateTimePickerTypeEnum.dateTime) {
        if (e.target.id == getId().dateId) {
            const pattern = dateTimeHelper.convertDayJsDateFormatToFlowbite(formats.dateFormat)
            const date = dateTimeHelper.parse(e.target.value, pattern)

            newValue = new Date(date.getFullYear(), date.getMonth(), date.getDate(), newValue.getHours(), newValue.getMinutes(), newValue.getSeconds(), newValue.getMilliseconds())
        }
        else if (e.target.id == getId().timeId) {
            const [hour, minute, second, milliSecond] = getTimeParts(e.target.value)

            newValue = new Date(newValue.getFullYear(), newValue.getMonth(), newValue.getDate(), hour, minute, second, milliSecond)
        }
    }
    else if (props.type == DateTimePickerTypeEnum.yearMonth) {
        if (e.type == 'year' && e.value)
            newValue = new Date(e.value, newValue.getMonth(), newValue.getDate(), newValue.getHours(), newValue.getMinutes(), newValue.getSeconds())
        else if (e.type == 'month' && e.value)
            newValue = new Date(newValue.getFullYear(), e.value, newValue.getDate(), newValue.getHours(), newValue.getMinutes(), newValue.getSeconds())
    }

    _value.value = newValue
}

function formatValue(): string {
    let fieldMetadata

    switch (props.type) {
        case DateTimePickerTypeEnum.date:
            fieldMetadata = { FieldType: 'Date', DisplayFormatString: formats.dateFormat }
            break
        case DateTimePickerTypeEnum.time:
            fieldMetadata = { FieldType: 'Time', DisplayFormatString: formats.timeFormat }
            break
        case DateTimePickerTypeEnum.dateTime:
            fieldMetadata = { FieldType: 'DateTime', DisplayFormatString: formats.dateTimeFormat }
            break
        case DateTimePickerTypeEnum.yearMonth:
            fieldMetadata = { FieldType: 'Date', DisplayFormatString: formats.yearMonthFormat }
            break
    }

    const result = modelBinder.formatData(_value.value, fieldMetadata, null, false, true)

    return result
}

function getTimeStep(): number {
    let result = 60

    if (!formats)
        return result

    if (formats.timeFormat.includes('S'))
        result = 0.1 // Milliseconds
    else if (formats.timeFormat.includes('s'))
        result = 1 // Seconds

    return result
}
</script>

<template>
    <div ref="$el" v-if="visible">
        <div v-if="!base.isBound() || base.fieldMetadata.value.IsReadable">
            <template v-if="showLabel && _label">
                <div>
                    <label :for="id" class="iris-label">
                        {{ _label }}
                        <span class="iris-required" v-if="_required">
                            <IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon>
                        </span>
                    </label>
                </div>
            </template>

            <template v-if="_mode == 'text' || _mode == 'html'">
                <!-- Text -->
                <span v-if="_mode == 'text'">{{ formatValue() }}</span>
    
                <!-- HTML -->
                <template v-else>
                    <span v-if="isLink()"><a :href="base.getId()" v-html="formatValue()"></a></span>
                    <span v-else v-html="formatValue()"></span>
                </template>
            </template>

            <!-- No Text or Html -->
            <template v-else>
                <!-- Datepicker -->
                <div class="relative" v-if="type == 'date' || type == 'date-time'">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <IrisIcon name="calendar" class="iris-icon"></IrisIcon>
                    </div>
                    <input
                        type="text"
                        datepicker
                        :="{
                            'datepicker-autohide': props.autoHide,
                            'datepicker-buttons': props.todayButton,
                            'datepicker-autoselect-today': true,
                            'datepicker-language': systemInfo.currentLanguage,
                            'datepicker-format': dateTimeHelper.convertDayJsDateFormatToFlowbite(formats.dateFormat),
                            'datepicker-orientation': props.orientation,
                        }"
                        :value="dateValue"
                        :id="getId().dateId"
                        :disabled="_mode == FormControlModeEnum.disabled"
                        :required="_required"
                        :readonly="_readOnly"
                        :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined"
                        :aria-label="ariaLabel"
                        :placeholder="base.languageHelper.getMessage('selectDate')"
                        @changeDate="reconstructValue"
                        class="border border-border-color text-text-color-200 bg-bg-color-200
                        text-sm rounded-lg block w-full ps-10 p-4
                        focus:ring-border-focus focus:border-primary
                        dark:border-border-color-dark dark:text-text-color-200-dark dark:bg-bg-color-200-dark" />
                </div>
    
                <!-- Timepicker -->
                <div class="relative mt-3" v-if="type == 'time' || type == 'date-time'">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <IrisIcon name="clock" class="iris-icon"></IrisIcon>
                    </div>
                    <input
                        type="time"
                        :value="timeValue"
                        :id="getId().timeId"
                        :disabled="_mode == FormControlModeEnum.disabled"
                        :required="_required"
                        :readonly="_readOnly"
                        :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined"
                        :aria-label="ariaLabel"
                        :step="getTimeStep()"
                        @change="reconstructValue"
                        class="iris-input-time border border-border-color text-text-color-200 bg-bg-color-200 
                        text-sm rounded-lg block w-full p-4 ps-10
                        focus:ring-border-focus focus:border-primary 
                        dark:border-border-color-dark dark:text-text-color-200-dark dark:bg-bg-color-200-dark" />
                </div>
    
                <!-- Year Month -->
                <div class="flex mt-3" v-if="type == 'year-month'">
                    <span class="mr-3">
                        <IrisSimpleDatepicker :id="getId().yearId" type="year" :value="yearValue" @on-change="reconstructValue({ type: 'year', value: $event })"></IrisSimpleDatepicker>
                    </span>
                    <span>
                        <IrisSimpleDatepicker :id="getId().monthId" type="month" :value="monthValue" @on-change="reconstructValue({ type: 'month', value: $event })"></IrisSimpleDatepicker>
                    </span>
                </div>
            </template>

            <!-- Hint -->
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>

            <!-- Errors -->
            <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
        </div>
    </div>
</template>