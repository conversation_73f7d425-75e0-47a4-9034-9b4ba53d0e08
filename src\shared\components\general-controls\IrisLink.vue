<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import Common from '@/shared/services/common'

// Props
const props = defineProps({
    path: {
        type: String,
        required: true
    },
    target: {
        type: String,
        default: ''
    },
    secure: {
        type: Boolean,
        default: true
    }
})

// Vars
const router = useRouter()
const _target = ref<string>(props.target)
const _path = ref<string>(props.path)

onMounted(() => {
    initialize()
})

watch(() => [props.target, props.path], () => initialize())

function initialize() {
    setPropValues()
}

function setPropValues() {
    const hasDomain = Common.hasDomain(props.path);
    let isExternalLink = false
    _target.value = props.target

    if (hasDomain) {
        _path.value = Common.includeUrlProtocol(props.path, props.secure)
        isExternalLink = Common.isExternalPath(_path.value)

        if (!isExternalLink) 
           _path.value = Common.getRelativePath(_path.value)
        else if (!_target.value)
            _target.value = '_blank'
    }
    else
        _path.value = props.path

    if (!isExternalLink && !_target.value)
        _target.value = '_self'
}
</script>

<template v-if="_path">
    <RouterLink :to="_path!" :target="_target" :class="$attrs.class" v-if="Common.IsIrisPath(path, router)">
        <template v-if="$slots['default']">
            <slot></slot>
        </template>
        <template v-else>
            {{ path }}
        </template>
    </RouterLink>

    <a :href="_path" :target="_target" :class="$attrs.class" v-else>
        <template v-if="$slots['default']">
            <slot></slot>
        </template>
        <template v-else>
            {{ path }}
        </template>
    </a>
</template>