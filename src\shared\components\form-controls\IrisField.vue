<script setup lang="ts">
import { computed, getCurrentInstance, onMounted } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import IrisCheckbox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisDateTime from '@/shared/components/form-controls/IrisDateTime.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisReference from '@/shared/components/form-controls/IrisReference.vue'
import IrisColor from '@/shared/components/form-controls/IrisColor.vue'
import IrisRadioPicklist from '@/shared/components/form-controls/IrisRadioPicklist.vue'
import IrisRange from '@/shared/components/form-controls/IrisRange.vue'
import IrisGridLink from '@/shared/components/general-controls/IrisGridLink.vue'

const props = defineProps({
    id: {
        type:String,
        required: true
    },
    field: {
        type: String,
        required: true
    },
    value: {
        type : Object,
        required: true
    },
    label: String,
    hint: String,
    mode: String,
    component: String
})

const base = useIrisFormControl(props)

const componentType = computed(() => {
    let requestedComponent = props.component

    if (!requestedComponent)
      requestedComponent = base.fieldMetadata.value.FieldType

    switch (requestedComponent) {
        case "IrisGridLink":
          return IrisGridLink
        case "PicklistMultiSelect":
        case "IrisMultiPicklist":
          return IrisMultiPicklist
        case "Picklist":
        case "IrisPicklist":
          return IrisPicklist
        case "IrisRadioPicklist":
          return IrisRadioPicklist
        case "IrisRange":
          return IrisRange
        case "Text":
        case "Number":
        case "Percent":
        case "Email":
        case "Phone":
        case "TextArea":
        case "Currency":
        case "Url":
        case "Password":
        case "Secret":
        case "IrisTextbox":
          return IrisTextbox
        case "CheckBox":
        case "IrisCheckbox":
          return IrisCheckbox
        case "Lookup":
        case "MasterDetail":
        case "IrisReference":
          return IrisReference
        case "DateTime":
        case "Date":
        case "Time":
        case "IrisDateTime":
          return IrisDateTime
        case "ID":
        case "Formula":
          return null;
        case "Color":
          return IrisColor
        default:
          return IrisTextbox
    }
})

onMounted(() => {
   base.addInputFieldSymbol(getCurrentInstance())
})
</script>
<template>
    <component :is="componentType"
        :id="id"
        :value="value" 
        :field="field" 
        :label="label"
        :hint="hint"
        :mode="mode" />
</template>