<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import DataAccess from '@/shared/services/data-access'
import Common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/shared/services/currency-helper'
import <PERSON><PERSON>elper from '@/shared/services/number-helper'
import CriterionTierStatus from '@/modules/partner-program/components/CriterionTierStatus.vue'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'

// Props received from the parent
const props = defineProps({
  programId: {
    type: String,
    required: true
  },
  partnerAccountId: {
    type: String,
    required: false
  },
  id: {
    type: String,
    required: true
  }
})

const _criteriaData: any = ref(null)
const _benefitsData: any = ref(null)
const _enrollmentData: any = ref(null)
const _tierData: any = ref(null)
const _evaluationData: any = ref(null)
const _benefitsTableData: any = ref(null)
const _tierOverallPercentages = ref<{ [key: string]: number }>({})
const isLoading = ref(false)
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')
const languageHelper = new LanguageHelper()
const isDataValid = computed(() => props.programId && props.partnerAccountId)
const currentTierId = computed(() => _enrollmentData.value?.ProgramTier?.Id || null)
const currentTierBadge = computed(() => _enrollmentData.value?.ProgramTier?.Badge || null)
const currencyhelper = new CurrencyHelper()

const nextTier = computed(() => {
  if (!currentTierId.value || !Array.isArray(_tierData.value) || _tierData.value.length === 0)
    return null

  const currentIndex = _tierData.value.findIndex(
    (tier: any) => tier.Id === currentTierId.value
  )

  if (currentIndex === -1 || currentIndex >= _tierData.value.length - 1)
    return null

  return _tierData.value[currentIndex + 1]
})

const percentageToNextTier = computed(() => {
  if (!nextTier.value)
    return null

  const nextTierCurrentProgress = _tierOverallPercentages.value[nextTier.value.Id]

  if (nextTierCurrentProgress === undefined || typeof nextTierCurrentProgress !== 'number')
    return null

  const remainingPercentage = Math.max(0, 100 - nextTierCurrentProgress)
  return remainingPercentage.toFixed(0)
})

const fetchPartnerStatus = async () => {
  if (!isDataValid.value) {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("missingProgramIdOrPartnerAccountId")
    isLoading.value = false
    return
  }

  try {
    isLoading.value = true
    showErrorMessage.value = false

    const endpoint = `/partnerprogram/getpartnerstatus`
    const data: Record<string, string> = {
      programId: props.programId
    }

    if (props.partnerAccountId)
      data.partnerAccountId = props.partnerAccountId

    const url = Common.constructUrl(endpoint, data)
    const dataAccess = new DataAccess()
    const response = await dataAccess.execute(url, RequestMethod.get)
console.log(response)
    if (!response.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      isLoading.value = false
      return
    }
    _evaluationData.value = response.Data.responseData || []
    _tierData.value = response.Data.tiersList || []
    _criteriaData.value = response.Data.criteriaList || []
    _enrollmentData.value = response.Data.activeEnrollment || []
    _benefitsData.value = response.Data.benefitsList || []

    if (!_evaluationData.value || !_tierData.value || !_criteriaData.value || !_enrollmentData.value) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      isLoading.value = false
      return
    }

    _benefitsTableData.value = Array.from(
      new Map(
        response.Data.benefitsList.map((benefit: any) => [
          benefit.ProgramBenefit.Id,
          { value: benefit.ProgramBenefit.Id, label: benefit.ProgramBenefit.Name }
        ])
      ).values()
    )

    _tierOverallPercentages.value = {}

    if (response.Data.responseData?.TierOverallProgress) {
      response.Data.responseData.TierOverallProgress.forEach((item: any) => {
        _tierOverallPercentages.value[item.TierId] = item.Percentage
      })
    }

    // Ensure all data is valid
    if (
      !_evaluationData.value ||
      !_tierData.value ||
      !_criteriaData.value ||
      !_enrollmentData.value ||
      !_benefitsData.value
    ) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      isLoading.value = false
      return
    }
console.log(_evaluationData)
    isLoading.value = false
  } catch {
    showErrorMessage.value = true
    _benefitsData.value = []
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    isLoading.value = false
  } finally {
    isLoading.value = false
  }
}

const getBenefitValue = (benefitId: string, tierId: string) => {
  if (!_benefitsData.value || _benefitsData.value.length === 0)
    return '-'

  const entry = _benefitsData.value.find(
    (benefit: any) =>
      benefit.ProgramBenefit.Id === benefitId && benefit.ProgramTier.Id === tierId
  )
  if (!entry)
    return ''
  else {
    var benefitType = entry.ProgramBenefit.BenefitType
    if (benefitType === "Revenue") 
      return currencyhelper.formatCurrency(entry.Value, entry.CurrencyIsoCode, undefined, 2)
    else if (benefitType === "Yes/No") {
      if (entry.Value === 1)
        return languageHelper.getMessage("yes")
      else
        return ''
    } else if (benefitType === "Percent") {
      var numberHelper = new NumberHelper()
      return numberHelper.formatNumber(entry.Value, { isPercentage: true })
    } else {
      return entry.value
    }
  }
}

onMounted(() => {
  if (isDataValid.value)
    fetchPartnerStatus()
})

watch(
  [() => props.programId, () => props.partnerAccountId],
  ([newProgramId, newPartnerAccountId]) => {
    if (newProgramId && newPartnerAccountId) 
      fetchPartnerStatus()
  },
)
</script>

<template>
  <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
    {{ serverErrorMessage }}
  </iris-alert>
  <slot name="PartnerProgramDropdown"></slot>
  <div v-if="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
  <div v-else-if="_evaluationData" class="relative overflow-x-auto">
    <!-- Program Info -->
    <h1 class="block pb-5 pt-5 text-lg font-semibold text-left rtl:text-right ">
      <span name="program-name">{{ _enrollmentData.Program.Name }}</span>
    </h1>
    <!-- Criteria Table -->
    <div class="border border-border-color rounded-lg">
      <table class="table-fixed text-sm text-left rtl:text-right ml-3 w-[calc(100%-0.75rem)]">
        <thead class="text-xs">
          <tr class="">
            <th :colspan="(_tierData?.length || 0) + 1" scope="col" class="pt-3 pb-3 px-3">
              <div class="inline-flex items-center space-x-2">
                <img v-if="currentTierBadge" :src="Common.getBaseUrl() + '/Contents/Assets/' + currentTierBadge"
                  alt="Tier Icon" class="h-8 w-8 object-contain left-6" />
                <div class="text-lg font-semibold">
                  {{ _enrollmentData.ProgramTier.Name }}
                </div>
                <div class="text-sm text-text-color-500 dark:text-text-color-500-dark">
                  |
                </div>
                <div class="text-sm text-text-color-500 dark:text-text-color-500-dark">
                  <template v-if="nextTier && percentageToNextTier !== null">
                    {{ percentageToNextTier }}% to {{ nextTier.Name }}
                  </template>
                  <template v-else-if="currentTierId && percentageToNextTier === null && nextTier === null">
                    {{ languageHelper.getMessage("highestTierReached") }}
                  </template>
                  <template v-else>
                    Loading tier info...
                  </template>
                </div>
              </div>
            </th>

          </tr>
          <tr class="border-b border-spacing-y-2">
            <th class=""></th>
            <th scope="col" v-for="tier in _tierData" :key="tier.Id" :class="[
              'py-4 text-center',
              currentTierId === tier.Id
                ? 'bg-bg-color-300 dark:bg-bg-color-300-dark'
                : ''
            ]">
              <div class="flex flex-col items-center space-y-3">
                <!-- Icon: Only rendered if tier.Badge exists -->
                <img v-if="tier.Badge" :src="Common.getBaseUrl() + '/Contents/Assets/' + tier.Badge" alt="Tier Icon"
                  class="h-8 w-8 object-contain" />

                <!-- Text content always centered -->

                <span class="font-semibold">{{ tier.Name }}</span>
                <span class="text-xs text-text-color-500 dark:text-text-color-500-dark">
                  {{ _tierOverallPercentages[tier.Id] !== undefined ? _tierOverallPercentages[tier.Id] + '%' : '0%' }}
                </span>

              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="criterion in _criteriaData" :key="criterion.Id" class="border-b">
            <td class="px-3 py-6 text-left">{{ criterion.Name }}</td>
            <td v-for="tier in _tierData" :key="tier.Id" :class="[
              'px-3 py-6 text-center',
              currentTierId === tier.Id ? 'bg-bg-color-200 dark:bg-bg-color-200-dark' : ''
            ]">
              <criterion-tier-status :criterion="criterion" :tierId="tier.Id"
                :evaluationData="_evaluationData.EvaluationTable" />
            </td>
          </tr>
          <tr>
            <td :colspan="(_tierData?.length || 0) + 1"
              class="inline-flex items-center font-semibold text-lg text-left rtl:text-right pt-6 pb-3 px-3">
              <h2>{{ languageHelper.getMessage("benefits") }}</h2>
            </td>
          </tr>
          <tr v-for="benefit in _benefitsTableData" class="border-t ">
            <td class="px-3 py-6 text-left"> {{ benefit.label }}</td>
            <td v-for="tier in _tierData" :class="[
              'px-3 py-6 text-center',
              currentTierId === tier.Id ? 'bg-bg-color-200 dark:bg-bg-color-200-dark' : ''
            ]"> {{ getBenefitValue(benefit.value, tier.Id) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>