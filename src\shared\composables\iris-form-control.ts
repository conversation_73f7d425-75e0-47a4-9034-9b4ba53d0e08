import { ref, onMounted, type ComponentInternalInstance } from 'vue'
import { irisInputComponentSymbol } from '@/shared/services/iris-input-component-symbol'
import { ModelError } from '@/shared/services/model-error'
import type { DatabaseErrorType } from '@/shared/services/types'
import LanguageHelper from '@/shared/services/language-helper'
import Common from '@/shared/services/common'

export function useIrisFormControl(props: any) {
    const languageHelper = new LanguageHelper()
    const metadata = ref({} as any)
    const fieldMetadata = ref({} as any)
    const errors = ref([] as ModelError[])
    const uniqueId = Common.newGuid()
    let isTouched = false

    onMounted(() => {
        if (isBound()) {
            metadata.value = props.value.__Metadata()
            fieldMetadata.value = getFieldMetadata()
        }
    })

    // Exposed methods
    function isValid(): boolean {
        const valid = getErrors().length == 0
        return valid
    }

    function isBound(): boolean {
        const result = typeof props.value === 'object' && props.field != undefined
        return result
    }

    function getErrors(): ModelError[] {
        var result: ModelError[]

        if (isBound())
            result = props.value.__Errors.filter((x: ModelError) => x.field == props.field)
        else
            result = errors.value

        return result
    }

    function resetErrors() {
        if (isBound())
            props.value.__Errors = props.value.__Errors.filter((x: ModelError) => x.field != props.field)
        else
            errors.value = [] as ModelError[]
    }

    function addErrors(errs: ModelError[]) {
        resetErrors()

        if (isBound())
            props.value.__Errors = props.value.__Errors.concat(errs)
        else
            errors.value = errors.value.concat(errs)
    }

    function addDatabaseErrors(dbErrs: DatabaseErrorType[]) {
        const myErrs = dbErrs.filter(x => x.fieldName == props.field)
        const errs = myErrs.map(x => new ModelError(x.fieldName, x.message))
        
        addErrors(errs)
    }

    function getValue(): any {
        var result = isBound() ? props.value![props.field!] : props.value
        return result
    }

    function getId(): any {
        var result = isBound() ? props.value!["Id"] : props.value
        return result
    }

    function isDupId(verbose: boolean = true): boolean {
        var result = false

        if (props.id) {
            const selector = `[id='${props.id}']`

            result = document.querySelectorAll(selector).length > 1

            if (result && verbose)
                throw new Error(languageHelper.getMessage('duplicateId', { id: props.id }))
        }

        return result
    }

    function getParentFormId(el: HTMLElement): string | null {
        const parent = el?.parentElement

        if (parent == null)
            return null

        if (parent.nodeName == 'FORM')
            return parent.id

        return getParentFormId(parent)
    }

    // Helper methods 
    function getFieldMetadata(): any {
        const matches = metadata.value.Fields.filter((x: any) => x.Name === props.field)

        if (matches.length == 0)
            throw new Error(`'${props.field}' ${languageHelper.getMessage('notKnownProp')}`)

        const result = matches[0]

        return result
    }

    function addInputFieldSymbol(instance : ComponentInternalInstance | null) : void {
        if (instance)
            (instance.type as any)[irisInputComponentSymbol] = true;
    }

    return { 
        languageHelper, 
        metadata,
        fieldMetadata,
        uniqueId,
        isTouched,
        isValid, 
        isBound,
        getErrors, 
        resetErrors, 
        addErrors,
        addDatabaseErrors,
        getValue, 
        getId,
        isDupId, 
        getParentFormId,
        addInputFieldSymbol
    }
}