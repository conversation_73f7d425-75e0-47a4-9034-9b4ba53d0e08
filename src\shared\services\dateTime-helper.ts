import { DateTimeFormat, DayJsUnit, DateTimeStyle } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import toObject from 'dayjs/plugin/toObject'
import isLeapYear from 'dayjs/plugin/isLeapYear'
import isBetween from 'dayjs/plugin/isBetween'
import 'dayjs/locale/fr'
import I18nHelper from '@/shared/services/i18n-helper'
import TimeZones from '@/shared/assets/data/time-zones.json'
import LanguageHelper from '@/shared/services/language-helper'

export default class DateTimeHelper {
    i18nHelper = new I18nHelper()
    languageHelper = new LanguageHelper()
    systemInfo = useIrisSystemInfoStore().getData()

    constructor() {
        this.initDaysJsExtensions()
    }

    initDaysJsExtensions() {
        dayjs.extend(relativeTime)
        dayjs.extend(toObject)
        dayjs.extend(isLeapYear)
        dayjs.extend(isBetween)
    }

    formatDateTime(date: Date, format: DateTimeFormat, locale = this.systemInfo.currentLocale): string {
        const result = this.i18nHelper.formatDateTime(date, format, locale)

        return result
    }

    customFormatDateTime(date: Date, format: string, locale = this.systemInfo.currentLocale): string {
        const result = dayjs(date).locale(locale).format(format)

        return result
    }

    add(date: Date, value: number, unit: DayJsUnit): Date {
        var result = date

        if (value == 0)
            return result

        if (value > 0)
            result = dayjs(date).add(value, unit as any).toDate()
        else
            result = dayjs(date).subtract(Math.abs(value), unit as any).toDate()

        return result
    }

    relativeDateTime(date: Date): string {
        const result = dayjs(date).fromNow()

        return result
    }

    isDst(): boolean {
        // Determine if Daylight Saving Time (DST) is in effect
        const date = new Date()
        const jan = new Date(date.getFullYear(), 0, 1)
        const jul = new Date(date.getFullYear(), 6, 1)
        const max = Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset())
        const result = date.getTimezoneOffset() < max

        return result
    }

    convertToUtc(date: Date): Date {
        const dstAdjustment = this.isDst() ? -1 : 0
        const result = new Date(this.add(date, dstAdjustment, DayJsUnit.hour).getTime() + (date.getTimezoneOffset() * 60000))

        return result
    }

    convertToLocal(utcDate: Date): Date {
        const localDate = new Date()
        const result = new Date(utcDate.getTime() - (localDate.getTimezoneOffset() * 60000))

        return result
    }

    convertToTimeZone(date: Date, timeZone: string): Date {
        const result = new Date(date.toLocaleString(undefined, { timeZone }))

        return result
    }

    difference(date1: Date, date2: Date, unit: DayJsUnit): number {
        const d1 = dayjs(date1)
        const d2 = dayjs(date2)
        const result = d1.diff(d2, unit)

        return result
    }

    daysInMonth(date: Date): number {
        const result = dayjs(date).daysInMonth()

        return result
    }

    toObject(date: Date): any {
        const result = dayjs(date).toObject()

        return result
    }

    isLeapYear(date: Date): boolean {
        const result = dayjs(date).isLeapYear()

        return result
    }

    isBefore(date: Date, unit: DayJsUnit = DayJsUnit.millisecond): boolean {
        const result = dayjs().isBefore(dayjs(date), unit as any)

        return result
    }

    isAfter(date: Date, unit: DayJsUnit = DayJsUnit.millisecond): boolean {
        const result = dayjs().isAfter(dayjs(date), unit as any)

        return result
    }

    isSame(date: Date, unit: DayJsUnit = DayJsUnit.millisecond): boolean {
        const result = dayjs().isSame(dayjs(date), unit as any)

        return result
    }

    isBetween(date: Date, fromDate: Date, toDate: Date, unit: DayJsUnit = DayJsUnit.millisecond): boolean {
        const result = dayjs(date).isBetween(dayjs(fromDate), dayjs(toDate), unit as any)

        return result
    }

    getTimeZones(): any[] {
        return TimeZones
    }

    getTimeZoneAbbrs(): string[] {
        const result = TimeZones.map(x => x.abbr).sort()

        return result
    }

    getTimeZoneByAbbr(abbr: string): any | null {
        const zone = TimeZones.find(x => x.abbr === abbr)
        const result = zone ? zone : null

        return result
    }

    convertTimeStringToDate(time: string): Date {
        if (!time)
            throw new Error(this.languageHelper.getMessage('invalidTime'))

        var today = new Date();
        var dateTimeString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()} ${time}`
        var result = new Date(dateTimeString)

        return result
    }

    isValidDateTime(dateTime: Date) {
        const result = !isNaN(dateTime.getTime())
        
        return result
    }

    //#region Conversion Utils
    convertDotNetDateTimeFormatToDayJs(format: string, purify: boolean = true): string {
        let result = purify ? this.getPureDisplayFormatString(format) : format
        
        // Year
        result = result.replace(/y/g, 'Y')

        // Day
        result = result.replace(/d/g, 'D')
        
        // Fix Day of the Week
        if (result.includes('DDDD'))
            result = result.replace(/DDDD/g, 'dddd')
        else if (result.includes('DDD'))
            result = result.replace(/DDD/g, 'ddd')

        // Milliseconds
        result = result.replace(/f/g, 'S')

        // AM/PM
        if (result.includes('tt'))
            result = result.replace(/tt/g, 'A')
        else if (result.includes('t'))
            result = result.replace(/t/g, 'a')

        // Offset from UTC
        if (result.includes('zzz'))
            result = result.replace(/zzz/g, 'Z')
        else if (result.includes('zz'))
            result = result.replace(/zz/g, 'ZZ')
        else if (result.includes('z'))
            result = result.replace(/z/g, 'ZZ')

        return result
    }

    convertDayJsDateFormatToFlowbite(format: string): string {
        let result = format.replace(/Y/g, 'y').replace(/d/g, '').replace(/D/g, 'd').trim()

        if (result.includes('MMMM'))
            result = result.replace('MMMM', 'MM')
        else if (result.includes('MMM'))
            result = result.replace('MMM', 'M')
        else if (result.includes('MM'))
            result = result.replace('MM', 'mm')
        else if (result.includes('M'))
            result = result.replace('M', 'm')

        return result
    }

    convertDayJsTimeFormatToHtml5(format: string): string {
        let result = 'HH:mm'

        if (format.includes('s'))
            result += ':ss'

        if (format.includes('S'))
            result += '.SSS'

        return result
    }

    getPureDisplayFormatString(displayFormatString: string) {
        const result = displayFormatString.replace('{0:', '').replace('}', '')

        return result
    }

    splitDisplayFormatString(displayFormatString: string): string[] {
        // Splits the DisplayFormatString of a full date-time to 2 parts of date and time format
        const stripLeadingTrailingBadChars = (value: string, validChars: string[]): string => {
            var startIndex = 0, endIndex = 0
    
            Array.from(value).some(x => {
                if (validChars.includes(x))
                    return true
    
                startIndex++
    
                return false
            })
    
            Array.from(value).reverse().some(x => {
                if (validChars.includes(x))
                    return true
    
                endIndex++
    
                return false
            })
        
            var result = value
    
            if (startIndex > 0)
                result = result.substring(startIndex)
    
            if (endIndex > 0)
                result = result.substring(0, result.length - endIndex)
    
            return result.trim()
        }
        
        var datePart = displayFormatString.replace('{0:', '').replace('}', '').replace(/h/g, '').replace(/H/g, '').replace(/m/g, '').replace(/s/g, '').replace(/f/g, '').replace(/F/g, '').replace(/t/g, '').trim()
        var timePart = displayFormatString.replace('{0:', '').replace('}', '').replace(/y/g, '').replace(/M/g, '').replace(/d/g, '').trim()
    
        datePart = stripLeadingTrailingBadChars(datePart, ['y', 'M', 'd'])
        timePart = stripLeadingTrailingBadChars(timePart, ['h', 'H', 'm', 's', 'f', 'F', 't'])
        
        datePart = `{0:${datePart.trim()}}`
        timePart = `{0:${timePart.trim()}}`
    
        return [datePart, timePart]
    }

    getDateTimePattern(locale: string = '', dateStyle: DateTimeStyle = DateTimeStyle.short, timeStyle: DateTimeStyle = DateTimeStyle.short) {
        const currentLocale = locale == '' ? this.i18nHelper.getCurrentLocale() : locale
        const dtfDate = new Intl.DateTimeFormat(currentLocale, { dateStyle })
        const dtfTime = new Intl.DateTimeFormat(currentLocale, { timeStyle })
        const dtfDateTime = new Intl.DateTimeFormat(currentLocale, { dateStyle, timeStyle })
        const dt = new Date('2024-02-07 09:04:06.002 AM')
        const dateParts = dtfDate.formatToParts(dt)
        const timeParts = dtfTime.formatToParts(dt)
        const dateTimeParts = dtfDateTime.formatToParts(dt)
        const hasDayPeriod = timeParts.find(x => x.type == 'dayPeriod') != undefined
        var datePattern = ''
        var timePattern = ''
        var dateTimePattern = ''

        dateParts.forEach(x => datePattern += this.getPatternChar(x, hasDayPeriod))
        timeParts.forEach(x => timePattern += this.getPatternChar(x, hasDayPeriod))
        dateTimeParts.forEach(x => dateTimePattern += this.getPatternChar(x, hasDayPeriod))

        datePattern = datePattern.trim()
        timePattern = timePattern.trim()
        dateTimePattern = dateTimePattern.trim()

        return { datePattern, timePattern, dateTimePattern }
    }

    getNullDateTime() {
        const result = new Date(0, 0, 1)

        return result
    }

    isNullDateTime(dt: Date | null): [Boolean, Boolean] {
        let isNullDate = false, isNullTime = false
    
        if (!dt) {
            isNullDate = true
            isNullTime = true
        }
        else {
            isNullDate = dt.getFullYear() == 1900 && dt.getMonth() == 0 && dt.getDate() == 1
            isNullTime = dt.getHours() == 0 && dt.getMinutes() == 0 && dt.getSeconds() == 0 && dt.getMilliseconds() == 0
        }

        return [isNullDate, isNullTime]
    }

    parse(value: string, pattern: string): Date {
        const result = dayjs(value, pattern).toDate()

        return result
    }
    //#endregion

    private getPatternChar(part: Intl.DateTimeFormatPart, hasDayPeriod: boolean): string {
        var result = ''
        
        if (part.type == 'weekday')
            if (part.value.length == 1)
                result = 'd'
            else if (part.value.length == 2)
                result = 'dd'
            else if (part.value.length == 3)
                result = 'ddd'
            else
                result = 'dddd'
        else if (part.type == 'month')
            if (part.value.length == 1)
                result = 'M'
            else if (part.value.length == 2)
                result = 'MM'
            else if (part.value.length == 3)
                result = 'MMM'
            else
                result = 'MMMM'
        else if (part.type == 'day')
            result = part.value.length == 2 ? 'DD' : 'D'
        else if (part.type == 'year')
            result = part.value.length == 4 ? 'YYYY' : 'YY'
        else if (part.type == 'hour') {
            const char = hasDayPeriod ? 'h' : 'H'

            result = char.repeat(part.value.length)
        }
        else if (part.type == 'minute')
            result = part.value.length == 2 ? 'mm' : 'm'
        else if (part.type == 'second')
            result = part.value.length == 2 ? 'ss' : 's'
        else if (part.type == 'dayPeriod')
            result = part.value == part.value.toUpperCase() ? 'A' : 'a'
        else if (part.type == 'literal')
            result = part.value

        return result
    }
}
