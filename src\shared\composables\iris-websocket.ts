import { LogLevel, WebSocketMessageType, WebSocketClientType } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import { WebSocketSubscriber } from '@/shared/services/websocket-subscriber'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'

export function useIrisWebSocket() {
    let socket: WebSocket
    const systemInfoStore = useIrisSystemInfoStore()
    const systemInfo = systemInfoStore.getData()
    
    async function initialize() {
        if (!isConnected()) {
            const dataAccess = new DataAccess()
            const websocketInfo = await dataAccess.execute('iris/getwebsocketinfo')
            const token = await getJwtToken(websocketInfo)

            if (!token) {
                Common.log('Jwt token for websocket is not available', LogLevel.error)
                return
            }

            if (websocketInfo.url) {
                var url = `${websocketInfo.url}/api/iriswebsocket/connect?token=${token}`

                socket = new WebSocket(url)
                socket.onopen = e => connect()
                socket.onclose = e => disconnect()
                socket.onmessage = (e: any) => processMessage(e.data)
                socket.onerror = err => Common.log(`WebSocket: ${err}`, LogLevel.error)
            }
        }
    }

    async function getJwtToken(websocketInfo: any) {
        const storageKey = 'IrisWebSocketJwtToken'
        let json = localStorage.getItem(storageKey)
        let token = ''

        if (json) {
            const value = JSON.parse(json)
            const minutesPassed = Math.floor((new Date().getTime() - value.creationTime) / 60000)

            if (minutesPassed < 720)
                token = value.token
        }

        if (token)
            return token

        // Get new token
        let url = websocketInfo.url.replace('wss://', 'https://') + '/api/iriswebsocket/login'
        const options: any = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'ApiKey': websocketInfo.apiKey
            }
        }
        const response = await fetch(url, options)
        const result = await response.text()

        if (result) {
            const data = {
                creationTime: new Date().getTime(),
                token: result
            }

            localStorage.setItem(storageKey, JSON.stringify(data))
        }

        return result
    }

    function connect() {
        Common.log("WebSocket: Connected", LogLevel.information)

        const host = Common.getBackendHostName()
        const message = { 
            MessageType: WebSocketMessageType.HandShake, 
            ClientType: WebSocketClientType.Client, 
            SiteUrl: host, 
            OrgCode: systemInfo.company.system.orgCode,
            UserId: systemInfo.userId
        }
        
        const json = JSON.stringify(message)

        sendMessage(json)
    }

    function disconnect() {
        if (isConnected()) {
            socket.close()
            Common.log("WebSocket: Disconnect", LogLevel.information)
        }
    }

    async function processMessage(json: string) {
        if (!json) {
            Common.log('WebSocket: Invalid Push message', LogLevel.warning)
            return
        }

        const message = JSON.parse(json)

        if (message.ClientType != WebSocketClientType.Server)
            return

        Common.log('WebSocket: Message Received', LogLevel.information, message)

        WebSocketSubscriber.subscribers.forEach(s => {
            const relevantMessages = message.PushMessages.filter((m: any) => m.Channel == s.channel)

            relevantMessages.forEach(async (m: any) => {
                if (s.isAsync == true)
                    if (!m.UserId)
                        await s.callback()
                    else if (m.UserId == systemInfo.userId)
                        await s.callback()
                else
                    if (!m.UserId)
                        s.callback()
                    else if (m.UserId == systemInfo.userId)
                        s.callback()
            })
        })
    }
    
    function sendMessage(message: string) {
        if (isConnected() && message) {
            socket.send(message)
            Common.log(`WebSocket: Message Sent => ${message}`, LogLevel.information)
        }
    }
    
    function isConnected(): boolean {
        const result = socket && socket.readyState == socket.OPEN
        return result
    }

    return { initialize, disconnect }
}