import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from "vitest"
import { createP<PERSON>, setActivePinia } from "pinia"
import { DayJsUnit, DateTimeFormat, DateTimeStyle } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import DateTimeHelper from "@/shared/services/dateTime-helper"
import type SystemInfoData from "@/shared/services/system-info-data"

describe("DateTimeHelper", () => {
    const dateTime = new Date('15 December 2023 1:39:08.524 PM')
    let dateTimeHelper: DateTimeHelper
    let systemInfo: SystemInfoData

    beforeAll(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
        systemInfo = useIrisSystemInfoStore().getData()
        dateTimeHelper = new DateTimeHelper()
        vi.spyOn(systemInfo, 'currentLocale', 'get').mockReturnValue('en-CA')
    })

    beforeEach(() => {
        vi.useFakeTimers()
    })

    afterEach(() => {
        vi.useRealTimers()
    })

    it("Format date for the current locale (shortDate)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.shortDate)

        expect(result).toEqual('Dec 15, 2023')
    })

    it("Format date for the current locale (shortTime)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.shortTime)

        expect(result).toEqual('1:39 p.m.')
    })

    it("Format date for the current locale (longDate)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.longDate)

        expect(result).toEqual('Friday, December 15, 2023')
    })

    it("Format date for the current locale (longTime)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.longTime)

        expect(result).toEqual('1:39:08 p.m.')
    })

    it("Format date for the current locale (longTimeMs)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.longTimeMs)

        expect(result).toEqual('1:39:08.524 p.m.')
    })

    it("Format date for the current locale (shortDateTime)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.shortDateTime)

        expect(result).toEqual('Dec 15, 2023, 1:39 p.m.')
    })

    it("Format date for the current locale (longDateTime)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.longDateTime)

        expect(result).toEqual('Friday, December 15, 2023 at 1:39:08 p.m.')
    })

    it("Format date for the current locale (longDateTimeMs)", () => {
        const result = dateTimeHelper.formatDateTime(dateTime, DateTimeFormat.longDateTimeMs)

        expect(result).toEqual('Friday, December 15, 2023 at 1:39:08.524 p.m.')
    })

    it("Add date", () => {
        const result = dateTimeHelper.add(dateTime, 2, DayJsUnit.day).toString()

        expect(result).toEqual('Sun Dec 17 2023 13:39:08 GMT-0500 (Eastern Standard Time)')
    })

    it("Relative Date Time", () => {
        const date = dateTimeHelper.add(new Date(), -2, DayJsUnit.day)
        const result = dateTimeHelper.relativeDateTime(date)

        expect(result).toEqual('2 days ago')
    })

    it("Convert to UTC", () => {
        const isDst = dateTimeHelper.isDst()
        const result = dateTimeHelper.convertToUtc(dateTime).toString()
        const dstHour = isDst ? '17' : '18'
        expect(result).toEqual(`Fri Dec 15 2023 ${dstHour}:39:08 GMT-0500 (Eastern Standard Time)`)
    })

    it("Convert to local", () => {
        const utc = dateTimeHelper.convertToUtc(dateTime)
        const result = dateTimeHelper.convertToLocal(utc).toString()

        expect(result).toEqual('Fri Dec 15 2023 13:39:08 GMT-0500 (Eastern Standard Time)')
    })

    it("Convert to PST timezone", () => {
        const result = dateTimeHelper.convertToTimeZone(dateTime, 'PST').toString()

        expect(result).toEqual('Fri Dec 15 2023 10:39:08 GMT-0500 (Eastern Standard Time)')
    })

    it("Difference", () => {
        const date = dateTimeHelper.add(new Date(), -2, DayJsUnit.day)
        const result = dateTimeHelper.difference(date, new Date(), DayJsUnit.day)

        expect(result).toEqual(-2)
    })

    it("Days in month", () => {
        const result = dateTimeHelper.daysInMonth(dateTime)

        expect(result).toEqual(31)
    })

    it("Convert to JSON object", () => {
        const json = dateTimeHelper.toObject(dateTime)
        const result = json.years == 2023 && json.months == 11 && json.date == 15 && json.hours == 13 && json.minutes == 39 && json.seconds == 8 && json.milliseconds == 524

        expect(result).toEqual(true)
    })

    it("Check for leap year", () => {
        const result = dateTimeHelper.isLeapYear(dateTime)

        expect(result).toEqual(false)
    })

    it("Check if todays date is before the supplied date (By milliseconds)", () => {
        const date = dateTimeHelper.add(new Date(), -1, DayJsUnit.minute)
        const result = dateTimeHelper.isBefore(date)

        expect(result).toEqual(false)
    })

    it("Check if todays date is after the supplied date (By milliseconds)", () => {
        const date = dateTimeHelper.add(new Date(), 1, DayJsUnit.minute)
        const result = dateTimeHelper.isAfter(date)

        expect(result).toEqual(false)
    })

    it("Check if todays date is after the supplied date (By month)", () => {
        const date = dateTimeHelper.add(new Date(), -1, DayJsUnit.minute)
        const result = dateTimeHelper.isAfter(date, DayJsUnit.month)

        expect(result).toEqual(false)
    })

    it("Check if todays date is the same as the supplied date (By hour)", () => {
        const date = dateTimeHelper.add(new Date(), -1, DayJsUnit.hour)
        const result = dateTimeHelper.isSame(date, DayJsUnit.hour)

        expect(result).toEqual(false)
    })

    it("Check if todays date is the same as the supplied date (By day)", () => {
        const date = dateTimeHelper.add(new Date(), -1, DayJsUnit.hour)
        const result = dateTimeHelper.isSame(date, DayJsUnit.day)

        expect(result).toEqual(true)
    })

    it("Check if a specified date is between from and to dates (By day)", () => {
        const dateTime = new Date('15 December 2023 1:39:08 PM')
        const dateTimeFrom = new Date('14 December 2023 1:39:08 PM')
        const dateTimeTo = new Date('16 December 2023 1:39:08 PM')
        const result = dateTimeHelper.isBetween(dateTime, dateTimeFrom, dateTimeTo, DayJsUnit.day)

        expect(result).toEqual(true)
    })

    it("Check if a specified date is between from and to dates (By month)", () => {
        const dateTime = new Date('15 December 2023 1:39:08 PM')
        const dateTimeFrom = new Date('14 December 2023 1:39:08 PM')
        const dateTimeTo = new Date('16 December 2023 1:39:08 PM')
        const result = dateTimeHelper.isBetween(dateTime, dateTimeFrom, dateTimeTo, DayJsUnit.month)

        expect(result).toEqual(false)
    })

    it("Check time zones", () => {
        const result = dateTimeHelper.getTimeZones().length

        expect(result).toBeGreaterThan(0)
    })

    it("Check time zones abbrs", () => {
        const result = dateTimeHelper.getTimeZoneAbbrs().length

        expect(result).toBeGreaterThan(0)
    })

    it("Get time zone by abbr (EST)", () => {
        const result = dateTimeHelper.getTimeZoneByAbbr('EST').value

        expect(result).toEqual('Eastern Standard Time')
    })

    it("Custom format", () => {
        const format = 'YYYY-MMM-DD @ hh:mm A'
        const result = dateTimeHelper.customFormatDateTime(dateTime, format)

        expect(result).toEqual('2023-Dec-15 @ 01:39 PM')
    })

    it("Convert DotNet date/time format to DayJs", () => {
        const format = '{0:dddd yyyy-MMM-dd hh:mm:ss.fff tt}'
        const result = dateTimeHelper.convertDotNetDateTimeFormatToDayJs(format)

        expect(result).toEqual('dddd YYYY-MMM-DD hh:mm:ss.SSS A')
    })

    it("Convert DayJs date format to Flowbite (MMMM)", () => {
        const format = 'dddd YYYY-MMMM-DD'
        const result = dateTimeHelper.convertDayJsDateFormatToFlowbite(format)

        expect(result).toEqual('yyyy-MM-dd')
    })

    it("Convert DayJs date format to Flowbite (MMM)", () => {
        const format = 'dddd YYYY-MMM-DD'
        const result = dateTimeHelper.convertDayJsDateFormatToFlowbite(format)

        expect(result).toEqual('yyyy-M-dd')
    })

    it("Convert DayJs date format to Flowbite (MM)", () => {
        const format = 'dddd YYYY-MM-DD'
        const result = dateTimeHelper.convertDayJsDateFormatToFlowbite(format)

        expect(result).toEqual('yyyy-mm-dd')
    })

    it("Convert DayJs date format to Flowbite (M)", () => {
        const format = 'dddd YYYY-M-DD'
        const result = dateTimeHelper.convertDayJsDateFormatToFlowbite(format)

        expect(result).toEqual('yyyy-m-dd')
    })

    it("Convert DayJs time format to HTML 5", () => {
        const format = 'dddd YYYY-M-DD hh:mm:ss.SSS A'
        const result = dateTimeHelper.convertDayJsTimeFormatToHtml5(format)

        expect(result).toEqual('HH:mm:ss.SSS')
    })

    it("Pure DisplayFormatString", () => {
        const format = '{0:yyyy-MMM-dd hh:mm:ss tt}'
        const result = dateTimeHelper.getPureDisplayFormatString(format)

        expect(result).toEqual('yyyy-MMM-dd hh:mm:ss tt')
    })

    it("Split DisplayFormatString", () => {
        const format = '{0:yyyy-MMM-dd hh:mm:ss tt}'
        const [datePart, timePart] = dateTimeHelper.splitDisplayFormatString(format)
        const result = `${datePart} ${timePart}`

        expect(result).toEqual('{0:yyyy-MMM-dd} {0:hh:mm:ss tt}')
    })

    it("Get date and time pattern (short)", () => {
        const patterns = dateTimeHelper.getDateTimePattern()
        const result = `${patterns.datePattern} ${patterns.timePattern} ${patterns.dateTimePattern}`

        expect(result).toEqual('M/D/YY h:mm A M/D/YY, h:mm A')
    })

    it("Get date and time pattern (medium)", () => {
        const patterns = dateTimeHelper.getDateTimePattern('', DateTimeStyle.medium, DateTimeStyle.medium)
        const result = `${patterns.datePattern} ${patterns.timePattern} ${patterns.dateTimePattern}`

        expect(result).toEqual('MMM D, YYYY h:mm:ss A MMM D, YYYY, h:mm:ss A')
    })

    it("Get date and time pattern (long)", () => {
        const patterns = dateTimeHelper.getDateTimePattern('', DateTimeStyle.long, DateTimeStyle.long)
        const result = `${patterns.datePattern} ${patterns.timePattern} ${patterns.dateTimePattern}`

        expect(result).toEqual('MMMM D, YYYY h:mm:ss A MMMM D, YYYY at h:mm:ss A')
    })

    it("Get date and time pattern (fr-CA + short)", () => {
        const patterns = dateTimeHelper.getDateTimePattern('fr-CA')
        const result = `${patterns.datePattern} ${patterns.timePattern} ${patterns.dateTimePattern}`

        expect(result).toEqual('YYYY-MM-DD HH h mm YYYY-MM-DD HH h mm')
    })

    it("Convert time string to date", () => {
        const time = '09:54 pm'
        const dt = dateTimeHelper.convertTimeStringToDate(time)
        const result = dt.getHours() == 21 && dt.getMinutes() == 54

        expect(result).toEqual(true)
    })

    it("Valid date time", () => {
        const result = dateTimeHelper.isValidDateTime(dateTime)

        expect(result).toEqual(true)
    })

    it("Invalid date time", () => {
        const badDateTime = new Date('Bad Date')
        const result = dateTimeHelper.isValidDateTime(badDateTime)

        expect(result).toEqual(false)
    })

    it("Custom format by locale", () => {
        const result = dateTimeHelper.customFormatDateTime(dateTime, 'YYYY-MMMM-DD HH:mm:ss', 'fr-CA')

        expect(result).toEqual('2023-décembre-15 13:39:08')
    })

    it("getNullDateTime() + isNullDateTime()", () => {
        const dt = dateTimeHelper.getNullDateTime()
        const [isNullDate, isNullTime] = dateTimeHelper.isNullDateTime(dt)
        const result = isNullDate && isNullTime

        expect(result).toEqual(true)
    })

    it("isNullDateTime(null)", () => {
        const [isNullDate, isNullTime] = dateTimeHelper.isNullDateTime(null)
        const result = isNullDate && isNullTime

        expect(result).toEqual(true)
    })

    it("isNullDateTime() with valid date", () => {
        const [isNullDate, isNullTime] = dateTimeHelper.isNullDateTime(dateTime)
        const result = !isNullDate && !isNullTime

        expect(result).toEqual(true)
    })

    it("parse", () => {
        const date = dateTimeHelper.parse('2025-04-14', 'yyyy-mm-dd')
        const result = date.getFullYear() == 2025 && date.getMonth() == 3 && date.getDate() == 14

        expect(result).toEqual(true)
    })
})