<script setup lang="ts">
import { onMounted, ref, watch, type PropType } from 'vue'
import { useRouter } from 'vue-router'
import { Dropdown, type DropdownOptions } from 'flowbite'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Common from '@/shared/services/common'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import DataAccess from '@/shared/services/data-access'

interface AppItem {
    id: string;
    launchUrl: string;
    selected: boolean;
    name: string;
    imageUrl: string;
}

// Props
const props = defineProps({
    value: {
        type: Array as PropType<Array<AppItem>>,
        required: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// constants & refs
const systemInfo = useIrisSystemInfoStore().getData()
const componentId = `al${Common.newGuid()}`
const isVisible = ref(true)
const $dropdownMenu = ref<HTMLElement>()
const $dropdownBtn = ref<HTMLElement>()
let dropdown : Dropdown
const router = useRouter()

watch(() => [props.value, isVisible], () => initialize())

onMounted(() => { 
    initialize()
})

function initialize() {
    const options: DropdownOptions = {
        placement: 'bottom',
        triggerType: 'click'
    }

    dropdown = new Dropdown($dropdownMenu.value, $dropdownBtn.value, options, { id:componentId, override: true });
    isVisible.value = !systemInfo.userInfo.user.guest && (props.value != null && props.value.length > 1)
}

async function launchApp(id: string) {
    var dataAccess = new DataAccess()

    // for backward compatibility
    const result = await dataAccess.execute("/iris/loadapp", { appId: id })

    // change the app inside Iris:
    dropdown.hide()

    if (Common.IsIrisPath(result.landingTabUrl, router)) {
        systemInfo.loadData()
        emits('onChange', id)
        router.push(result.landingTabUrl)
    }
    else
        window.top!.location.href = result.landingTabUrl
}
</script>
<template>
    <div v-if="isVisible">
        <button 
            ref="$dropdownBtn"
            :id="'btn' + componentId" 
            type="button"
            class="appLink flex items-center gap-1"
            :class="$attrs.class">
            <IrisIcon name="grid" aria-hidden="true" width="1.5rem" height="1.5rem"/>
        </button>
        <div ref="$dropdownMenu" 
            :id="componentId"
            data-popper-placement="bottom"
            class="z-10 hidden bg-bg-color divide-y divide-border-muted rounded-lg shadow overflow-hidden border border-border-color dark:border-border-color-dark dark:divide-border-muted-dark"
            :class="{ 
                'w-48': (value.length <= 10),
                'w-96': value.length > 10
            }">
            <ul class="py-2 text-sm text-text-color dark:text-text-color-dark"
                :class = "{
                    'grid grid-cols-2 gap-2' : (value.length > 10)
                }"
                >
                <li v-for="(app, index) in value" :key="index">
                    <a @click="launchApp(app.id)" 
                        :class="{
                            'flex items-center px-4 py-2 hover:bg-bg-color-300 cursor-pointer dark:hover:bg-bg-color-300-dark': true,
                            'bg-bg-color-200 dark:bg-bg-color-200-dark': app.selected
                            }"
                            >
                        <img :src="Common.getBaseUrl() + app.imageUrl" v-if="app.imageUrl" class="h-7 flex-initial" crossorigin="anonymous" />
                        <span :title="app.name" class='text-responsive ml-3'>{{ app.name }}</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</template>
