const messages = {
    invalidCurrency: "Ungültiger Währungscode: {currencyCode}",
    endpointRequired: "Endpunkt ist erforderlich",
    queryRequired: "Abfrage ist erforderlich",
    idRequired: "ID ist erforderlich",
    entityNameRequired: "Entitätsname ist erforderlich",
    dataRequired: "Daten sind erforderlich",
    invalidTime: "Ungültiges Zeitformat",
    pingFailed: "Ping fehlgeschlagen!",
    invalidTimeout: "Ungültige Zeitüberschreitung",
    createUserSessionFailed: "Benutzersitzung konnte nicht erstellt werden",
    isRequired: "{label} ist erforderlich",
    notKnownProp: "ist keine bekannte Eigenschaft",
    invalidProp: "Ungültiger Wert für die Eigenschaft [{prop}]",
    invalidLookupField: "Ungültiges Nachschlagefeld [{field}]",
    invalidMaxLength: "Die maximale Länge für die Eigenschaft [{prop}] beträgt {length} Zeichen",
    pageTitleRequired: "Seitentitel ist erforderlich",
    pageTitleMaxLength: "Die maximale Länge des Seitentitels beträgt {maxTitleLength} Zeichen",
    invalidItem: "Ungültiger {item}",
    invalidMaxRange: "Der Maximalwert sollte größer als der Minimalwert sein.",
    invalidRange: "Der Wert muss zwischen {min} und {max} liegen.",
    remove: "Entfernen",
    select: "Auswählen",
    selectAll: "Alle auswählen",
    close: "Schließen",
    search: "Suche",
    showAllResults: 'Alle Ergebnisse für "{search}" anzeigen',
    loading: "Wird geladen...",
    recentRecords: "Kürzliche Aufzeichnungen",
    required: "Erforderlich",
    duplicateId: "Doppelte ID: {id}",
    results: "Ergebnisse",
    all: "Alle",
    getWithData: "Das Modell wird mit der GET-Anfrage ignoriert.",
    singleSelectedColumnOnly: "Es kann nur eine einzelne Spalte zur Sortierung ausgewählt werden.",
    months: "Januar,Februar,März,April,Mai,Juni,Juli,August,September,Oktober,November,Dezember",
    year: "Jahr",
    month: "Monat",
    day: "Tag",
    selectDate: "Datum auswählen",
    error: "Fehler",
    emailsDisabled: "Alle E-Mails sind auf diesem Portal deaktiviert. Weitere Informationen finden Sie in der Dokumentation.",
    noDataToDisplay : "Keine Daten zum Anzeigen."
}

export default messages