import { describe, it, expect, beforeAll } from "vitest"
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from "pinia"
import Number<PERSON><PERSON><PERSON> from "@/shared/services/number-helper"

describe("NumberHelper", async () => {
    let numberHelper: NumberHelper

    beforeAll(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
        numberHelper = new NumberHelper()
    })

    it("Format number for the current locale (minimumIntegerDigits)", () => {
        const options = { minimumIntegerDigits: 3 }
        const result = numberHelper.formatNumber(12.99, options)

        expect(result).toEqual('012.99')
    })

    it("Format number for the current locale (minimumFractionDigits)", () => {
        const options = { minimumFractionDigits: 3 }
        const result = numberHelper.formatNumber(12.99, options)

        expect(result).toEqual('12.990')
    })

    it("Format number for the current locale (maximumFractionDigits)", () => {
        const options = { maximumFractionDigits: 1 }
        const result = numberHelper.formatNumber(12.56, options)

        expect(result).toEqual('12.6')
    })

    it("Format number for the current locale (minimumSignificantDigits)", () => {
        const options = { minimumSignificantDigits: 6 }
        const result = numberHelper.formatNumber(12.56, options)

        expect(result).toEqual('12.5600')
    })

    it("Format number for the current locale (maximumSignificantDigits)", () => {
        const options = { maximumSignificantDigits: 3 }
        const result = numberHelper.formatNumber(12.56, options)

        expect(result).toEqual('12.6')
    })

    it("Format number for the current locale with percentage mode", () => {
        const result = numberHelper.formatNumber(12.56, { isPercentage : true })

        expect(result).toEqual('1,256%')
    })
})