import Sortable from 'sortablejs'

export default class IrisSortable extends EventTarget {
    private sortable: Sortable | undefined = undefined

    // Props
    group: string | { name: String, pull: any, put: any, revertClone: Boolean } | undefined = undefined
    sort: Boolean = true
    delay: number = 0
    delayOnTouchOnly: boolean = false
    touchStartThreshold: number = 1
    disabled: boolean = false
    store: any = undefined
    animation: number = 0
    easing: string | undefined = undefined
    handle: string | undefined = undefined
    filter: string | undefined = undefined
    preventOnFilter: boolean = true
    draggable: string | undefined = undefined
    dataIdAttr: string = 'data-id'
    ghostClass: string = 'sortable-ghost'
    chosenClass: string = 'sortable-chosen'
    dragClass: string = 'sortable-drag'
    swapThreshold: number = 1
    invertSwap: boolean = false
    invertedSwapThreshold: number | undefined = undefined
    direction: string | undefined = undefined
    forceFallback: boolean = false
    fallbackClass: string = 'sortable-fallback'
    fallbackOnBody: boolean = false
    fallbackTolerance: number = 0
    dragoverBubble: boolean = false
    removeCloneOnHide: boolean = true
    emptyInsertThreshold: number = 5

    // Functions
    setData: (dataTransfer: DataTransfer, dragEl: HTMLElement) => void = (dataTransfer, dragEl) => dataTransfer.setData("Text", dragEl.textContent!)

    create(el: HTMLElement) {
        const options: any = {
            // Props
            group: this.group,
            sort: this.sort,
            delay: this.delay,
            delayOnTouchOnly: this.delayOnTouchOnly,
            touchStartThreshold: this.touchStartThreshold,
            disabled: this.disabled,
            store: this.store,
            animation: this.animation,
            easing: this.easing,
            handle: this.handle,
            filter: this.filter,
            preventOnFilter: this.preventOnFilter,
            dataIdAttr: this.dataIdAttr,
            ghostClass: this.ghostClass,
            chosenClass: this.chosenClass,
            dragClass: this.dragClass,
            swapThreshold: this.swapThreshold,
            invertSwap: this.invertSwap,
            invertedSwapThreshold: this.invertedSwapThreshold,
            direction: this.direction,
            forceFallback: this.forceFallback,
            fallbackClass: this.fallbackClass,
            fallbackOnBody: this.fallbackOnBody,
            fallbackTolerance: this.fallbackTolerance,
            dragoverBubble: this.dragoverBubble,
            removeCloneOnHide: this.removeCloneOnHide,
            emptyInsertThreshold: this.emptyInsertThreshold,

            // Functions
            setData: this.setData,

            // Events
            onChoose: (evt: Event) => this.raiseEvent('onChoose', evt),
            onUnchoose: (evt: Event) => this.raiseEvent('onUnchoose', evt),
            onStart: (evt: Event) => this.raiseEvent('onStart', evt),
            onEnd: (evt: Event) => this.raiseEvent('onEnd', evt),
            onAdd: (evt: Event) => this.raiseEvent('onAdd', evt),
            onUpdate: (evt: Event) => this.raiseEvent('onUpdate', evt),
            onSort: (evt: Event) => this.raiseEvent('onSort', evt),
            onRemove: (evt: Event) => this.raiseEvent('onRemove', evt),
            onFilter: (evt: Event) => this.raiseEvent('onFilter', evt),
            onMove: (evt: Event, orgEvt: any) => this.raiseEvent('onMove', evt, orgEvt),
            onClone: (evt: Event) => this.raiseEvent('onClone', evt),
            onChange: (evt: Event) => this.raiseEvent('onChange', evt)
        }

        if (this.draggable)
            options.draggable = this.draggable

        this.sortable = Sortable.create(el, options)
    }

    toArray(): string[] | undefined {
        const result = this.sortable?.toArray()

        return result
    }

    sortItems(order: string[], useAnimation: boolean) {
        this.sortable?.sort(order, useAnimation)
    }

    destroy() {
        this.sortable?.destroy()
    }

    private raiseEvent(eventName: string, ...eventData: Event[]) {
        if (eventData.length == 0)
            throw new Error('Missing event arguments.')

        const evt = eventData[0]
        let event

        if (eventName == 'onMove') {
            if (eventData.length < 2)
                throw new Error('Missing event arguments.')

            const orgEvt = eventData[1]

            event = new CustomEvent(eventName, { detail: { evt, orgEvt } })
        }
        else
            event = new CustomEvent(eventName, { detail: { evt } })

        this.dispatchEvent(event)
    }
}