import { inject } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import type { Pinia } from 'pinia'
import TestRoutes from '@/play-ground/router/index'
import SharedRoutes from '@/shared/router/index'
import GlobalSearchRoutes from '@/modules/global-search/router/index'
import AiAssistantRoutes from '@/modules/ai-assistant/router/index'
import SystemInfoData from '@/shared/services/system-info-data'
import partnerProgramGuards from '@/modules/partner-program/router/guards'
import PartnerProgramRoutes from '@/modules/partner-program/router/index'
import Auth from '@/shared/services/auth'

const sysInfo = new SystemInfoData()

let routes: any[] = [
  ...GlobalSearchRoutes,
  ...AiAssistantRoutes,
  ...SharedRoutes,
  ...PartnerProgramRoutes
]

if (!sysInfo.env.production)
  routes = [ ...routes, ...TestRoutes ]

const router = createRouter({
  history: createWebHistory('/'),
  routes
})

router.beforeEach(async (to, from) => {
  const pinia = inject('pinia', 'NA') as Pinia | string

  if (pinia == 'NA')
    return true

  if (from.name && to.meta?.layout != from.meta?.layout) {
    // User is switching between main site and setup menu or the other way around
    window.location.href = to.fullPath
    return false
  }

  if (to.name === "not-found" && to.params.pathMatch) {
    const invalidPath = Array.isArray(to.params.pathMatch)
        ? to.params.pathMatch.join('/')
        : to.params.pathMatch

    // Check if the path starts with "/setup/"
    // Dynamically add meta variable
    if (invalidPath.startsWith("setup/")) 
      to.meta.layout = 'Setup'
  }

  const systemInfoStore = useIrisSystemInfoStore(pinia as Pinia)
  const systemInfo = systemInfoStore.getData()
  const requiresAuth = to.meta?.requiresAuth == undefined ? true : to.meta?.requiresAuth

  if (!systemInfo.company)
  {
    if (!systemInfo.env.production)
      await Auth.createSession(systemInfo.env.refreshToken)

    await systemInfo.loadData()
  }

  if (requiresAuth && !systemInfo.userId)
    return false

  const partnerProgramSuccess = partnerProgramGuards(to, systemInfo, router)

  if (!partnerProgramSuccess)
    window.location.href = '/'

  return true
})

export default router
