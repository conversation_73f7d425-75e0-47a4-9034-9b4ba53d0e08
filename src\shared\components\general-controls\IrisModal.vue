<script setup lang="ts">
import { onMounted, ref, useSlots, watch } from 'vue'
import { Modal, type ModalInterface, type ModalOptions, type modalPlacement } from 'flowbite'
import { constants } from '@/shared/services/constants'
import { HorizontalAlignmentEnum, ModalPlacementEnum, ModalSizeEnum } from '@/shared/services/form-control-enums'
import LanguageHelper from '@/shared/services/language-helper'

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    title: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    showCloseButton: {
        type: Boolean,
        default: true
    },
    show : {
        type: Boolean,
        required: true
    },
    staticBackdrop: {
        type: Boolean,
        default: false
    }, 
    closable: {
        type: Boolean,
        default: true
    },
    size: {
        type: String,
        default: "medium",
        validator: (value: string) => {
            return (<any>Object).values(ModalSizeEnum).includes(value)
        }
    },
    bodyFullHeight: {
        type: Boolean,
        default: false
    },
    bodyPadding: {
        type: Boolean,
        default: true
    },
    placement: {
        type: String,
        default: "center",
        validator: (value: string) => {
            return (<any>Object).values(ModalPlacementEnum).includes(value)
        }
    },
    buttonsAlignment: {
        type: String,
        default: "right",
        validator: (value: string) => {
            return (<any>Object).values(HorizontalAlignmentEnum).includes(value)
        }
    } 
})


const emits = defineEmits(["onShow","onHide","onToggle"])
const languageHelper = new LanguageHelper()
const $el = ref<HTMLElement>()
const $modalBody = ref<HTMLElement>()
const slots = useSlots();

//props.placement as modalPlacement
// options with default values
const options : ModalOptions = {
    placement: props.placement as modalPlacement,
    backdrop: (props.staticBackdrop)? 'static' : 'dynamic',
    backdropClasses: 'bg-text-color-400 dark:bg-base-400-dark opacity-50 fixed inset-0 z-40',
    closable: props.closable,
    onHide: () => {
        emits('onHide')
    },
    onShow: () => {
        emits('onShow')
    },
    onToggle: () => {
        emits('onToggle')
    }
}

// instance options object
const instanceOptions = {
  id: props.id,
  override: true
};

const _modal = ref<ModalInterface>()
const _show = ref(props.show)

onMounted(() => {
    _modal.value = new Modal($el.value, options, instanceOptions);
    initialize()

    if (props.bodyFullHeight)
        adjustBodyHeight()
})

function initialize() {
    _show.value = props.show
}

watch(_show, () => {
    if (_show.value == true)
        _modal.value!.show();
    else
        _modal.value!.hide();
})

watch(() => [props.show], () => initialize())

function closeModal(): void {
    _show.value = false
}

function getViewportHeightInRem(): number {
    // Get the height of the viewport in pixels
    const viewportHeightPx = window.innerHeight;

    // Get the root element's font-size (in pixels)
    const rootFontSize = getComputedStyle(document.documentElement).fontSize;

    // Parse the root font size to a number
    const rootFontSizePx = parseFloat(rootFontSize);

    // Calculate the height of the viewport in rem units
    const viewportHeightRem = viewportHeightPx / rootFontSizePx;

    return viewportHeightRem;
}

function adjustBodyHeight() : void {
    const bodyHeightRem = getViewportHeightInRem()
    // deduct the padding around modal and modal body padding (top and bottom)
    let height = bodyHeightRem - (2 + 3)

    if (props.title)
        height = height - 2.75

    // deduct the padding around footer and button padding and text
    if (!!slots.footer)
        height = height - (1.25 + 0.625 + 0.875)

    if ($modalBody.value)
        $modalBody.value.style.height = `${height}rem`;
}
</script>

<template>
<div
    ref="$el"
    :id="id"
    v-show="show"
    tabindex="-1"
    
    class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full overflow-y-auto overflow-x-hidden p-4 md:inset-0"
>
    <div class="relative max-h-full w-full" 
        :class = "{
            'max-w-md': props.size == ModalSizeEnum.small,
            'max-w-2xl': props.size == ModalSizeEnum.medium,
            'max-w-4xl': props.size == ModalSizeEnum.large,
            'max-w-7xl': props.size == ModalSizeEnum.extraLarge
        }">
        <!-- Modal content -->
        <div class="relative rounded-lg bg-bg-color shadow drop-shadow dark:bg-bg-color-dark">
            <!-- Modal header -->
            <div v-if="title != null && title != ''"
                class="flex items-start justify-between rounded-t border-b p-5">
                <h3 class="text-xl font-semibold text-text-color-200 lg:text-2xl dark:text-text-color-200-dark">
                    {{ title }}
                </h3>
                <button
                    v-if="showCloseButton"
                    @click="closeModal"
                    type="button"
                    class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm 
                    text-pageB-base-400 hover:bg-bg-color-300 hover:text-text-color-200
                    dark:text-pageB-base-400-dark dark:hover:bg-bg-color-300-dark dark:hover:text-text-color-200-dark"
                >
                    <svg
                        class="h-3 w-3"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 14 14"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                        />
                    </svg>
                    <span class="sr-only">{{ languageHelper.getMessage('close') }}</span>
                </button>
            </div>
            <!-- Modal body -->
            <div ref="$modalBody" :class="bodyPadding ? 'p-6' : ''">
                <slot name="content"></slot>
            </div>
            <!-- Modal footer -->
            <div
                v-if="$slots.footer"
                class="flex gap-1 space-x-4 rtl:space-x-reverse rounded-b border-t border-border-color p-6 dark:border-border-color-dark"
                :class="{ 
                    'justify-end' : buttonsAlignment == HorizontalAlignmentEnum.right,
                    'justify-center' : buttonsAlignment == HorizontalAlignmentEnum.center,
                    'justify-start' : buttonsAlignment == HorizontalAlignmentEnum.left
                 }"
            >
                <slot name="footer"></slot>
            </div>
        </div>
    </div>
</div>
</template>