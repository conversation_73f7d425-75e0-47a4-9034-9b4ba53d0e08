import { describe, it, expect, beforeAll } from "vitest"
import { createPinia, setActivePinia } from "pinia"
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import Auth from "@/shared/services/auth"

describe("Auth", async () => {
    beforeAll(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
    })

    it("createSession", async () => {
        const systemInfo = useIrisSystemInfoStore().getData()
        const result = await Auth.createSession(systemInfo.env.refreshToken)
        
        expect(result.success).toEqual(true)
    })
})