<script setup lang="ts">
import { getCurrentInstance, onMounted, onUnmounted, ref, watch } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { constants } from '@/shared/services/constants'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { ModelError } from '@/shared/services/model-error'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    placeHolder: String,
    isNullable: {
        type: Boolean,
        default: true 
    },
    value: [String, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const $el = ref<HTMLElement>()
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _placeHolder = ref(`--${base.languageHelper.getMessage('select').toUpperCase()}--`)
const isVisible = ref(true)

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable
    const formId = base.getParentFormId($el.value!)
    store.add(base.uniqueId, formId, base.getErrors, validate)

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)
        initialize(false)
    }
})

watch(_value, () => {

    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)
    }
})

watch(() => [
    props.value,
    props.field,
    props.required,
    props.label, 
    props.mode,
    props.hint
], () => initialize(true))

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation)
        validate()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label

        if (base.fieldMetadata.value.IsReadOnly)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
    }

    if (props.placeHolder) {
        _placeHolder.value = props.placeHolder
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true

    var errors: ModelError[] = []

    if (_required.value && !_value.value) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    base.addErrors(errors)

    return result
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="mode == FormControlModeEnum.text">
            {{ _value }}
        </template> 
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label"> 
                {{ _label }} 
                <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
            </label>

            <div v-if="mode == FormControlModeEnum.html && _value">
                <span class="inline-flex items-center h-10 w-14 p-1 rounded border border-border-color">
                    <span class="p-1 h-5 w-14 block border border-border-color" :style='{ backgroundColor: _value }'></span>
                </span>
            </div>
            <div v-if="mode != FormControlModeEnum.html" class="relative">
                <input type="color" 
                    class="p-1 h-10 w-14 block bg-bg-color border border-border-color cursor-pointer rounded-lg disabled:border-border-muted disabled:cursor-not-allowed dark:bg-bg-color-dark dark:border-border-color-dark dark:disabled:border-border-muted-dark"
                    :class="{
                            'border-border-color hover:border-border-focus dark:border-border-color-dark': mode != FormControlModeEnum.disabled
                            }"
                    :id="id" 
                    v-model="_value" 
                    :disabled="mode == FormControlModeEnum.disabled"
                    title="Choose your color"
                >

                <!-- Errors -->
                <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
            </div>
        </template>
    </div>
</template>