<script setup lang="ts">
import { AlertTypeEnum } from '@/shared/services/form-control-enums'

const props = defineProps({
    type: {
        type: String,
        default: AlertTypeEnum.primary,
        validator: (value: string) => {
            return (<any>Object).values(AlertTypeEnum).includes(value)
        }
    }
})

const typeClass = "iris-alert-" + props.type 
</script>

<template>
    <div :class="typeClass">
        <slot></slot>
    </div>
</template>