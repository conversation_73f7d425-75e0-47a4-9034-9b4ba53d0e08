const messages = {
    invalidCurrency: "Codice valuta non valido: {currencyCode}",
    endpointRequired: "Endpoint richiesto",
    queryRequired: "Query richiesta",
    idRequired: "ID richiesto",
    entityNameRequired: "Nome dell'entità richiesto",
    dataRequired: "Dati richiesti",
    invalidTime: "Formato dell'orario non valido",
    pingFailed: "Ping fallito!",
    invalidTimeout: "Timeout non valido",
    createUserSessionFailed: "Creazione della sessione utente non riuscita",
    isRequired: "{label} è richiesto",
    notKnownProp: "non è una proprietà conosciuta",
    invalidProp: "Valore non valido per la proprietà [{prop}]",
    invalidLookupField: "Campo di ricerca non valido [{field}]",
    invalidMaxLength: "La lunghezza massima per la proprietà [{prop}] è di {length} caratteri",
    pageTitleRequired: "<PERSON><PERSON> della pagina richiesto",
    pageTitleMaxLength: "La lunghezza massima per il titolo della pagina è di {maxTitleLength} caratteri",
    invalidItem: "{item} non valido",
    invalidMaxRange: "Il valore massimo deve essere maggiore del valore minimo.",
    invalidRange: "Il valore deve essere compreso tra {min} e {max}.",
    remove: "Rimuovere",
    select: "Seleziona",
    selectAll: "Seleziona tutto",
    close: "Chiudere",
    search: "Cerca",
    showAllResults: 'Mostra tutti i risultati per "{search}"',
    loading: "Caricamento...",
    recentRecords: "Record recenti",
    required: "Richiesto",
    duplicateId: "ID duplicato: {id}",
    results: "Risultati",
    all: "Tutti",
    getWithData: "Il modello verrà ignorato con la richiesta GET.",
    singleSelectedColumnOnly: "Può essere selezionata solo una colonna per l'ordinamento.",
    months: "Gennaio,Febbraio,Marzo,Aprile,Maggio,Giugno,Luglio,Agosto,Settembre,Ottobre,Novembre,Dicembre",
    year: "Anno",
    month: "Mese",
    day: "Giorno",
    selectDate: "Seleziona data",
    error: "Errore",
    emailsDisabled: "Tutte le e-mail sono disabilitate su questo portale. Per riabilitarle, consultare la documentazione.",
    noDataToDisplay : "Nessun dato da visualizzare."
}

export default messages