<script setup lang="ts">
import {  ref, onMounted, watch } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'

const props = defineProps({
id: String,
field: String,
value: { 
    type: [String, Object],
    required: true
},
forId : String
})

// Composables
const base = useIrisFormControl(props)
const _label = ref(null)

// Hooks
onMounted(() => {
    initialize()
})

watch(() => [props.value, props.field], () => initialize())

// Functions
function initialize() {
    setPropValues()
}

function setPropValues() {

    if (base.isBound())
        _label.value = base.fieldMetadata.value.Label
    else
        _label.value = base.getValue()
}
</script>

<template>
    <label :id="id" :for="forId" class="iris-label">{{ _label }}</label>
</template>