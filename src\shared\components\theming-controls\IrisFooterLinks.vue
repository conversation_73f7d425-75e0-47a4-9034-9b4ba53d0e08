<script setup lang="ts">
import type { PropType } from 'vue';

interface FooterLink {
    link: string;
    target: string;
    text: string;
}

interface FooterLinkData {
    termsLink: FooterLink;
    privacyLink: FooterLink;
    poweredByLink: FooterLink;
    copyRight: string;
}

const props = defineProps({
    value: {
        type: Object as PropType<FooterLinkData>,
        required: true
    }
})

</script>
<template>
    <div class="footer-legal-bits">
        <a :href="value.termsLink.link" class="sitefooter-link-item" :class="$attrs.class"
            rel="nofollow" :target='value.termsLink.target'>{{ value.termsLink.text }}</a> 
        <span> - </span>
        <a :href="value.privacyLink.link" class="sitefooter-link-item" :class="$attrs.class"
            rel="nofollow" :target='value.privacyLink.target'>{{ value.privacyLink.text }}</a>
    </div>
    <div class="footer-copyright pt-1">{{ value.copyRight }}</div>
    <div class="footer-poweredby pt-1" v-if="value.poweredByLink.link">
        <span>{{ value.poweredByLink.text }}&nbsp;</span> 
        <a :href="value.poweredByLink.link" :class="$attrs.class" rel="nofollow" target="_blank">Magentrix</a>
    </div>
</template>