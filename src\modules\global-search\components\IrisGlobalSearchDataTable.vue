<script setup lang="ts">
import { onMounted, onUpdated, ref, watch } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import { GridColumn } from '@/shared/services/grid-column'
import { initDropdowns } from 'flowbite'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'
import IrisPagination from '@/shared/components/general-controls/IrisPagination.vue'
import LanguageHelper from '@/shared/services/language-helper'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    entityId: String,
    entity: String,
    selectedEntityId: String,
    area: String,
    size: Number,
    pageIndex: Number,
    term: String,
    sortExperssion: String,
    sortDir: String,
    enablePagination: {
        type: Boolean,
        default: false
    },
    pageChanged: Function,
    refresh: Boolean
})

const models = ref([])
const columns = ref<GridColumn[]>()
const resultRows = ref('')
const totalRows = ref(0)
const _pageIndex = ref(props.pageIndex)
const isSortByRelevance = ref(false)
const languageHelper = new LanguageHelper()
const dropdownElement = ref<HTMLDivElement | null>(null)
let searchError = ref('')
let isFirstLoad = false
let isLoading = ref(false)

const emits = defineEmits(["onLoaded", "columnClicked", "refreshCompleted"])

onMounted(() => {
    isSortByRelevance.value = true
    isFirstLoad = true
    loadData(props.pageIndex)
})

onUpdated(() => {
    initDropdowns()
})

watch(() => props.refresh, (newVal) => {
  if (newVal) {
    // Perform refresh logic here
    loadData(props.pageIndex)
    // Emit the refresh completion event when done
    emits('refreshCompleted')
  }
});

watch(() => [props.size, props.term, props.pageIndex, props.sortExperssion, props.sortDir], () => {
    if (!isFirstLoad)
        loadData(props.pageIndex);
})

watch(() => [_pageIndex], () => {

    if (props.pageChanged)
       props.pageChanged(_pageIndex.value);
})

function getSortExprDir() : String {
    const selectedCol = getSortValues()

    if (selectedCol.sortExpr)
        return `${selectedCol.sortExpr}-${selectedCol.sortDir}`
    else
        return ''
}

function getSortValues() : { sortExpr: string, sortDir: string } {
    let result = { 
        sortExpr : props.sortExperssion ?? "",
        sortDir: props.sortDir ?? ""
    }

    if (isFirstLoad) {
        if (props.sortExperssion) {
            result.sortExpr = props.sortExperssion
            result.sortDir = props.sortDir ?? ""
            isSortByRelevance.value = false
            return result
        }
    }

    return result
}

async function loadData(pinx = _pageIndex.value) {
    isLoading.value = true
    var endpoint = `/${props.entity}/getglobalsearchresult`

    if (props.area)
        endpoint = `/${props.area}${endpoint}`

    const data: any = {
        term: props.term,
        srt: getSortExprDir(),
        entityId: props.entityId,
        size: props.size,
        pinx: pinx,
        useDefaultSort: isSortByRelevance.value
    }

    const url = Common.constructUrl(endpoint, data)
    const dataAccess = new DataAccess()
    let searchResult = null

    try {
        searchResult = await dataAccess.execute(url, null, RequestMethod.get)
        _pageIndex.value = pinx
    }
    catch (err: any) {
        isLoading.value = false
        searchError.value = err.message
        isFirstLoad = false
        return
    }

    isLoading.value = false
    isFirstLoad = false
    columns.value = searchResult.columns
    models.value = searchResult.models
    totalRows.value = resultRows.value = searchResult.totalRows

    if (props.size == 5 && totalRows.value > 10)
        resultRows.value = '10+'

    const sortValues = getSortValues()
    
    const eventData = {
        entityId: props.entityId,
        entity: props.entity,
        pageIndex: pinx,
        pageSize: props.size,
        sortExpr: sortValues.sortExpr,
        sortDir: sortValues.sortDir,
        totalRows: totalRows.value
    }

    emits('onLoaded', eventData)
}

function tableColHeaderClicked(selectedColumn: GridColumn | null, changeDirection = true) : void {
    if (columns.value) {
        const selectedCol = columns.value.find(col => col.name == selectedColumn?.name)    
        const isAlreadySelected = selectedCol?.selected

        columns.value.forEach(f => f.selected = false)
    
        if (selectedCol) {
            selectedCol.selected = true

            if (isAlreadySelected && changeDirection)
                selectedCol.sortDirection = (selectedCol.sortDirection == "A")? "D" : "A"
        }
    }

    isSortByRelevance.value = false
    _pageIndex.value = 0

    const eventData = {
        entityId: props.entityId,
        entity: props.entity,
        pageIndex: _pageIndex.value,
        sortExpr: selectedColumn?.sortExpression,
        sortDir: selectedColumn?.sortDirection,
        pageSize: 10
    }

    emits('columnClicked', eventData)

    // Hide the dropdown
    if (dropdownElement.value)
        dropdownElement.value.classList.add('hidden')
}

function sortByRelevance() {
    isSortByRelevance.value = true
    _pageIndex.value = 0

    if (columns.value)
        columns.value.forEach(f => f.selected = false)

    const eventData = {
        entityId: props.entityId,
        entity: props.entity,
        pageIndex: _pageIndex.value,
        sortExpr: '',
        sortDir: 'A',
        pageSize: 10
    }

    emits('columnClicked', eventData)

    // Hide the dropdown
    if (dropdownElement.value)
        dropdownElement.value.classList.add('hidden')
}

function getSortFieldLabel () : String {
    if (columns.value) {
        const selectedCol = columns.value.find(f => f.selected == true)

        if (selectedCol)
            return selectedCol.label
        else
            return languageHelper.getMessage('relevance')
    }

    return ''
}

function showMore() : void {
    if (!getSortFieldLabel () || getSortFieldLabel () == languageHelper.getMessage('relevance')) {
        sortByRelevance()
        return
    }
    else if (columns.value) {
        var selectedColumn = columns.value.find(f => f.label == getSortFieldLabel ()) || null
        tableColHeaderClicked(selectedColumn, false)
    }
}
</script>

<template>
    <div v-if="models && columns && !isLoading">
        <div v-if="totalRows > 0" class="result-table-info text-xs mb-4 pl-3">
            <span>{{resultRows}} {{ languageHelper.getMessage('results') }}</span>
            <span> - {{languageHelper.getMessage('sortedBy')}}</span>

            <button 
                :id="`${id}_sortDropdownBtn`" 
                :data-dropdown-toggle="`${id}_sortDropdown`"
                data-dropdown-placement="bottom" 
                data-dropdown-offset-distance="5" 
                class="font-medium px-2 text-center inline-flex items-center" 
                type="button">
                {{ getSortFieldLabel() }}
                <svg class="w-2 h-2 ms-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                </svg>
            </button>

            <div :id="`${id}_sortDropdown`" ref="dropdownElement" class="z-10 hidden border border-border-color bg-bg-color rounded shadow overflow-hidden w-44 dark:border-border-color-dark dark:bg-bg-color-dark">
                <ul class="py-2 text-sm" :aria-labelledby="`${id}_sortDropdownBtn`">
                    <li>
                        <a class="block px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark" href="javascript:void(0)" @click.prevent="sortByRelevance()">{{ languageHelper.getMessage('relevance') }}</a>
                    </li>
                    <li v-for='(column, index) in columns'>
                        <a class="block px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark" href="javascript:void(0)" @click.prevent="tableColHeaderClicked(column)">{{column.label}}</a>
                    </li>
                </ul>
            </div>
        </div>
        <div v-if="totalRows > 0" class="result-table-detail mb-8 overflow-hidden">
            <IrisGrid
                v-if="models"
                :id="`${id}_grid`" 
                :value="models"
                :cols="columns!"
                :showBorder="false"
                :showRounding="false"
                :showHeaderBackground="false"
                @onSort="tableColHeaderClicked"
                >
            </IrisGrid>
            <div class="mt-5" v-show="!enablePagination && totalRows > 5">
                <a class="pl-3 text-sm font-bold text-primary hover:text-primary-hover" href="javascript:void(0)" @click.prevent="showMore()">
                    {{ languageHelper.getMessage('showMore') }}
                    <IrisIcon name="arrow-up-right" class="iris-icon"></IrisIcon>
                </a>
            </div>
            <div class="mt-5" v-if="enablePagination && totalRows > 10">
                <iris-pagination v-if="enablePagination"
                    :page-changed="loadData"
                    :total="totalRows"
                    :page-size="size" 
                    :current-page="_pageIndex"
                    :use-icons-only="true"
                    :next-and-previous-only="false" 
                    :size="'sm'"
                    :first-btn-lbl="'First'"
                    :last-btn-lbl="'Last'" 
                    :next-btn-lbl="'Next'" 
                    :prev-btn-lbl="'Previous'"></iris-pagination>
            </div>
        </div>
        <div class="p-3 text-text-color-400 dark:text-text-color-400-dark" v-if="totalRows == 0">
            <span>{{ languageHelper.getMessage('noDataToDisplay') }}</span>
        </div>
    </div>
    <div class="placeholder-block item-search-result-placeholder animate-pulse" v-show="isLoading && !searchError">
        <div class="result-table-info-placeholder mb-4 pl-3 w-32 h-4 bg-bg-color-300"></div>
        <div :class="'result-table-detail mb-8 bg-bg-color-300 ' + (props.size == 5 ? 'h-72' : 'h-[36rem]')"></div>
    </div>
    <div v-if="searchError && !models && !columns">
        <div class="text-red-600 mb-8 pl-3">
            {{ searchError }}
        </div>
    </div>
</template>