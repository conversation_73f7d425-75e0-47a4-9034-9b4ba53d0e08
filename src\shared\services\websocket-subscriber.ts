import Common from "@/shared/services/common";
import { WebSocketChannel } from "@/shared/services/enums";

export class WebSocketSubscriber {
    public static subscribers: any[] = []

    public static add(channel: WebSocketChannel, callback: Function, isAsync: Boolean = false) : string {
        const id = Common.newGuid()
        WebSocketSubscriber.subscribers.push({ id, channel, callback, isAsync })

        return id
    }

    public static remove(subscriberId : string) {
        WebSocketSubscriber.subscribers = WebSocketSubscriber.subscribers.filter(s => s.id != subscriberId)
    }
} 