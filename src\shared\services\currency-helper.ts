import { CurrencySymbolFormat } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import I18nHelper from '@/shared/services/i18n-helper'
import NumberHelper from '@/shared/services/number-helper'
import LanguageHelper from '@/shared/services/language-helper'
import Currencies from '@/shared/assets/data/currencies.json'

export default class CurrencyHelper {
    i18nHelper = new I18nHelper()
    numberHelper = new NumberHelper()
    languageHelper = new LanguageHelper()
    countryCurrencies: any[] = Currencies
    systemInfo = useIrisSystemInfoStore().getData()

    formatCurrency(value: number, 
        currencyCode: string = this.systemInfo.preferredCurrency, 
        locale: string = this.systemInfo.currentLocale,
        decimalPlaces: number | undefined = undefined,
        includeConvertedRate: boolean = this.systemInfo.isDisplayUserCurrency): string {
        
        const sign = this.systemInfo.isMultiCurrency ? currencyCode : this.getCurrencySign(currencyCode)

        // If currency code is not provided, system assumes company's default currency.
        if (!currencyCode)
            currencyCode = this.systemInfo.preferredCurrency

        if (this.systemInfo.isMultiCurrency && !decimalPlaces) {
            const exchangeInfo = this.systemInfo.currencyExchangeRates.find(x => x.CurrencyIsoCode.toLowerCase() == currencyCode.toLowerCase())
      
            if (exchangeInfo)
              decimalPlaces = exchangeInfo.DecimalPlaces
        }

        const amount = this.numberHelper.formatNumber(value, { 
            locale,
            minimumFractionDigits: decimalPlaces,
            maximumFractionDigits: decimalPlaces
        } as any)

        var separator = this.systemInfo.isMultiCurrency ? ' ' : ''
        var result = `${sign}${separator}${amount}`

        if (this.systemInfo.isMultiCurrency 
            && includeConvertedRate 
            && currencyCode.toLowerCase() !== this.systemInfo.preferredCurrency.toLowerCase()) {

            const exchangeInfo = this.systemInfo
                .currencyExchangeRates
                .find(x => x.CurrencyIsoCode.toLowerCase() == this.systemInfo.preferredCurrency.toLowerCase())

            if (exchangeInfo) {
                const userValue = this.convertCurrency(value, currencyCode, this.systemInfo.preferredCurrency)
                const userDecimalPlaces = exchangeInfo.DecimalPlaces
                const userSign = exchangeInfo.CurrencyIsoCode
                const userAmount = this.numberHelper.formatNumber(userValue, { 
                    locale: this.systemInfo.currentLocale,
                    minimumFractionDigits: userDecimalPlaces, 
                    maximumFractionDigits: userDecimalPlaces
                })

                result += ` (${userSign}${separator}${userAmount})`
            }
        }

        return result
    }

    getCurrencySign(currencyCode: string = this.systemInfo.preferredCurrency): string | null {
        
        const result = !this.systemInfo.isMultiCurrency || currencyCode == this.systemInfo.preferredCurrency
            ? this.getCurrencySymbolByCurrencyIsoCode(currencyCode)
            : this.getCurrencySymbolByCurrencyIsoCode(currencyCode, CurrencySymbolFormat.code) + ' '
        
        return result
    }

    convertCurrency(value: number, fromCode: string, toCode: string): number {
        if (value == null || fromCode == toCode)
            return value

        const fromCurrency =  this.systemInfo.currencyExchangeRates.find(x => x.CurrencyIsoCode == fromCode)

        if (!fromCurrency)
            throw new Error(this.languageHelper.getMessage('invalidCurrency', { currencyCode: fromCode }))

        const toCurrency =  this.systemInfo.currencyExchangeRates.find(x => x.CurrencyIsoCode == toCode)

        if (!toCurrency)
            throw new Error(this.languageHelper.getMessage('invalidCurrency', { currencyCode: toCode }))

        const result = (value / fromCurrency.ConversionRate) * toCurrency.ConversionRate

        return result
    }

    getCurrencySymbolByLocale(locale: string | null = null, format: CurrencySymbolFormat = CurrencySymbolFormat.symbol): string {
        const result = this.i18nHelper.getCurrencySymbolByLocale(locale, format)

        return result
    }

    getCurrencySymbolByCurrencyIsoCode(currencyCode: string, format: CurrencySymbolFormat = CurrencySymbolFormat.symbol): string | null {
        // format: {symbol | code}
        const locales = this.i18nHelper.getLocalesByCurrencyIsoCode(currencyCode)

        if (locales.length == 0)
            return currencyCode

        const locale = locales[0]
        const result = this.getCurrencySymbolByLocale(locale, format)

        return result
    }

    getCountryCodeByCurrencyIsoCode(currencyCode: string) : string | null {
       const item = this.countryCurrencies.find(f => f.currency_code == currencyCode)

       if (item != null)
        return item.country_code

       return null
    }
}