<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Player from '@vimeo/player'
import common from '@/shared/services/common'

const props = defineProps({
    src : {
        type: String,
        required: true,
        validator: (value : string) => {
            return true
        }
    },
    poster: {
        type: String,
        validator: (value : string) => {
            return true
        }
    },
    visible : {
        type: Boolean,
        default: true
    },
    autoplay : {
        type: Boolean,
        default: false
    },
    controls : {
        type: Boolean,
        default: true
    },
    allowFullSreen : {
        type: Boolean,
        default: true
    },
    allowSeeking: {
        type: Boolean,
        default: true
    },
    play: {
        type: Boolean,
        default : false
    }
})

const $video = ref<HTMLVideoElement>()
const $vimeo = ref<HTMLIFrameElement>()
const $youtube = ref<HTMLIFrameElement>()
const _play = ref(props.play)
const _videoType = ref("mp4")
const _videoSource = ref(props.src)
const eventListeners = new Array<string>()
let vimeoPlayer: Player | null = null
let scriptLoaded : boolean
let youTubePlayer: any = null
let uniqueId: string = "YT" +common.newGuid().replace(/-/g, '')

const emits = defineEmits({
    onPlay: (e: Event) => true,
    onEnded: (e: Event) => true,
    onPause: (e : Event) => true,
    onError: (e : Event) => true
})

onMounted(() => {
    initialize(true)
})

watch(_play, () => {

    if (_videoType.value == "mp4") {

        if (_play.value)
            $video.value?.play()
        else
            $video.value?.pause()

    } else if (_videoType.value == "Vimeo" && vimeoPlayer != null) {

        if (_play.value)
            vimeoPlayer.play()
        else
            vimeoPlayer.pause();

    }
})

watch(() => [ props.play, props.visible, props.src ], () => initialize())

function initialize(isInitial : Boolean = false) : void {

    // detect type:
    const source = props.src.toLocaleLowerCase()

    if (source.indexOf('youtube') > 0 || source.indexOf('utube') > 0)
        _videoType.value = "YouTube"
    else if (source.indexOf('play.vimeo.com') > 0 || source.indexOf('vimeo.com') > 0)
        _videoType.value = "Vimeo"
    else 
        _videoType.value = "mp4"

    if (_videoType.value == "mp4") {
        _videoSource.value = props.src

        if (onerror)
            addEventListenerOnce($video.value!, 'error', errorEvent)

        addEventListenerOnce($video.value!, 'play', playEvent)
        addEventListenerOnce($video.value!, 'pause', pauseEvent)
        addEventListenerOnce($video.value!, 'ended', endedEvent)

        if (!props.allowSeeking) {
            let previousTime = 0;

            // Listen for the seeking event
            addEventListenerOnce($video.value!, 'seeking', () => {
                // Reset the video's current time to its previous value
                $video.value!.currentTime = previousTime;
            })

            // Update the previous time whenever the video's time updates
            addEventListenerOnce($video.value!,'timeupdate', () => {
                previousTime = $video.value?.currentTime!;
            });
        }

    } else if (_videoType.value == "YouTube") {
        const videoId = getYouTubeVideoId(props.src)
        
        loadScript("https://www.youtube.com/iframe_api")
            .then(() => {
                // Script loaded successfully, now you can use its functionalities
                (window as any).onYouTubeIframeAPIReady = () => {
                    const currentURLWithoutQueryString: string = window.location.origin + window.location.pathname;
                    //_videoSource.value = `https://www.youtube.com/embed/${videoId}?origin=${encodeURIComponent(currentURLWithoutQueryString)}`;
                    youTubePlayer = new (window as any).YT.Player(uniqueId, {
                        videoId,
                        events: {
                            'onReady': function() {
                            },
                            'onStateChange': function(e : Event) {
                            }
                        }
                    })
                }
            })
    } else if (_videoType.value == "Vimeo") {
        const regex = /\/(\d+)$/; // Match one or more digits at the end of the URL
        const match = props.src.match(regex);

        if (match) {
            const videoId = parseInt(match[1]);
            setTimeout(() => {
                vimeoPlayer = new Player($vimeo.value!, {
                    id: videoId,
                    autoplay: props.autoplay,
                    controls: props.controls,
                    byline: false,
                    portrait: false
                });

                addEventListenerOnce(vimeoPlayer, 'play', playEvent)
                addEventListenerOnce(vimeoPlayer, 'pause', pauseEvent)
                addEventListenerOnce(vimeoPlayer, 'ended', endedEvent)

                if (onerror)
                    addEventListenerOnce(vimeoPlayer, 'error', errorEvent)    

                let previousTime = 0;

                // Listen for the timeupdate event
                addEventListenerOnce(vimeoPlayer, 'timeupdate', function(data: { seconds: number; }) {
                    // Check if the current time is greater than the previous time
                    if (data.seconds > previousTime) {
                        // Reset the playback time to the previous value
                        vimeoPlayer!.setCurrentTime(previousTime).catch(function(error) {
                            console.error('Error resetting playback time:', error);
                        });
                    } else {
                        // Update the previous time
                        previousTime = data.seconds;
                    }
                })
            }, 100);
            
            _videoSource.value = `https://player.vimeo.com/video/${videoId}?title=0&byline=0`

        } else {
            _videoSource.value = ''
        }
    }

    _play.value = props.play
}

function playEvent(e : Event) : void {
    
    if (!_play.value)
        _play.value = true

    emits('onPlay', e)
}

function endedEvent(e : Event) : void {
    _play.value = false
    emits('onEnded', e)
}

function errorEvent(e : Event) : void {
    emits('onError', e)
}

function pauseEvent(e : Event) : void {
    _play.value = false
    emits('onPause', e)
}

function addEventListenerOnce(source : HTMLVideoElement | Player, eventName : string, callback: any) {
  if (eventListeners.indexOf(eventName) != -1) {
    // Create a new array to store event listeners for this event
    eventListeners.push(eventName)

    // Add the event listener
    if (source instanceof HTMLVideoElement)
        (source as HTMLVideoElement).addEventListener(eventName, callback, false)
    else if (source instanceof Player)
        (source as Player).on(eventName, callback)

  }
}

function getYouTubeVideoId(url : string) : string {
  // Regular expression to match YouTube video IDs in different URL formats
  const regex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:embed\/|watch\?v=)|youtu\.be\/)([\w-]{11})/;
  const match = url.match(regex);

  if (match)
    return match[1]; 
  else
    return ''; 
}

function loadScript(scriptUrl: string) {
  return new Promise((resolve: any, reject: any) => {
    if (scriptLoaded) {
      // Script has already been loaded, resolve immediately
      resolve();
    } else {
      // Create a script element
      const script = document.createElement('script');
      script.src = scriptUrl;

      // Set up event listeners to handle script loading
      script.onload = () => {
        scriptLoaded = true;
        resolve();
      };

      script.onerror = () => {
        reject(new Error('Failed to load the script'));
      };

      // Append the script element to the document
      document.head.appendChild(script);
    }
  });
}
</script>

<template>
    <template v-if="visible">
        {{ _videoType }}
        <video
            ref="$video"
            v-if="_videoType == 'mp4'"
            :autoplay="autoplay"
            :controls="controls"
            class="w-full h-auto max-w-full border border-gray-200 rounded-lg dark:border-gray-700">
            <source :src="_videoSource" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="relative" style="padding-bottom: 56.25%;" v-if="_videoType == 'YouTube'">
            <iframe
                ref="$youtube"
                :id="uniqueId"
                :src="_videoSource"
                frameborder="0"
                class="absolute inset-0 w-full h-full rounded-lg"
                allow="autoplay; fullscreen; picture-in-picture"
                :allowfullscreen="allowFullSreen"
                referrerpolicy="strict-origin-when-cross-origin"
            >
            </iframe>
        </div>
        <div class="relative" style="padding-bottom: 56.25%;" v-if="_videoType == 'Vimeo'">
            <iframe 
                ref="$vimeo"
                :src="_videoSource"
                frameborder="0"
                class="absolute inset-0 w-full h-full rounded-lg"
                >
            </iframe>
        </div>
    </template>
</template>