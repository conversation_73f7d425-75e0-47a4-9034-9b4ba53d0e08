<script setup lang="ts">
import { onMounted, ref, watch, type PropType } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { FormulaParser } from '@/shared/services/formula-parser'
import { constants } from '@/shared/services/constants'
import ModelBinder from '@/shared/services/model-binder'
import Common from '@/shared/services/common'

interface UrlData {
    urlPattern: string
}

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    field: {
        type: String,
        required: true
    },
    value: {
        type: Object,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    data: { 
        type: Object as PropType<UrlData> 
    }
})

const base = useIrisFormControl(props)
const url = ref("")
const text = ref("")

onMounted(() => { 
    initialize()
})

watch(() => [
    props.value, 
    props.field,
    props.data
], () => initialize())

function initialize() {
    setPropValues()
    validateProps()
}

function setPropValues(): void {
    const rawValue = base.getValue()
    text.value = rawValue

    if (props.data && base.isBound()) {
        const parser = new FormulaParser(props.value)
        url.value = parser.replaceTokens(props.data.urlPattern)

        const binder = new ModelBinder()

        // If the value is a reference object, get the name from it
        const refField = base.fieldMetadata.value.ReferenceField
        const referenceModel = (props.value as any)?.[refField]

        if (referenceModel) {
            text.value = getReferenceDisplayName(referenceModel)
        } else {
            text.value = binder.formatData(rawValue, base.fieldMetadata, props.value)
        }
    } else {
        url.value = ""
        text.value = ""
    }
}

function getReferenceDisplayName(referenceModel: any): string {
    let nameFieldName = "Name"
    let isAutoNumber = false
    let format = ''

    if (referenceModel.__Metadata != null) {
        const parentMetadata = referenceModel.__Metadata()

        if (parentMetadata != null) {
            const nameField = parentMetadata.Fields.find((f: { IsNameField: boolean }) => f.IsNameField === true)

            if (nameField != null) {
                nameFieldName = nameField.Name
                isAutoNumber = nameField.IsCalculated
                format = nameField.DisplayFormatString
            }
        }
    }

    let nameValue = referenceModel[nameFieldName]

    if (isAutoNumber && format && nameValue && !isNaN(nameValue)) {
        nameValue = Common.formatAutoNumber(parseInt(nameValue), format)
    }

    return nameValue || base.getValue()
}

function validateProps(): void {
    
    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
    
    if (!props.id)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'id' }))
}
</script>

<template>
    <a 
        v-if="url && text"
        :id="id"
        :href="url"
        class="text-primary hover:text-primary-hover" 
    >
        {{ text }}
    </a>
</template>