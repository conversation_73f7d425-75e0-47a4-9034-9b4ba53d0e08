import LanguageHelper from '@/shared/services/language-helper'
import Common from '@/shared/services/common'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'

export function useIrisPage() {
    const languageHelper = new LanguageHelper()
    const store = useIrisFormControlStore()

    function setPageTitle(title: string): void {
        const maxTitleLength = 60

        if (!title)
            throw new Error(languageHelper.getMessage('pageTitleRequired'))

        if (title.length > maxTitleLength)
            throw new Error(languageHelper.getMessage('pageTitleMaxLength', { maxTitleLength }))

        Common.setPageTitle(title)
    }

    function isValidFrom(formId: string): boolean {
        const form = document.getElementById(formId) as HTMLFormElement

        if (!form)
            throw new Error(languageHelper.getMessage('invalidItem', { item: 'Form' }))

        const result = form.checkValidity() && !store.hasErrors(formId)

        return result
    }

    return { language<PERSON><PERSON><PERSON>, setPageTitle, isValidFrom }
}