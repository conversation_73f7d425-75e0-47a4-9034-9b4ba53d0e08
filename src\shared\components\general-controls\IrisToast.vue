<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

// Props
const props = defineProps({
  id: {
    type: String,
    default: () => `flowbite-toast-${Math.random().toString(36).substring(2, 9)}`
  },
  type: {
    type: String,
    default: 'default',
    validator: (value: string) => ['default', 'success', 'danger', 'warning'].includes(value)
  },
  message: {
    type: String,
    required: true
  },
  show: {
    type: Boolean,
    default: false
  },
  closable: {
    type: Boolean,
    default: true
  },
  duration: {
    type: Number,
    default: 0
  },
  position: {
    type: String,
    default: 'top-right',
    validator: (value: string) =>
      ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center', 'static'].includes(value)
  },
  iconName: {
    type: String,
    default: null
  },
})

// Emits
const emit = defineEmits(['update:show', 'closed'])

// Ref & Vars
const isVisible = ref(props.show);
let autoCloseTimer: ReturnType<typeof setTimeout> | null = null;
const closeButtonAriaLabel = 'Close toast';
const baseToastClasses = 'flex items-center w-full max-w-xs p-4 bg-bg-color-200 rounded shadow-lg'

// Computed
const toastId = computed(() => props.id)
const positionClasses = computed(() => {
  if (props.position === 'static') 
    return ''
  
  const classes = ['fixed']
  
  if (props.position.includes('top')) 
    classes.push('top-5')
  
  if (props.position.includes('bottom')) 
    classes.push('bottom-5')

  if (props.position.includes('right')) 
    classes.push('right-5')
  else if (props.position.includes('left')) 
    classes.push('left-5')
  else if (props.position.includes('center')) 
    classes.push('left-1/2', '-translate-x-1/2')

  return classes.join(' ')
})

const toastTypeClasses = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'bg-red-100'
    default:
      return 'bg-bg-color-200'
  }
})

const toastWrapperClasses = computed(() => {
  const baseClasses = 'z-[1000] flex items-center w-full max-w-xs p-4 rounded shadow-lg'
  return `${baseClasses} ${positionClasses.value} ${toastTypeClasses.value}`
})

const closeButtonClasses = computed(() =>
  'ms-auto -mx-1.5 -my-1.5 text-text-color dark:text-text-color-dark rounded inline-flex items-center justify-center h-8 w-8'
)

// Hooks
onMounted(() => {
  if (isVisible.value && props.duration > 0)
    handleAutoClose()
})

onUnmounted(() => {
  clearAutoCloseTimer()
})

// Watchers
watch(() => props.show, (newValue) => {
  isVisible.value = newValue;
  if (newValue) {
    handleAutoClose()
  } else {
    clearAutoCloseTimer()
  }
})

watch(isVisible, (newValue) => {
  if (newValue !== props.show) {
    emit('update:show', newValue)
  }
  if (!newValue) {
    emit('closed')
  }
})

// Functions
function dismissToast() {
  isVisible.value = false
}

function handleAutoClose() {
  clearAutoCloseTimer();
  if (props.duration > 0 && isVisible.value) {
    autoCloseTimer = setTimeout(() => {
      dismissToast()
    }, props.duration)
  }
}

function clearAutoCloseTimer() {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
    autoCloseTimer = null
  }
}
</script>

<template>
  <transition
    enter-active-class="transition-all duration-300 ease-in-out"
    leave-active-class="transition-all duration-300 ease-in-out"
    enter-from-class="opacity-0 translate-y-5"
    enter-to-class="opacity-100 translate-y-0"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-5"
  >
    <div
      v-if="isVisible"
      :id="toastId"
      :class="toastWrapperClasses"
      role="alert"
    >
      <div v-if="iconName">
        <IrisIcon :name="iconName" class="w-5 h-5" />
      </div>

      <div :class="['text-text-color dark:text-text-color-dark', !iconName ? 'w-full' : 'ms-3']">
        {{ message }}
      </div>

      <button
        v-if="closable"
        type="button"
        :class="closeButtonClasses"
        :aria-label="closeButtonAriaLabel"
        @click="dismissToast"
      >
        <span class="sr-only">Close</span>
        <IrisIcon name="xmark" />
      </button>
    </div>
  </transition>
</template>
