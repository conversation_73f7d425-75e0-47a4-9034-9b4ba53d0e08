<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, computed, getCurrentInstance } from 'vue'
import { constants } from '@/shared/services/constants'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { GridColumn } from '@/shared/services/grid-column'
import { RequestMethod } from '@/shared/services/enums'
import type { NameId } from '@/shared/services/name-id'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import DataAccess from '@/shared/services/data-access'
import Debounce from '@/shared/services/debounce'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import Common from '@/shared/services/common'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    value: [String, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Refs
const $el = ref<HTMLElement>()
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const text = ref("")
const showModal = ref(false)
const showResults = ref(false)
const searchResults = ref<NameId[]>([])
const selectedIndex = ref(-1)
const hasCreateAccess = ref(false)
const loading = ref(false)
const modalLoading = ref(false)
const recentData = ref<NameId[]>([])
const parentEntityLabel = ref("Record")
const viewRouteUrl = ref("")
const fullDataResultsData = ref<Object[]>()
const fullDataResultsColumns = ref<GridColumn[]>()
const lookupFilterMessage = ref("")

// Vars
let isVisible = true
let isFirstLoad = true
let previousSearchText = ""
let resultError = ref('')

// Debounce prevents too many queries to be sent out to the backend, leading flag means that system should
// sent the request right away and trailing flag means that system should first wait until time expires and then run.
const debounceInstance = new Debounce(searchByKeywords, 500, { leading: false, trailing: true })

// Computed
const isValueSet = computed(() => _value.value != null && _value.value.trim() !== '')

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// Hooks
onMounted(() => {
    base.addInputFieldSymbol(getCurrentInstance())
    isVisible = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible) {
        const formId = base.getParentFormId($el.value!)
        store.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)
    }
})

onUnmounted(() => {
    if (isVisible) 
        store.remove(base.uniqueId)
})

watch(_value, () => {
    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)
    }
})

watch(() => [
    props.value,
    props.field, 
    props.required,
    props.label, 
    props.mode,
    props.hint
], () => initialize(true))

// Methods
function initialize(performValidation: boolean) : void {
    setPropValues()
    validateProps()

    if (performValidation)
        validate()
}

function setPropValues(): void {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label

        // Set the text value of ID value is set.
        setTextValue()
        getViewRoute()

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html

    } else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
    }
}

function validateProps(): void {
    const isBound = base.isBound()

    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (isBound && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (isBound && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))

    if (isBound && base.fieldMetadata.value.IsReference != true)
        throw new Error(base.languageHelper.getMessage('invalidLookupField', { field: props.field }))
}

function validate(): boolean {
    var errors: ModelError[] = []
    const field = props.field ?? props.id

    if (_required.value && !_value.value) {
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value ?? props.id })
        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    base.addErrors(errors)

    return result
}

function setTextValue(): void {
    text.value = ''

    if (isValueSet.value) {
        const referenceModel = (props.value! as any)[base.fieldMetadata.value.ReferenceField]
        text.value = getNameFieldValue(referenceModel)
    }
}

function getNameFieldValue(referenceModel : any) : string {
    let nameFieldName = "Name"
    let isAutoNumber = false
    let format = ''

    if (referenceModel != null) {

        if (referenceModel.__Metadata != null) {
            var parentMetadata = referenceModel.__Metadata()

            if (parentMetadata != null) {
                var nameField = parentMetadata.Fields.find((f: { IsNameField: boolean; }) => f.IsNameField == true)
                
                if (nameField != null) {
                    nameFieldName = nameField.Name as string
                    isAutoNumber = nameField.IsCalculated as boolean
                    format = nameField.DisplayFormatString as string
                }

                parentEntityLabel.value = parentMetadata.Label
            }
        }

        let nameValue = referenceModel[nameFieldName]

        if (isAutoNumber && format && Common.isNumber(nameValue))
            nameValue = Common.formatAutoNumber(parseInt(nameValue), format)
        
        return nameValue
    }

    return _value.value
}

function doSearch(e: Event) {
    if (isValueSet.value == true)
        return

    if (text.value.length >= 3) {

        if (text.value !== previousSearchText && text.value.trim() != '') {
            previousSearchText = text.value
            debounceInstance.debounced()
        }
    }
    else
        searchResults.value = new Array<NameId>()
}

async function searchByKeywords() {
    // Search the database
    loading.value = true
    const autoCompleteUrl = getJsonLookupUrl()
    const dataAccess = new DataAccess()
    let results = null

    try {
       results = await dataAccess.execute(autoCompleteUrl, null, RequestMethod.get)
    }
    catch (err: any) {
        resultError.value = err.message
        return
    }
    
    if (text.value.trim() != "") {
        searchResults.value = results.Records
        .map((item: { Id: String; Name: String; }) => ({
            id: item.Id,
            name: item.Name
        })) as Array<NameId>
    } else {
        recentData.value = results.Records
        .map((item: { Id: String; Name: String; }) => ({
            id: item.Id,
            name: item.Name
        })) as Array<NameId>
    }
    
    selectedIndex.value = 0
    hasCreateAccess.value = results.hasCreateAccess
    loading.value = false
}

function resetValue() {
    _value.value = ""
    text.value = ""
    searchResults.value = new Array<NameId>()
    previousSearchText = ""
    document.getElementById(props.id)?.focus()
}

function hideMenu() {
    setTimeout(() => {
        showResults.value = false
    }, 150);
}

function showMenu() {
    setTimeout(() => {
        if (!isValueSet.value)
            showResults.value = true

        if (isFirstLoad) {
            isFirstLoad = false
            searchByKeywords()
        }

    }, 150);
}

function selectItem(itemId: string, itemName: string): void {
    _value.value = itemId
    showResults.value = false
    selectedIndex.value = -1
    text.value = itemName
}

function itemSelectedViaGrid(selectedItems: Object[]): void {
    if (selectedItems && selectedItems.length > 0) {
        const referenceModel = selectedItems[0] as any
        const nameFieldValue = getNameFieldValue(referenceModel)
        selectItem(referenceModel.Id, nameFieldValue)
        showModal.value = false
    }
}

function getJsonLookupUrl(forGridDisplay:boolean = false) {
    const entityName = getParentEntityName()
    let action = 'jsonlookup'
    let recordCount = "5"
    let area = ""

    if (forGridDisplay) {
        action = 'getlookupirisdata'
        recordCount = "10"
    }
    
    if (entityName.indexOf('__') > 0)
        area = entityName.split('__')[0]

    const baseUrl = (area)? `${area}/${entityName}/${action}` : `/${entityName}/${action}`
    return `${baseUrl}?keywords=${encodeURIComponent(text.value)}&count=${recordCount}&rfid=${base.fieldMetadata.value.Id}`
}

function getViewRoute() {
    if (!_value.value)
        return

    /* FUTURE CODE, do not remove:
    const metadata = base.fieldMetadata.value
    const entityName = metadata.ReferenceTo
    let area = ""
    
    if (entityName.indexOf('__') > 0)
        area = entityName.split('__')[0]

    const url = (area)? `${area}/${entityName}/detail/${_value.value}` : `/${entityName}/detail/${_value.value}`
    */
    const url = `/${_value.value}`
    viewRouteUrl.value = url
}

function openModalDialog() {
    showModal.value = true
    performModalSearch()
}

function getParentEntityName() {
    const metadata = base.fieldMetadata.value
    return metadata.ReferenceTo
}

async function performModalSearch() {
    modalLoading.value = true
    const endpoint = getJsonLookupUrl(true)
    const dataAccess = new DataAccess()
    let fullDataResults = null
    let _metadata: any = null

    try {
        fullDataResults = await dataAccess.execute(endpoint, null, RequestMethod.get)
        const entityName = getParentEntityName()
        _metadata = fullDataResults.__metadatas[entityName]
    }
    catch (err: any) {
        resultError.value = err.message
        return
    }

    if (fullDataResults.columns) {
        let cols: GridColumn[] = []
        fullDataResults.columns.forEach((c: object) => {
            let col = GridColumn.create(c)

            if (_metadata != null) {
                const fieldMeta = _metadata.Fields.find((f: { Id: string; Name: string; }) => f.Id == col.name || f.Name == col.name)

                if (fieldMeta.IsNameField 
                    || fieldMeta.FieldType == 'Url' 
                    || fieldMeta.FieldType == 'Lookup'
                    || fieldMeta.FieldType == 'MasterDetail') {
                    col.mode = FormControlModeEnum.text
                }
            }

            cols.push(col)
        })

        fullDataResultsColumns.value = cols
    }

    fullDataResultsData.value = fullDataResults.data
    modalLoading.value = false
    lookupFilterMessage.value = fullDataResults.lookupFilterMessage
    document.getElementById(`${props.id}__grid_search`)?.focus()
}

function selectNextItem(): void {
    let maxLength = Math.max(searchResults.value.length, recentData.value.length)

    if (hasCreateAccess) 
        maxLength++

    if (selectedIndex.value < maxLength)
        selectedIndex.value++;
}

function selectPreviousItem(): void {
    if (selectedIndex.value > 0)
        selectedIndex.value--;
}

function selectCurrentItem(): void {
    if (selectedIndex.value == 0 && text.value.length > 0) {
        openModalDialog()
        return
    }
    
    if (selectedIndex.value !== -1 && searchResults.value.length > 0) {
        const selectedItem = searchResults.value[selectedIndex.value - 1];
        selectItem(selectedItem.id, selectedItem.name);
    }

    if (selectedIndex.value !== -1 && recentData.value.length > 0) {
        const selectedItem = recentData.value[selectedIndex.value - 1];
        selectItem(selectedItem.id, selectedItem.name);
    }
}

function newRecord(): void {

}

function boldKeywordInSearchText(searchText: string, keyword: string): string {
    // Escape special characters in the keyword for regex
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // Create a regular expression to match the keyword
    const regex = new RegExp(`(${escapedKeyword})`, 'gi');
    // Replace occurrences of the keyword with bolded version
    const boldedText = searchText.replace(regex, '<strong>$1</strong>');
    return boldedText;
}

function substitude(value1: string, value2: string): string {
    if (!value1)
        return value2
    else
        return value1
}

</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="_mode == FormControlModeEnum.text">
            {{ substitude(text,_value) }}
        </template>
        <template v-else-if="_mode == FormControlModeEnum.html">
            <label v-if="_label && showLabel" :for="id" class="iris-label"> {{ _label }} </label>
            <IrisLink :path="viewRouteUrl" class="text-primary hover:text-primary-hover">{{ text }}</IrisLink>
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
        </template>
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label"> 
                {{ _label }} 
                <span class="iris-required" v-if="_required">
                    <IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon>
                </span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <IrisIcon name="magnifying-glass" class="iris-icon text-text-color-400 dark:text-text-color-400-dark"></IrisIcon>
                </div>
                <input type="text" 
                    autocomplete="off" 
                    :id="id" 
                    :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined" 
                    class="block w-full p-4 ps-10 text-sm text-text-color-100 bg-bg-color-200 border border-border-color rounded-lg
                     focus:ring-border-focus focus:border-border-focus
                     dark:text-text-color-100-dark dark:border-border-color-dark
                     dark:bg-bg-color-200-dark"
                    :class="{
                        'select-none cursor-not-allowed' : isValueSet
                    }"
                    placeholder="Search"
                    v-model="text"
                    :readonly="isValueSet"
                    @focus="showMenu"
                    @blur="hideMenu"
                    @keyup.arrow-down.prevent="selectNextItem"
                    @keydown.arrow-up.prevent="selectPreviousItem"
                    @keydown.enter.prevent="selectCurrentItem"
                    @keyup.prevent="doSearch" />
                
                <button 
                    v-if="isValueSet"
                    type="button"
                    :title="base.languageHelper.getMessage('remove')"
                    @click="resetValue"
                    class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <IrisIcon 
                        name="xmark" 
                        class="iris-icon text-text-color-400 hover:text-primary dark:text-text-color-400-dark">
                    </IrisIcon>
                </button>
                <div v-show="showResults"
                    class="z-10 border-border-color border divide-y divide-border-muted rounded-lg shadow overflow-hidden w-full 
                    dark:border-border-color-dark dark:divide-border-muted-dark"
                    :class="{
                        'absolute bg-bg-color block w-full' : showResults
                    }">
                    <ul v-if="!loading" class="max-h-56 text-sm text-text-color-200 overflow-y-auto dark:text-text-color-200-dark" aria-labelledby="dropdownDefaultButton">
                        <li v-if="text.length > 1" class="font-semibold border-b border-border-muted dark:border-border-muted-dark">
                            <a
                                @click.prevent="openModalDialog"
                                href="javascript:void(0)"
                                class="block px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark"
                            > {{ base.languageHelper.getMessage('showAllResults', { search: text }) }}
                            </a>
                        </li>
                        <li v-for="(item, index) in searchResults" :class="{ 'bg-bg-color-200 dark:bg-bg-color-200-dark': (index + 1) === selectedIndex }">
                            <a @click.prevent="selectItem(item.id, item.name)"
                                class="block px-4 py-2 hover:bg-bg-color-300 cursor-pointer dark:hover:bg-bg-color-300-dark" v-html="boldKeywordInSearchText(item.name,text)"></a>
                        </li>
                        <li v-if="text.length == 0">
                            <span class="block px-4 py-2 font-semibold">{{ base.languageHelper.getMessage('recentRecords') }}</span>
                        </li>
                        <li v-if="text.length == 0" v-for="(item, index) in recentData" :class="{ 'bg-bg-color-200 dark:bg-bg-color-200-dark': (index + 1) === selectedIndex }">
                            <a @click.prevent="selectItem(item.id, item.name)"
                            class="block px-4 py-2 hover:bg-bg-color-300 cursor-pointer dark:hover:bg-bg-color-300-dark">{{ item.name }}</a>
                        </li>
                    </ul>
                    <ul v-if="loading">
                        <li>
                            <div class="flex flex-row px-4 py-2" v-if="!resultError">
                                <div role="status" class="inline-flex">
                                    <IrisIcon 
                                        name="spinner-third" 
                                        class="iris-icon animate-spin w-6 h-6 text-text-color-400 dark:text-text-color-400-dark"
                                        aria-hidden="true">
                                    </IrisIcon>
                                    <span class="sr-only">{{ base.languageHelper.getMessage('loading') }}</span>
                                </div>
                                <div class="pl-2">{{ base.languageHelper.getMessage('loading') }}</div>
                            </div>
                            <div class="px-4 py-2" v-if="resultError">
                                <div class="text-red-600">
                                    {{ resultError }}
                                </div>
                            </div>
                        </li>
                    </ul>
                    <div class="py-2" v-if='hasCreateAccess'>
                        <a  @click.prevent="newRecord"
                            :class="{ 'bg-bg-color': (Math.max(searchResults.length, recentData.length) + 1) === selectedIndex }"
                            class="px-4 py-2 text-sm cursor-pointer flex items-center text-text-color-200 dark:text-text-color-200-dark hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">
                            <IrisIcon 
                                name="plus" 
                                class="iris-icon mr-1"
                                aria-hidden="true">
                            </IrisIcon>
                            New {{ parentEntityLabel }}</a>
                    </div>
                </div>
            </div>
            
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
            <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
            <iris-modal :id="id + '__modal_'" 
                :show="showModal"
                :title="label" 
                size="extra-large"
                @on-hide="showModal=false">
                <template #content>
                    <div class="iris-alert-light border border-border-color flex items-center dark:border-border-color-dark" v-if="lookupFilterMessage">
                        <IrisIcon name="circle-info" class="iris-icon mr-1 text-text-color-400 dark:text-text-color-400-dark"></IrisIcon>{{ lookupFilterMessage }}
                    </div>

                    <IrisGrid
                        :id="`${id}___grid_view`"
                        :value="fullDataResultsData"
                        :cols="fullDataResultsColumns!"
                        :selectable="true"
                        :multi-select="false"
                        @onSelect="itemSelectedViaGrid"
                        v-if="fullDataResultsData"
                    >
                    <template #caption>
                            <div class="flex items-center w-96">
                                <label :for="`${id}__grid_search`" class="sr-only">Search</label>
                                <div class="relative w-full">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <IrisIcon name="magnifying-glass" class="iris-icon text-text-color-400 dark:text-text-color-400-dark"></IrisIcon>
                                    </div>
                                    <input 
                                        v-model="text" 
                                        type="text" 
                                        :id="`${id}__grid_search`" 
                                        class="block w-full p-2 pl-10 text-sm font-normal text-text-color-200 bg-bg-color-200 border 
                                        border-border-color rounded-lg focus:ring-border-focus focus:border-border-focus 
                                        dark:text-text-color-200-dark dark:bg-bg-color-200-dark  
                                        dark:border-border-color-dark" 
                                        placeholder="Search" 
                                        @keyup.enter.prevent="performModalSearch" />
                                    
                                    <div v-if="modalLoading" role="status" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <IrisIcon 
                                            name="spinner-third" 
                                            class="iris-icon animate-spin w-6 h-6 text-text-color-400 dark:text-text-color-400-dark"
                                            aria-hidden="true">
                                        </IrisIcon>
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>

                        </template>
                    </IrisGrid>

                    <div v-if="resultError">
                        <div class="text-red-600">
                            {{ resultError }}
                        </div>
                    </div>

                </template>
                <template #footer>
                    <div class="">
                        <button type="button" class="btn-light p-2" @click="showModal=false">{{ base.languageHelper.getMessage('close') }}</button>
                    </div>
                </template>
            </iris-modal>
        </template>
    </div>
</template>