import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'

const routes = [
    {
        path: '/setup/ai-assistant-setting',
        name: 'ai-assistant-setting',
        component: () => import('@/modules/ai-assistant/views/Index.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/home/<USER>'
        },
        beforeEnter: (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
            const systemInfo = useIrisSystemInfoStore().getData()

            if (systemInfo.userInfo.user.roleType === 'admin')
                next()
            else
                next({ path: 'not-found' })
        }
    }
]

export default routes