import vue from '@vitejs/plugin-vue'
import fs from 'fs'
import { defineConfig } from 'vite'
import { fileURLToPath, URL } from 'node:url'
import { globaSearchChunks } from './src/modules/global-search/services/chunks'
import { aiAssistantSettingChunks } from './src/modules/ai-assistant/services/chunks'
import { sharedChunks } from './src/shared/services/chunks'
import { partnerProgramChucks } from './src/modules/partner-program/services/chunks'

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    https: {
      key: fs.readFileSync('server.key'),
      cert: fs.readFileSync('server.crt')
    }
  },
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    modulePreload: false,
    chunkSizeWarningLimit: 4096,
    rollupOptions: {
      external: (id) => {
        const excludes = [
          '/play-ground/views/LeoTest.vue',
          '/play-ground/views/SamTest.vue',
          '/play-ground/views/NafisTest.vue',
          '/play-ground/views/WebsocketTestView.vue',
          '/play-ground/views/SamLayout.vue',
          '/modules/ide/views/Icons.vue',
          '/shared/assets/scss/_variables.css'
        ]

        return excludes.some(x => id.includes(x))
      },
      output: {
        entryFileNames: `_assets/scripts/iris/[name].[hash].js`,
        chunkFileNames: `_assets/scripts/iris/[name].[hash].js`,
        assetFileNames: `_assets/scripts/iris/[name].[hash].[ext]`,
        manualChunks: {
          'global-search': globaSearchChunks,
          'ai-assistant-setting': aiAssistantSettingChunks,
          'shared': sharedChunks,
          'partner-program': partnerProgramChucks
        }
      }
    }
  }
})
