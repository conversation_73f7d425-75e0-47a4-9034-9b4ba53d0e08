<script setup lang="ts">
import { computed, onMounted, ref, watch, type PropType } from 'vue'

interface FooterCell {
    Content: string
    Width: number
}

const props = defineProps({
    value: {
        type: Array as PropType<FooterCell[]>,
        required: true
    },
    alignment: String
})

const hasValues = ref(false);

onMounted(() => {
    initialize()
});

watch(() => props.value, () => initialize(), { deep: true });

function initialize() {
    hasValues.value = props.value.length > 0
}

// Convert raw content to styled HTML using Tailwind/Flowbite styles
const styledContent = computed(() => {
    return props.value.map(cell => ({
        ...cell,
        Content: cell.Content
            .replace(/<ol>/g, '<ol class="list-decimal list-inside space-y-1">')
            .replace(/<ul>/g, '<ul class="list-disc list-inside space-y-1">')
            .replace(/<li>/g, '<li class="text-base">')
    }))
})

</script>
<template>
    <div v-if="hasValues" class="custom-footer flex flex-nowrap gap-5 mb-20 w-full" :class="alignment">
        <div v-for="(cell, index) in styledContent" :key="index" v-html="cell.Content" :style="{ width: cell.Width }"></div>
    </div>
</template>