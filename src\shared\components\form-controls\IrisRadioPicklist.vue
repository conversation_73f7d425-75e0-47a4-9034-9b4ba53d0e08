<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, computed, getCurrentInstance } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { constants } from '@/shared/services/constants'
import { DropDownItem } from '@/shared/services/dropdown-item'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { ModelError } from '@/shared/services/model-error'
import { useDependentFieldStore } from '@/shared/stores/dependent-field-store'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    isNullable: {
        type: Boolean,
        default: true 
    },
    value: [String, Object],
    items: Array<DropDownItem>,
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    inline: {
        type: Boolean,
        default: false 
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// Composables
const base = useIrisFormControl(props)
const irisFormControlStore = useIrisFormControlStore()
const dependentFieldStore = useDependentFieldStore()
const isVisible = ref(true)
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _items = ref(props.items)
const $el = ref<HTMLElement>()
let subscriptionId = ''

// computed
const filteredItems = computed(() => {
    return getFilteredValues()
})

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())
    
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)
        irisFormControlStore.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)

        if (base.isBound()) {
            // register picklist
            subscriptionId = dependentFieldStore.registerComponent({ 
                field: props.field!,
                parentField: base.fieldMetadata.value.ControllerName,
                entity: (props.value! as any).__Metadata().Name,
                formId: formId!,
                invalidate: invalidateState
            })
        }
    }
})

onUnmounted(() => {
    if (isVisible.value) {
        irisFormControlStore.remove(base.uniqueId)
        dependentFieldStore.removeComponent(subscriptionId)
    }
})

watch(_value, () => {

    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)

        //call dependency component registry
        dependentFieldStore.dispatch(subscriptionId, _value.value)
    }
})

watch(() => [
    props.value, 
    props.field,
    props.required,
    props.label, 
    props.mode,
    props.hint
], () => initialize(true))

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation) 
        validate()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label
        _items.value = props.items 
        
        if (_items.value == null || _items.value.length == 0) {
            _items.value = []
            
            base.fieldMetadata.value.PicklistEntries.forEach((element: { Label: string; Value: string; Color: string }) => {
                _items.value?.push(new DropDownItem(element.Label, element.Value, element.Color))
            })
        }

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
        _items.value = props.items 
    }

    if (_value.value) {
        _items.value?.forEach(f => f.selected = false)

        const selectedValue = _items.value?.find(f => f.value == _value.value)
            
        if (selectedValue != null)
            selectedValue.selected = true
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true

    var errors: ModelError[] = []

    if (_required.value && !_value.value) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    base.addErrors(errors)

    return result
}

function getFilteredValues() : Array<DropDownItem> {
   
   // By record type if exists
   const model = (props.value! as any)
   const rtFieldName = 'RecordTypeId'
   let finalItems = _items.value

   if (rtFieldName in model) {
       const recordTypeId = model[rtFieldName]

       if (recordTypeId
           && model.__Metadata != null 
           && model.__Metadata.RecordTypes != null 
           && model.__Metadata.RecordTypes.length > 0) {

           const rt = model.__Metadata.RecordTypes.find((f: { Id: any, Name: String, IsActive: Boolean }) => f.Id == recordTypeId)

           if (rt.RecordTypePicklistSettings != null && rt.RecordTypePicklistSettings.length > 0) {
               const picklistSetting = rt.RecordTypePicklistSettings.find((f: { EntityField: string | undefined }) => f.EntityField == props.field)

               if (picklistSetting != null && picklistSetting.Values) {
                   const values = removeFirstAndLastLetters(picklistSetting.Values).split(',')
                   finalItems = finalItems?.filter(item => values.includes(item.value))
               }
           }
       }
   }

   // dependent picklist
   if (base.fieldMetadata.value.ControllerName 
       && base.fieldMetadata.value.DependentPicklists != null) {
           
       const controllerValue = model[base.fieldMetadata.value.ControllerName]

       if (controllerValue) {
           const values = base.fieldMetadata.value.DependentPicklists[controllerValue]

           if (values) {
               const allowedValues = values.map((f: { Value: string }) => f.Value)
               finalItems = finalItems?.filter(item => allowedValues.includes(item.value))
           }
       }
       else if (_mode.value != FormControlModeEnum.html && _mode.value != FormControlModeEnum.text)
           _mode.value = FormControlModeEnum.disabled
   }

   return finalItems!
}

function removeFirstAndLastLetters(input: string): string {
   if (input.length <= 2) {
       // If the input has 0 or 1 characters, return an empty string
       return ''; 
   } else {
       // Slice the string to remove the first and last characters
       return input.slice(1, -1); 
   }
}

function invalidateState(value: string) : void {
   if (_mode.value == FormControlModeEnum.html || _mode.value == FormControlModeEnum.text)
       return;

   //parent controling field has requested the state to be reset
   _items.value?.forEach(item => item.selected = false)
   getFilteredValues()
   
   if (value)
       _mode.value = FormControlModeEnum.edit
   else
       _mode.value = FormControlModeEnum.disabled
}

function selectItem(item : DropDownItem) : void {

    _items.value?.forEach(f => f.selected = false)

    if (item) {
        item.selected = true

        _value.value = item.value
    }
}

function getLabel() {
    const selectedItem = _items.value?.find(f => f.value == _value.value)

    if (selectedItem)
        return selectedItem.label
    else
        return ''
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="mode == FormControlModeEnum.text">
            {{ getLabel() }}
        </template>
        <template v-else-if="mode == FormControlModeEnum.html">
            <label v-if="_label && showLabel" :for="id" class="iris-label"> {{ _label }} </label>
            <div class="mb-3"> {{ getLabel() }} </div>
        </template>
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label"> 
                {{ _label }}
                <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
            </label>
            <div :class="{ 'flex' : inline == true }">
                <div v-for="(item, index) in filteredItems" class="flex items-center" :class="{
                    'mb-4' : inline == false,
                    'me-4' : inline == true
                }">
                    <input 
                        @change="selectItem(item)"
                        :id="`${id}-${index}`"
                        :disabled="mode == FormControlModeEnum.disabled" 
                        :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined" 
                        type="radio" 
                        :value="item.value"
                        :name="`n${id}`"
                        :checked="item.selected"
                        class="w-4 h-4 text-primary bg-bg-color border-border-color focus:ring-border-focus focus:ring-2 dark:bg-bg-color-dark dark:border-border-color-dark" />
                    
                    <label
                        :for="`${id}-${index}`"
                        class="ms-2 text-sm font-medium text-text-color-200 dark:text-text-color-200-dark"> {{ item.label }}</label>
                    <p v-if="item.helperText && inline != true" class="text-xs font-normal text-text-color-400 dark:text-text-color-400-dark"> {{ item.helperText }}</p>
                </div>
            </div>

            <!-- Errors -->
            <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
        </template>    
    </div>
</template>