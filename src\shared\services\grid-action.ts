import { FormulaParser } from "@/shared/services/formula-parser"

export enum GridActionMode {
  Link   = 'link',
  Button = 'button',
}
export class GridAction {

    label: string = ''
    icon: string = 'lightning'
    path: string = ''
    visibilityCondition: string = ''
    isVisible: boolean = true
    type: GridActionMode = GridActionMode.Link

    getActionPath(data : object) {
        if (this.path) {
            const parser = new FormulaParser(data)

            if (this.visibilityCondition)
                this.isVisible = parser.replaceTokens(this.visibilityCondition) == "true"
            
            if (this.isVisible)
                return parser.replaceTokens(this.path)

        } else 
            this.isVisible = false

        return ''
    }

    static create(data: object) : GridAction {
        const action = new GridAction();

        for (const prop in data) {
            if (action.hasOwnProperty(prop)) {
                // Use value from data object if present
                (action as any)[prop] = (data as any)[prop];
            }
        }

        return action;
    }

}