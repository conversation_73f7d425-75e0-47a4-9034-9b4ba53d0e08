<script setup lang="ts">
import { ref,onMounted } from 'vue'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { RequestMethod } from '@/shared/services/enums'
import IrisCheckbox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisSection from '@/shared/components/form-controls/IrisSection.vue'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import DataAccess from '@/shared/services/data-access'
import IrisField from '@/shared/components/form-controls/IrisField.vue'
import IrisRange from '@/shared/components/form-controls/IrisRange.vue'
import IrisColor from '@/shared/components/form-controls/IrisColor.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisValidationSummary from '@/shared/components/form-controls/IrisValidationSummary.vue'

const model:any = ref(null)
const store = useIrisFormControlStore()

onMounted(async () => {
    const dataAccess = new DataAccess()
    model.value = await dataAccess.execute('iris/GetSampleIrisData', null, RequestMethod.get)
})

function submitApplication() {

    let isValid = store.isFormValid("f1")
    var errors = store.getErrors()
    console.log(errors, isValid)
}
</script>

<template>
    <div class="border">
        <button type="button" class="btn-primary">Make Read-Only</button>
        <button type="button" class="btn-primary">Make Editable</button>
        <button type="button" class="btn-primary">Change Labels</button>
    </div>

    <h1 class="text-2xl font-semibold text-text-color mt-6 mb-3 text-center">Scholarship Application</h1>
    <div class="mx-auto max-w-screen-md">
        <p class="mb-4 lg:mb-4 font-light text-center text-gray-500 dark:text-gray-400 sm:text-xl">Got a technical issue? Want to send feedback about a beta feature? Need details about our Scholarship plan? Let us know.</p>
    </div>
    
    <br/>
    <IrisForm id="f1" v-if="model">
        <IrisValidationSummary formId="f1" />
        <IrisSection id="section1" cols="two" title="General Details" :flush="true" :collapsible="false">
            <IrisField id="Name" :value="model.scholar" field="Name"/>
            <IrisCheckbox id="Academic_Transcript__c" :value="model.scholar" field="Academic_Transcript__c" output-mode="toggle"/> 
            <IrisField id="Current_School__c" :value="model.scholar" field="Current_School__c"/>
            <IrisRange id="Current_GPA__c" :value="model.scholar" field="Current_GPA__c" displayLabels/>
            <IrisField id="Citizenship__c" :value="model.scholar" field="Citizenship__c"/>
            <IrisField id="Date_of_Acceptance__c" :value="model.scholar" field="Date_of_Acceptance__c"/>
            <IrisField id="Approved_Interventions__c" :value="model.scholar" field="Approved_Interventions__c"/>
            <IrisTextbox id="Emergency_Contact_Mobile__c" :value="model.scholar" field="Emergency_Contact_Mobile__c" addOnIcon="mobile" placeHolder="Enter mobile number" required/>
            <IrisColor id="Medical_Insurance_Identification_Number__c" :value="model.scholar" field="Medical_Insurance_Identification_Number__c"/>
        </IrisSection>
        <IrisSection id="section2" cols="one" :flush="true">
            <IrisField id="Medical_social_history_family__c" :value="model.scholar" field="Medical_social_history_family__c" :flush="true"/>
        </IrisSection>
        <div class="pt-4 text-right">
            <button type="button" class="btn-primary" @click="submitApplication">Submit Application</button>
        </div>
    </IrisForm>
    <br/>
    <br/>
    <br/>
    <br/>
</template>