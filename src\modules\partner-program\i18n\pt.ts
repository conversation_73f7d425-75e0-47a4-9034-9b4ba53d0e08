const messages = {
  partnerProgram: "Programa de Parceiros",
  partnerManagement: "Gestão de Parceiros",
  helponthispage: "Ajuda nesta página",
  search: "Pesquisar",
  programs: "Programas",
  account: "Conta",
  partnerPrograms: "Programas de Parceiros",
  serverError: "Algo deu errado!",
  loading: "Carregando...",
  yes: "Sim",
  no: "N<PERSON>",
  benefits: "Benefícios",
  programBenefits: "Benefícios do Programa",
  newProgram: "Novo Programa",
  programTiers: "Níveis do Programa",
  dragDropMessage: "Arraste e solte os níveis para reordená-los",
  tier: "Nível",
  remove: "Remover",
  addTier: "Adicionar Nível",
  editTier: "Editar Nível",
  tierBadge: "Distintivo do Nível",
  tierDescription: "Descrição do Nível",
  tierName: "Nome do Nível",
  deleteTier: "Excluir Nível",
  noRecords: "Nenhum dado para exibir.",
  newBenefit: "Novo Benefício",
  cancel: "<PERSON>celar",
  close: "<PERSON><PERSON><PERSON>",
  addBenefit: "Adicionar Benefício",
  saving: "Salvando...",
  existingBenefitName: "Já existe um benefício com este nome. Escolha um nome diferente.",
  highestTierReached: "Você está no nível mais alto!",
  failFetchData: "Erro: Não foi possível buscar os dados. Contate o administrador.",
  failInitialize: "Falha ao inicializar os componentes",
  failSave: "Falha ao salvar o registro. Tente novamente ou contate o administrador.",
  benefitDuplicateNameError: "Já existe um benefício com este nome. Escolha outro.",
  save: "Salvar",
  deleteBenefit: "Excluir Benefício",
  deleteBenefitMessage: "Tem certeza de que deseja excluir este benefício e todas as atribuições?",
  delete: "Excluir",
  failDelete: "Falha ao excluir o registro. Tente novamente ou contate o administrador.",
  edit: "Editar",
  first: "Primeiro",
  last: "Último",
  next: "Próximo",
  previous: "Anterior",
  editBenefit: "Editar Benefício",
  noProgramsAvailable: "Nenhum programa disponível.",
  enrollments: "Inscrições",
  partnerEnrollments: "Inscrições de Parceiros",
  newEnrollment: "Nova Inscrição",
  allPrograms: "Todos os Programas",
  noTiers: "Nenhum nível disponível para o programa selecionado. Escolha outro programa ou contate o administrador.",
  editEnrollment: "Editar Inscrição",
  addEnrollment: "Adicionar Inscrição",
  deleteEnrollmentMessage: "Tem certeza de que deseja excluir esta inscrição e todas as informações?",
  deleteEnrollment: "Excluir Inscrição",
  duplicateEnrollment: "Este parceiro já possui uma inscrição ativa neste programa.",
  noAccountsAvailable: "Nenhuma conta disponível.",
  deleteProgramMessage: "Você está prestes a excluir este programa e todos os seus dados, incluindo níveis, inscrições e critérios. Esta ação não pode ser desfeita.",
  deleteProgram: "Excluir Programa de Parceiros",
  setting: "Configuração",
  settings: "Configurações",
  partnerProgramStatus: "Status do Programa de Parceiros",
  enablePartnerProgram: "Ativar Programa de Parceiros",
  partnerProgramIsDisabled: "Programa de Parceiros está desativado",
  partnerProgramIsActive: "Programa de Parceiros está ativo",
  confirmationDisablingPartnerProgram: "Tem certeza de que deseja desativar o Programa de Parceiros?",
  confirmationDisablingPartnerProgramMessage: "A visibilidade dos níveis e benefícios será ocultada.",
  ok: "OK",
  settingsSaved: "Configurações salvas com sucesso!",
  back: "Voltar",
  benefit: "Benefício do Programa de Parceiros",
  benefitTypeError: "Tipo de benefício é obrigatório.",
  benefitNameRequired: "Nome do benefício é obrigatório.",
  enrollmentDateMustBeBeforeExpiration: "A data de inscrição deve ser anterior à data de expiração.",
  editProgram: "Editar Programa",
  addProgram: "Adicionar Programa",
  programNameRequired: "Nome do programa é obrigatório.",
  tierNameRequired: "Nome do nível é obrigatório.",
  tierInexNameRequired: "Nível {idx}: nome é obrigatório.",
  duplicateTierName: "Nome de nível duplicado",
  unsavedChangesConfimation: "Você tem alterações não salvas. Deseja realmente sair?",
  minTierError: "É necessário configurar pelo menos um nível.",
  maxTierError: "Você pode configurar no máximo seis níveis.",
  duplicateIndexTierName: "Nível {idx}: nome duplicado {tierName}.",
  missingProgramId: "Programa inválido",
  program: "Programa",
  newTier: "Novo Nível",
  clone: "Clonar",
  configure: "Configurar",
  noTiersDefined: "Nenhum nível definido.",
  criteria: "Critérios do Programa",
  noCriteriaDefined: "Nenhum critério definido.",
  addCriterion: "Adicionar Critério",
  noBenefitDefined: "Nenhum benefício definido.",
  atLeastOneTierRequired: "Informe pelo menos um nível para o programa",
  confirm: "Confirmar",
  noMoreAvailableBenefits: "Nenhum benefício adicional disponível para adicionar.",
  missingProgramIdOrPartnerAccountId: "Informações ausentes. Contate a administração.",
  noProgramEnrollments: "Você não está inscrito em nenhum programa de parceiros. Contate o administrador.",
  noPartnerAccount: "Você precisa ser um parceiro para ver esta página.",
  certifications: "Certificações",
  numberofOpportunities: "Número de Oportunidades",
  opportunityRevenue: "Receita de Oportunidades",
  numberofDealRegistrations: "Número de Registros de Negócios",
  custom: "Personalizado",
  configureCriterion: "Configurar Critério",
  createCriterion: "Criar Critério",
  create: "Criar",
  criteriaDuplicateNameError: "Já existe um critério com este nome. Escolha outro.",
  quantity: "Quantidade",
  revenue: "Receita",
  criterionNameRequiredError: "Nome do critério é obrigatório.",
  criterionTypeRequiredError: "Tipo do critério é obrigatório.",
  trainingCourseRequiredError: "Curso de treinamento é obrigatório para Certificações.",
  rollupFieldRequiredError: "Campo de agregação é obrigatório para Receita.",
  attainmentDateFieldRequired: "Campo de data de conclusão é obrigatório",
  unitofMeasureRequired: "Unidade de medida é obrigatória.",
  editCriterion: "Editar Critério",
  missingFields: "Há informações ausentes",
  trainingCourse: "Curso de Treinamento",
  name: "Nome",
  type: "Tipo",
  filterLogic: "Lógica de Filtro (opcional)",
  filterLogicPlaceHolder: "ex: (1 e 2) ou 3",
  reset: "Redefinir",
  addFilter: "Adicionar Filtro",
  equals: "igual a",
  notEquals: "diferente de",
  includes: "inclui",
  excludes: "exclui",
  greaterThan: "maior que",
  lessThan: "menor que",
  lessOrEqual: "menor ou igual a",
  greaterOrEqual: "maior ou igual a",
  contains:"contém",
  notContain: "não contém",
  startWith: "começa com",
  notStartWith: "não começa com",
  partnerAccount: "Conta de Parceiro",
  enrollmentDate: "Data de Inscrição",
  expirationDate: "Data de Expiração",
}

export default messages