type DataObject = { [key: string]: any };

export class FormulaParser {
  private data: DataObject;

  constructor(data: DataObject) {
    this.data = data;
  }

  public replaceTokens(template: string): string {
    const tokenRegex = /{\!(.*?)}/g;

    return template.replace(tokenRegex, (match, token) => {
      const value = this.evaluateToken(token);
      return value !== undefined ? value : match;
    });
  }

  private evaluateToken(token: string): any {
    const functionRegex = /^(\w+)\((.*)\)$/;

    // Check if the token is a function
    const functionMatch = token.match(functionRegex);
    if (functionMatch) {
      const funcName = functionMatch[1].toUpperCase(); // Convert function name to upper case
      const funcArgs = this.parseArguments(functionMatch[2]);
      return this.applyFunction(funcName, funcArgs);
    }

    // If it's not a function, treat it as a path
    return this.getValueFromPath(token);
  }

  private parseArguments(argString: string): any[] {
    const args: any[] = [];
    let currentArg = '';
    let parenCount = 0;

    for (let char of argString) {
      if (char === ',' && parenCount === 0) {
        args.push(this.evaluateToken(currentArg.trim()));
        currentArg = '';
      } else {
        if (char === '(') parenCount++;
        if (char === ')') parenCount--;
        currentArg += char;
      }
    }

    if (currentArg.trim()) {
      args.push(this.evaluateToken(currentArg.trim()));
    }

    return args;
  }

  private getValueFromPath(path: string): any {
    const keys = path.split('.');
    let value = this.data;

    for (let key of keys) {
      value = value?.[key];
      if (value === undefined) return undefined;
    }

    return value;
  }

  private applyFunction(funcName: string, args: any[]): any {
    switch (funcName) {
      case 'LEN':
        return String(args[0]?.length || 0);
      case 'ADD':
        return String(args.reduce((sum, arg) => sum + Number(arg), 0));
      case 'AND':
        return String(args.every(arg => Boolean(arg)));
      case 'OR':
        return String(args.some(arg => Boolean(arg)));
      case 'NOT':
        return String(!Boolean(args[0]));
      case 'TOLOWER':
        return String(args[0]).toLowerCase();
      case 'TOUPPER':
        return String(args[0]).toUpperCase();
      case 'CONCAT':
        return args.join('');
      case 'REPLACE':
        const [str, pattern, replaceValue, ignoreCase] = args;
        const flags = ignoreCase ? 'gi' : 'g';
        return String(str).replace(new RegExp(String(pattern), flags), String(replaceValue));
      case 'STARTSWITH':
        return String(args[0]).startsWith(String(args[1]));
      case 'ENDSWITH':
        return String(args[0]).endsWith(String(args[1]));
      case 'CONTAINS':
        return String(args[0]).includes(String(args[1]));
      case 'ISNULL':
        return args[0] === null || args[0] === undefined;
      case 'EQUAL':
        return args.every((arg, i, arr) => arg === arr[0]);
      case 'TEXT':
        return String(args[0]);
      case 'HASVALUE':
        return args[0] !== null && args[0] !== undefined && args[0] !== '';
      case 'TODAY':
        return new Date().toISOString().split('T')[0];
      case 'LESSTHAN':
        return String(Number(args[0]) < Number(args[1]));
      case 'MORETHAN':
        return String(Number(args[0]) > Number(args[1]));
      case 'LESSTHANOREQUAL':
        return String(Number(args[0]) <= Number(args[1]));
      case 'MORETHANOREQUAL':
        return String(Number(args[0]) >= Number(args[1]));
      case 'ADDDAYS':
        const date = new Date(args[0]);
        date.setDate(date.getDate() + Number(args[1]));
        return date.toISOString().split('T')[0];
      case 'URLENCODE':
        return encodeURIComponent(String(args[0]));
      case 'URLDECODE':
        return decodeURIComponent(String(args[0]));
      case 'FORMATNUMBER':
        return Number(args[0]).toFixed(Number(args[1]) || 0);
      case 'IF':
        return Boolean(args[0]) ? args[1] : args[2];
      // Add more functions here as needed
      default:
        throw new Error(`Function ${funcName} not supported.`);
    }
  }
}
