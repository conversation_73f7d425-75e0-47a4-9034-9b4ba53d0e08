<script setup lang="ts">
    import { onMounted } from 'vue'
    import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
    import IrisUserDropDown from '@/shared/components/theming-controls/IrisUserDropDown.vue'
    import IrisNavbarMenu from '@/shared/components/theming-controls/IrisNavbarMenu.vue'
    import IrisAppLauncher from '@/shared/components/theming-controls/IrisAppLauncher.vue'
    import IrisLanguagePicker from '@/shared/components/theming-controls/IrisLanguagePicker.vue'
    import IrisSocialLinks from '@/shared/components/theming-controls/IrisSocialLinks.vue'
    import IrisFooterLinks from '@/shared/components/theming-controls/IrisFooterLinks.vue'
    import IrisGlobalSearchBox from '@/modules/global-search/components/IrisGlobalSearchBox.vue'
    import IrisInstanceType from '@/shared/components/theming-controls/IrisInstanceType.vue'
    import IrisCustomFooter from '@/shared/components/theming-controls/IrisCustomFooter.vue'
    import Iris<PERSON>ogo from '@/shared/components/theming-controls/IrisLogo.vue'
    import FloatingMenuHelper from '@/shared/services/floating-menu-helper'

    const props = defineProps({
        showMenu: {
            type: Boolean,
            default: true
        }
    })

    const systemInfo = useIrisSystemInfoStore().getData()

    onMounted(() => {
        FloatingMenuHelper.calcSiteBodyPaddingTop(true)
        
        document.body.classList.add('bg-header-bg')
        document.body.classList.add('dark:bg-header-bg-dark')
    })

</script>

<template>
    <div class="sitePage">
        <div class='siteHeaderContainer std-theme-siteHeaderContainer'>
            <div class="site-page-header bg-nav-bg dark:bg-nav-bg-dark" :class="{
                'navbar-fixed fixed z-[1000] w-full' : systemInfo.company.theme.isFloatingMenu
             }">
                <div class="bg-header-bg dark:bg-header-bg-dark py-4" v-if="systemInfo.userInfo">
                    <div class="max-w-page mx-auto px-4">
                        <div class="flex justify-end" v-if="systemInfo.company.instance.instanceName">
                            <IrisInstanceType :value="systemInfo.company.instance" class=" mb-3"/>
                        </div>
                        <div class="flex gap-4 items-center justify-between flex-wrap">
                            <div class="siteHeader-logo h-12 max-w-32 sm:max-w-52 overflow-hidden">
                                <IrisLogo 
                                    :url="systemInfo.company.theme.logoUrl" 
                                    :logo-click-url="systemInfo.company.logoClickUrl" 
                                    class="site-logo-img h-full flex items-center justify-center"/>
                            </div>
                            <div class="siteHeader-items flex items-center gap-4">
                                <div v-if="systemInfo.company.system.globalSearch">
                                    <IrisGlobalSearchBox class="text-header-text dark:text-header-text-dark"/>
                                </div>
                                <IrisAppLauncher :value="systemInfo.apps" v-if="showMenu" class="p-0.5 text-header-text dark:text-header-text-dark
                                opacity-80 hover:opacity-100
                                rounded focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark
                                "/>
                                <IrisLanguagePicker 
                                    class="p-0.5 text-header-text dark:text-header-text-dark
                                    opacity-80 hover:opacity-100 rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"
                                    :value="systemInfo.company.langauges" 
                                    :showLabel=false
                                    :showFlags="systemInfo.company.system.languageFlagsEnabled" 
                                    :selected="systemInfo.userInfo.user.lang"/>
                                <IrisUserDropDown 
                                    :value="systemInfo.userInfo" 
                                    :showUserName="false" 
                                    :useIcons="true"
                                    class="p-0.5 text-header-text dark:text-header-text-dark opacity-80 hover:opacity-100
                                    rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="site-main-nav block w-full border-b border-border-muted dark:border-border-muted-dark" v-if="systemInfo.menuItems && showMenu">
                    <IrisNavbarMenu :hasBg=true :value="systemInfo.menuItems"/>
                    <!-- MenuAlignment="Left" HoverEffect="BackgroundHighlight" -->
                </div>
            </div>
        </div>
        <div class="siteBody bg-bg-color dark:bg-bg-color-dark">
            <div class="p-4 min-h-[550px] max-w-page mx-auto">
                <RouterView />
            </div>
        </div>
        <div class="py-10 bg-header-bg border-t border-border-color text-header-text dark:border-border-color-dark dark:text-header-text-dark dark:bg-header-bg-dark">
            <div class="max-w-page mx-auto">
                <div class="footer p-4">
                    <IrisCustomFooter :value="systemInfo.company.customFooterCells" :alignment="systemInfo.company.customFooterCellsAlignment"/>
                    <div class="flex justify-start flex-wrap gap-5 flex-col lg:flex-row lg:gap-20">
                        <IrisSocialLinks :value="systemInfo.company.socialLinks" class="inline-flex items-center p-4 text-header-text dark:header-text-dark opacity-80 hover:opacity-100 rounded-full border border-header-text dark:border-header-text-dark" />
                        <div>
                            <IrisFooterLinks :value="systemInfo.company.footerData" class="opacity-80 hover:opacity-100 hover:underline"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>