<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch, getCurrentInstance, onUpdated } from 'vue'
import { initTooltips } from 'flowbite'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { constants } from '@/shared/services/constants'
import { ComponentSizeEnum, FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisStaticAssets from '@/shared/components/general-controls/IrisStaticAssets.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'

// Props
const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    value: [String, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    placeholder: {
        type: String,
        default: 'Select an asset'
    },
    size: {
        type: String,
        default: ComponentSizeEnum.default,
        validator: (value: string) => {
            return (<any>Object).values(ComponentSizeEnum).includes(value)
        }
    },
    allowedExtensions: {
        type: Array as () => string[],
        default: () => []
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    multiSelect: {
        type: Boolean,
        default: false
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// Composables & Local state (const, refs)
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const isVisible = ref(true)
const $el = ref<HTMLElement>()
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const showModal = ref(false)
const tempAsset = ref<any>(null)
const thumbnailUrl = ref<string>('');
const systemInfoStore = useIrisSystemInfoStore()
const systemInfo = systemInfoStore.getData()
const dataAccess = new DataAccess()
const originalValue = ref<string>('')

// Lifecycle Hooks (onMounted, onUnmounted)
onMounted(() => {
    base.addInputFieldSymbol(getCurrentInstance())
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)
        store.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)
    }
})

onUnmounted(() => {
    if (isVisible.value)
        store.remove(base.uniqueId)
})

onUpdated(() => {
    initTooltips()
})

// Computed properties
const isFileSelected = computed(() => {
    return tempAsset.value && tempAsset.value.isFile === true
})

const isOkEnabled = computed(() => {
    if (!isFileSelected.value)
        return false;

    if (!props.allowedExtensions.length)
        return true;

    const relativePath = tempAsset.value?.relativePath || '';

    if (!relativePath)
        return false;

    const ext = '.' + (relativePath.split('.').pop()?.toLowerCase() ?? '');
    
    return props.allowedExtensions
        .map(e => e.toLowerCase())
        .includes(ext);
})

const inputSizeClass = computed(() => {
    switch (props.size) {
        case ComponentSizeEnum.small: return 'iris-textbox-sm'
        case ComponentSizeEnum.large: return 'iris-textbox-lg'
        default: return ''
    }
})

const selectedFileType = computed(() => {
    if (!_value.value) 
        return null;
    
    const value = base.isBound() ? base.getValue() : _value.value
    const ext = value.split('.').pop()?.toLowerCase();
    
    return ext;
});

const isImageFile = computed(() => {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

    return selectedFileType.value && imageTypes.includes(selectedFileType.value);
});

const fileIcon = computed(() => {
    switch (selectedFileType.value) {
        case 'pdf': 
            return 'document-text';
        case 'json': 
            return 'code';
        case 'txt': 
            return 'document';
        case 'doc':
        case 'docx': 
            return 'document-text';
        case 'xls':
        case 'xlsx':
            return 'table';
        default: 
            return 'document';
    }
});

const selectedFileName = computed(() => {
    return tempAsset.value?.name || _value.value?.split('\\').pop()?.split('/').pop() || '';
});

const selectedFileSize = computed(() => {
    if (!tempAsset.value?.size) 
        return '';

    if (typeof tempAsset.value.size === 'string')
        return tempAsset.value.size;

    return Common.formatFileSize(tempAsset.value?.size)
});

// Watch handlers
watch(_value, () => {
    if (validate()) {
        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)
    }
})

watch(() => [props.value, props.required, props.label, props.mode],
    () => initialize(true))

// Functions (Methods & Event Handlers)
function setThumbnailUrl(filePath: string) {
    if (isImageFile.value) {
        const path = filePath.startsWith('/') ? filePath : '/' + filePath;
        thumbnailUrl.value = !systemInfo.env.production
            ? `${systemInfo.env.baseUrl}/Contents/Assets${path}`
            : `/Contents/Assets${path}`;
    }
}

function initialize(performValidation: boolean) {
    setPropValues()

    if (performValidation)
        validate()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
    }

    if (_value.value) {
        let path = _value.value.startsWith('/') ? _value.value : '/' + _value.value;

        fetchFileDetails(path)
        setThumbnailUrl(_value.value)
    }
}

function validate(): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true

    const errors: ModelError[] = []

    if (_required.value && !_value.value && base.isTouched) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })
        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0
    base.addErrors(errors)
    
    return result
}

function onLookupClick() {
    originalValue.value = _value.value
    tempAsset.value = null
    showModal.value = true
    nextTick(() => {
        window.dispatchEvent(new Event('resize'))
    })
}

function handleAssetsSelect(files: any) {
    if (!files?.length) 
        return;
    
    const file = files[0];
    const ext = '.' + (file.relativePath.split('.').pop()?.toLowerCase() ?? '');
    
    if (!props.allowedExtensions.length || 
        props.allowedExtensions
            .map(e => e.toLowerCase())
            .includes(ext)) {
        tempAsset.value = file;
    }
}

function onOk() {
    if (isOkEnabled.value) {
        _value.value = tempAsset.value.relativePath

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        setThumbnailUrl(_value.value)

        emits('onChange', _value.value)
    }
    showModal.value = false
}

function onCancel() {
    showModal.value = false
}

function onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && _mode.value === FormControlModeEnum.edit) {
        event.preventDefault()
        onLookupClick()
    }
}

// Add this new function after other functions
function onRemove() {
    _value.value = ''
    tempAsset.value = null
    thumbnailUrl.value = ''
    emits('onChange', '')
}

async function fetchFileDetails(location: string) {
    try {
        const parts = location.split(/[/\\]/);
        const fileName = parts.pop() || '';
        const path = parts.length > 0 ? parts.join('/') : '/';

        const response = await dataAccess.postData('/sys/staticasset/operations', {
            action: 'details', 
            path: path,
            names: fileName ? [fileName] : [],  // Pass filename in names array
            data: null,
            showHiddenItems: false
        });
        
        if (response?.details) {
            tempAsset.value = response.details
        }
    } catch (error) {
        console.error('Error fetching file details:', error);
    }
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="_mode == FormControlModeEnum.html">
            <label v-if="_label && showLabel" class="iris-label">{{ _label }}</label>
            <span>{{ _value }}</span>
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
        </template>
        <template v-else-if="_mode == FormControlModeEnum.text">
            {{ _value }}
        </template>
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label">
                {{ _label }}
                <span class="iris-required" v-if="_required">
                    <IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem" />
                </span>
            </label>
            <div class="relative">
                <!-- File preview when file is selected -->
                <div v-if="_value" 
                    class="mb-2 flex items-start gap-3 p-3 bg-bg-color-200 dark:bg-bg-color-200-dark border rounded border-border-color dark:border-border-color-dark">
                    <!-- Image preview -->
                    <div v-if="isImageFile" class="flex-shrink-0">
                        <img 
                            :src="thumbnailUrl" 
                            :alt="selectedFileName" 
                            class="w-10 h-10 object-cover rounded"
                        />
                    </div>
                    
                    <!-- File icon for non-images -->
                    <div v-else class="flex-shrink-0">
                        <IrisIcon 
                            :name="fileIcon" 
                            class="w-10 h-10" 
                        />
                    </div>

                    <!-- File information -->
                    <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium truncate text-text-color dark:text-text-color-dark">
                            {{ selectedFileName }}
                        </p>
                        <p v-if="selectedFileSize" class="mt-1 text-xs text-text-color-300 dark:text-text-color-300-dark">
                            {{ selectedFileSize }}
                        </p>
                    </div>

                    <!-- Change button when in edit mode -->
                    <button
                        type="button"
                        @click="onRemove"
                        :data-tooltip-target="`${id}-delete-tooltip`"
                        class="text-sm font-medium self-center text-text-color dark:text-text-color-dark"
                    >
                        <IrisIcon
                            name="trash-can"
                            class="h-4 w-4"
                        />
                    </button>
                    <div :id="id+'-delete-tooltip'" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-text-color transition-opacity duration-300 bg-bg-color-100 border border-border-color rounded-lg shadow-sm opacity-0 tooltip dark:text-text-color-dark dark:bg-bg-color-dark-100 dark:border-border-color-dark">
                        Clear
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </div>
                
                <!-- Input field when no file is selected -->
                <div v-else class="relative">
                    <input
                        :id="id"
                        type="text"
                        class="iris-textbox pr-10"
                        :class="[inputSizeClass]"
                        v-model="_value"
                        :placeholder="placeholder"
                        readonly
                        :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined"
                        :disabled="_mode == FormControlModeEnum.disabled"
                        @keydown="onKeyDown"
                    />
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 flex items-center pe-3"
                        @click="onLookupClick"
                        :data-tooltip-target="`${id}-browse-tooltip`"
                        :disabled="_mode == FormControlModeEnum.disabled"
                    >
                        <IrisIcon
                            name="magnifying-glass"
                            class="h-5 w-5"
                        />
                    </button>
                    <div :id="id+'-browse-tooltip'" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-text-color transition-opacity duration-300 bg-bg-color-100 border border-border-color rounded-lg shadow-sm opacity-0 tooltip dark:text-text-color-dark dark:bg-bg-color-dark-100 dark:border-border-color-dark">
                        Browse files
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </div>
            </div>
            <div v-if="hint" class="iris-input-hint mt-1">{{ hint }}</div>
            <div v-if="_mode == FormControlModeEnum.edit" class="text-danger small" v-for="error in base.getErrors()">
                {{ error.message }}
            </div>

            <!-- Modal with IrisStaticAssets -->
            <iris-modal
                :id="id + '_modal'"
                title="Select Asset"
                :show="showModal"
                size="large"
                @onHide="onCancel"
                :bodyPadding="false"
            >
                <template #content>
                    <IrisStaticAssets 
                        :id="id" 
                        :allowedExtensions="allowedExtensions" 
                        :multiSelect="multiSelect"
                        :showSelectButton="true"
                        @onSelect="handleAssetsSelect" 
                    />
                </template>
                <template #footer>
                    <button type="button" class="btn-light px-3" @click="onCancel">Cancel</button>
                    <button
                        type="button"
                        class="btn-primary px-3"
                        @click="onOk"
                        :disabled="!isOkEnabled"
                    >Select</button>
                </template>
            </iris-modal>
        </template>
    </div>
</template>
