const messages = {
    partnerProgram: "Partner Program",
    partnerManagement: "Partner Management",
    helponthispage: "Help on this page",
    search: "Search",
    programs: "Programs",
    account: "Account",
    partnerPrograms: "Partner Programs",
    serverError: "Something went wrong! ",
    loading: "Loading...",
    yes: "Yes",
    no: "No",
    benefits: "Benefits",
    programBenefits: "Program Benefits",
    newProgram: "New Program",
    programTiers: "Program Tiers",
    dragDropMessage: "Drag and drop tiers to reorder them",
    tier: "Tier",
    remove: "Remove",
    addTier: "Add Tier",
    editTier: "Edit Tier",
    tierBadge: "Tier Badge",
    tierDescription: "Tier Description",
    tierName: "Tier Name",
    deleteTier: "Delete Tier",
    noRecords: "No data to display.",
    newBenefit: "New Benefit",
    cancel: "Cancel",
    close: "Close",
    addBenefit: "Add Benefit",
    saving: "Saving...",
    existingBenefitName: "A benefit with this name already exists. Please choose a different name.",
    highestTierReached: "You are in the highest tier!",
    failFetchData: "Error: Unable to fetch data. Contact your administrator",
    failInitialize: "Failed to initialize components",
    failSave:"Failed to save the record. Please try again or contact your administrator.",
    benefitDuplicateNameError: "A benefit with this name already exists. Please choose a different name.",
    save: "Save",
    deleteBenefit: "Delete Benefit",
    deleteBenefitMessage: "Are you sure to delete this benefit and all the assignments?",
    delete: "Delete",
    failDelete:"Failed to delete the record. Please try again or contact your administrator.",
    edit: "Edit",
    first: "First",
    last: "Last",
    next: "Next",
    previous: "Previous",
    editBenefit: "Edit Benefit",
    noProgramsAvailable: "No Program is available.",
    enrollments: "Enrollments",
    partnerEnrollments: "Partner Enrollments",
    newEnrollment: "New Enrollment",
    allPrograms: "All Programs",
    noTiers: "No tiers available for the selected program.",
    editEnrollment: "Edit Enrollment",
    addEnrollment: "Add Enrollment",
    deleteEnrollmentMessage: "Are you sure to delete this enrollment and all the information?",
    deleteEnrollment: "Delete Enrollment",
    duplicateEnrollment: "This partner already has an active enrollment in this program.",
    noAccountsAvailable: "No Account is available.",
    deleteProgramMessage: "You are about to delete this program and all of its data, including tiers, enrollments, and criteria. This action cannot be undone.",
    deleteProgram: "Delete Partner Program",
    setting: "Setting",
    settings: "Settings",
    partnerProgramStatus: "Partner Program Status",
    enablePartnerProgram: "Enable Partner Program",
    partnerProgramIsDisabled: "Partner Program is Disabled",
    partnerProgramIsActive: "Partner Program is Active",
    confirmationDisablingPartnerProgram: "Are you sure you want to disable Partner Program?",
    confirmationDisablingPartnerProgramMessage: "Tier visibility and benefits will be hidden.",
    ok: "Ok",
    settingsSaved: "Settings saved successfully!",
    back: "Back",
    benefit: "Partner Program Benefit",
    benefitTypeError: "Benefit Type is required.",
    benefitNameRequired: "Benefit Name is required.",
    enrollmentDateMustBeBeforeExpiration: "Enrollment Date must be before expiration date.",
    editProgram: "Edit Program",
    addProgram: "Add Program",
    programNameRequired: "Program name is required.",
    tierNameRequired: "Tier name is required.",
    tierInexNameRequired: "Tier {idx}: name is required.",
    duplicateTierName: "Duplicate tier name",
    unsavedChangesConfimation: "You have unsaved changes. Are you sure you want to close?",
    minTierError: "You must configure at least one tier.",
    maxTierError: "You can configure at most six tiers.",
    duplicateIndexTierName: "Tier {idx}: duplicate name {tierName}.",
    missingProgramId: "Not a valid program",
    program: "Program",
    newTier: "New Tier",
    clone: "Clone",
    configure: "Configure",
    noTiersDefined: "No tiers defined.",
    criteria: "Program Criteria",
    noCriteriaDefined: "No criteria defined.",
    addCriterion: "Add Criterion",
    noBenefitDefined: "No benefit defined.",
    atLeastOneTierRequired: "Enter at least one tier for program",
    confirm: "Confirm",
    noMoreAvailableBenefits: "No more benefits available to add.",
    missingProgramIdOrPartnerAccountId: "Missing infromation. Contact administration.",
    noProgramEnrollments: "You are not enrolled in a partner program. Please contact your administrator.",
    noPartnerAccount: "You must be a partner to see this page.",
    certifications: "Certifications",
    numberofOpportunities: "Number of Opportunities",
    opportunityRevenue: "Opportunity Revenue",
    numberofDealRegistrations: "Number of Deal Registrations",
    custom: "Custom",
    configureCriterion: "Configure Criterion",
    createCriterion: "Create Criterion",
    create: "Create",
    criteriaDuplicateNameError: "A criteria with this name already exists. Please choose a different name.",
    quantity: "Quantity",
    revenue: "Revenue",
    criterionNameRequiredError: "Criterion name is required.",
    criterionTypeRequiredError: "Criterion type is required.",
    trainingCourseRequiredError: "Training course is required for Certifications.",
    rollupFieldRequiredError: "Rollup field is required for Revenue.",
    attainmentDateFieldRequired: "Attainment date field is required",
    unitofMeasureRequired: "Unit of measure is required.",
    editCriterion: "Edit Criterion",
    missingFields: "There is missing informa",
    trainingCourse: "Training Course",
    name: "Name",
    type: "Type",
    filterLogic: "Filter Logic (optional)",
    filterLogicPlaceHolder: "e.g. (1 and 2) or 3",
    reset: "Reset",
    addFilter: "Add Filter",
    equals: "equals",
    notEquals: "not equals to",
    includes: "includes",
    excludes: "excludes",
    greaterThan: "greater than",
    lessThan: "less than",
    lessOrEqual: "less or equal",
    greaterOrEqual: "greater or equal",
    contains:"contains",
    notContain: "does not contain",
    startWith: "starts with",
    notStartWith: "does not start with",
    partnerAccount: "Partner Account",
    enrollmentDate: "Enrollment Date",
    expirationDate: "Expiration Date",
    sequence: "Sequence",
    description: "Description",
    badge: "Badge",
    deleteThisTier: "Delete This Tier?",
    deleteTierMessage: "This will permanently delete the tier, along with all its associated program criteria and benefits. This action cannot be undone.",
    rollupField: "Rollup Field",
    attainmentDateField: "Attainment Date Field",
    unitofMeasure: "Unit of Measure",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    thisMonth: "This Month",
    lastMonth: "Last Month",
    nextMonth: "Next Month",
    last30Days: "Last 30 Days",
    next30Days: "Next 30 Days",
    last90Days: "Last 90 Days",
    next90Days: "Next 90 Days",
    thisFiscalYear: "This Fiscal Year",
    lastFiscalYear: "Last Fiscal Year",
    nextFiscalYear: "Next Fiscal Year",
    thisYear: "This Year",
    lastYear: "Last Year",
    nextYear: "Next Year",
    selectDateOption: "Select Date Option",
    accountRequired: "Partner Account is required.",
    expirationDateRequired: "Expiration Date is required.",
    programRequired: "Partner Program is required.",
    tierRequired: "Partner tier is required.",
    enrollmentDateRequired: "Enrollment Date is required",
}

export default messages