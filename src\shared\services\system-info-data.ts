import DataAccess from "@/shared/services/data-access"
import I18n<PERSON>elper from "@/shared/services/i18n-helper"

export default class SystemInfoData {
    antiForgeryToken: string = ''
    currencyExchangeRates: any[] = []
    company: any = null
    menuItems: any[] = []
    userInfo: any = null
    apps : any[] = []

    get env() {
        const result = {
            production: (import.meta as any).env.VITE_PRODUCTION === 'true',
            baseUrl: (import.meta as any).env.VITE_BASE_URL,
            username: (import.meta as any).env.VITE_USERNAME,
            password: (import.meta as any).env.VITE_PASSWORD,
            refreshToken: (import.meta as any).env.VITE_REFRESH_TOKEN
        }

        return result
    }
    get userId(): string { return this.userInfo?.user?.id }     
    get portalName(): string { return this.company.system.portalName }
    get isGuest(): boolean { return this.userInfo?.user?.guest }
    get timeout(): number { return this.userInfo?.user?.st }
    get activeLanguages(): string[] { return this.company.langauges.map((item: { value: any }) => item.value) }
    get currentLanguage(): string { return this.userInfo?.user?.lang }
    get currentLocale(): string { return this.userInfo?.user?.locale }
    get preferredCurrency(): string { return this.userInfo?.user?.preferred_currency }
    get isMultiCurrency(): boolean { return this.company.system.multi_currency }
    get isDisplayUserCurrency(): boolean { return this.company.system.display_user_currency }
    get userTimezone(): string { return this.userInfo?.user?.user_timezone }

    async loadData() {
        const dataAccess = new DataAccess()
        const path = '/iris/getstartupdata?includeuserinfo=true&includeMenu=true&includeApps=true'
        const response = await dataAccess.execute(path)
      
        if (!response)
            return

        this.currencyExchangeRates = response.exchangeRates
        this.company = response.company
        this.menuItems = response.menuItems
        this.userInfo = response.userInfo
        this.apps = response.apps

        this.setLocaleAndLanguage()
    }

    setLocaleAndLanguage() {
        const i18nHelper = new I18nHelper()
        const currentSystemLocale = i18nHelper.getCurrentLocale()
    
        if (this.currentLocale && this.currentLocale != currentSystemLocale)
          i18nHelper.changeLocale(this.currentLocale)
    
        i18nHelper.changeLanguage(this.currentLanguage)
    }
}