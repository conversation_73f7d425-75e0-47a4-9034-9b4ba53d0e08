import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from "vitest"
import { create<PERSON><PERSON>, setActivePinia } from "pinia"
import { CurrencySymbolFormat } from "@/shared/services/enums"
import { exchangeInfoMock } from "@/shared/tests/mocks/exchange-info"
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import CurrencyHelper from "@/shared/services/currency-helper"
import I18nHelper from "@/shared/services/i18n-helper"
import type SystemInfoData from "@/shared/services/system-info-data"

describe("CurrencyHelper", async () => {
    let currencyHelper: CurrencyHelper
    let i18nHelper: I18nHelper
    let systemInfo: SystemInfoData
    
    beforeAll(async () => {
        const pinia = createPinia()
        setActivePinia(pinia)
        systemInfo = useIrisSystemInfoStore().getData()
        await systemInfo.loadData()
        currencyHelper = new CurrencyHelper()
        i18nHelper = new I18nHelper()
        vi.spyOn(systemInfo, 'preferredCurrency', 'get').mockReturnValue('CAD')
        vi.spyOn(systemInfo, 'isMultiCurrency', 'get').mockReturnValue(true)
        vi.spyOn(systemInfo, 'isDisplayUserCurrency', 'get').mockReturnValue(true)
        vi.spyOn(systemInfo, 'currencyExchangeRates', 'get').mockReturnValue(JSON.parse(exchangeInfoMock))
    })

    beforeEach(() => {
        vi.useFakeTimers()
    })

    afterEach(() => {
        vi.useRealTimers()
    })

    it("Format currency for the current locale", () => {
        const result = currencyHelper.formatCurrency(12.99)

        expect(result).toEqual('CAD 12.99')
    })

    it("Format currency with different currency code and default locale", () => {
        const result = currencyHelper.formatCurrency(12.99, 'EUR')
        
        expect(result).toEqual('EUR 12.99 (CAD 17.09)')
    })

    it("Format currency with different currency code and different locale", () => {
        const result = currencyHelper.formatCurrency(12.99, 'EUR', 'fr-FR')

        expect(result).toEqual('EUR 12,99 (CAD 17.09)')
    })

    it("Get default locale currency symbol", () => {
        const result = currencyHelper.getCurrencySymbolByLocale()

        expect(result).toEqual('$')
    })

    it("Get french currency symbol by changing locale", () => {
        i18nHelper.changeLocale('fr-FR')

        const result = currencyHelper.getCurrencySymbolByLocale()

        expect(result).toEqual('€')
        i18nHelper.resetLocale()
    })

    it("Get french currency symbol without changing locale", () => {
        const result = currencyHelper.getCurrencySymbolByLocale('fr-FR')

        expect(result).toEqual('€')
    })

    it("Get french currency code without changing locale", () => {
        const result = currencyHelper.getCurrencySymbolByLocale('fr-FR', CurrencySymbolFormat.code)

        expect(result).toEqual('EUR')
    })

    it("Get currency symbol by currency ISO code", () => {
        const result = currencyHelper.getCurrencySymbolByCurrencyIsoCode('EUR')

        expect(result).toEqual('€')
    })
})