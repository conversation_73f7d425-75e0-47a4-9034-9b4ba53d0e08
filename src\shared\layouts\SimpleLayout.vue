<script setup lang="ts">
    import { onMounted } from 'vue'
    import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
    import IrisUserDropDown from '@/shared/components/theming-controls/IrisUserDropDown.vue'
    import IrisNavbarMenu from '@/shared/components/theming-controls/IrisNavbarMenu.vue'
    import IrisAppLauncher from '@/shared/components/theming-controls/IrisAppLauncher.vue'
    import IrisLanguagePicker from '@/shared/components/theming-controls/IrisLanguagePicker.vue'
    import IrisSocialLinks from '@/shared/components/theming-controls/IrisSocialLinks.vue'
    import IrisFooterLinks from '@/shared/components/theming-controls/IrisFooterLinks.vue'
    import IrisGlobalSearchBox from '@/modules/global-search/components/IrisGlobalSearchBox.vue'
    import IrisInstanceType from '@/shared/components/theming-controls/IrisInstanceType.vue'
    import IrisCustomFooter from '@/shared/components/theming-controls/IrisCustomFooter.vue'
    import FloatingMenuHelper from '@/shared/services/floating-menu-helper'
    import Common from '@/shared/services/common'
    
    const props = defineProps({
        showMenu: {
            type: Boolean,
            default: true
        }
    })

    const systemInfo = useIrisSystemInfoStore().getData()

    onMounted(() => {
        FloatingMenuHelper.calcSiteBodyPaddingTop(true)

        document.body.classList.add('bg-nav-bg')
        document.body.classList.add('dark:bg-nav-bg-dark')
    })

</script>
<template>
    <div class="sitePage">
        <div class='siteHeaderContainer std-theme-siteHeaderContainer'>
            <div class="site-page-header bg-nav-bg dark:bg-nav-bg-dark border-b border-border-muted dark:border-border-muted-dark" :class="{
                'navbar-fixed fixed z-[1000] w-full' : systemInfo.company.theme.isFloatingMenu
             }">
                <div class="bg-header-bg dark:bg-header-bg-dark" v-if="systemInfo.userInfo">
                    <div class="flex items-center flex-wrap px-4 py-4 md:py-2.5 max-w-page mx-auto" 
                        :class="systemInfo.company.instance.instanceName ? 'justify-between' : 'justify-end'">
                        <div class="flex" v-if="systemInfo.company.instance.instanceName">
                            <IrisInstanceType :value="systemInfo.company.instance"/>
                        </div>
                        <div class="siteHeader-items flex items-center gap-4">
                            <div v-if="systemInfo.company.system.globalSearch">
                                <IrisGlobalSearchBox class="text-header-text dark:text-header-text-dark"/>
                            </div>
                            <IrisAppLauncher 
                                :value="systemInfo.apps" 
                                v-if="showMenu" 
                                class="p-0.5 text-header-text dark:text-header-text-dark
                                opacity-80 hover:opacity-100
                                rounded focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark"/>
                            <IrisLanguagePicker 
                                class="p-0.5 text-header-text dark:text-header-text-dark
                                opacity-80 hover:opacity-100 rounded-full focus:ring-header-text dark:focus:ring-header-text-dark" 
                                :value="systemInfo.company.langauges" 
                                :showLabel=false
                                :showFlags="systemInfo.company.system.languageFlagsEnabled" 
                                :selected="systemInfo.userInfo.user.lang"/>
                            <IrisUserDropDown 
                                :value="systemInfo.userInfo" 
                                :showUserName="false" 
                                :useIcons="true"
                                class="p-0.5 text-header-text dark:text-header-text-dark opacity-80 hover:opacity-100
                                rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"/>
                        </div>
                    </div>
                </div>
                <div class="mag-navbarmenu max-w-page mx-auto">
                    <div class="site-main-nav py-2" v-if="systemInfo.menuItems && showMenu">
                        <IrisNavbarMenu 
                            :logo-url="Common.getBaseUrl() + systemInfo.company.theme.logoUrl"
                            :logo-click-url="systemInfo.company.logoClickUrl"
                            :isMenuLeftAligned=false
                            :value="systemInfo.menuItems"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="siteBody bg-bg-color dark:bg-bg-color-dark">
            <div class="p-4 min-h-[550px] max-w-page mx-auto">
                <RouterView />
            </div>
        </div>
        <div class="py-10 bg-nav-bg text-nav-text border-t border-border-color dark:border-border-color-dark dark:text-nav-text-dark dark:bg-nav-bg-dark">
            <div class="max-w-page mx-auto">
                <div class="footer p-4">
                    <IrisCustomFooter :value="systemInfo.company.customFooterCells" :alignment="systemInfo.company.customFooterCellsAlignment"/>
                    <div class="flex justify-start gap-5 flex-col lg:flex-row lg:gap-20 flex-wrap">
                        <IrisSocialLinks :value="systemInfo.company.socialLinks" class="inline-flex items-center p-4 text-nav-text dark:header-nav-dark opacity-80 hover:opacity-100 rounded-full border border-nav-text dark:border-nav-text-dark" />
                        <div>
                            <IrisFooterLinks :value="systemInfo.company.footerData" class="opacity-80 hover:opacity-100 hover:underline"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>