const messages = {
  partnerProgram: "合作伙伴计划",
  partnerManagement: "合作伙伴管理",
  helponthispage: "此页面帮助",
  search: "搜索",
  programs: "计划",
  account: "账户",
  partnerPrograms: "合作伙伴计划",
  serverError: "发生错误！",
  loading: "加载中...",
  yes: "是",
  no: "否",
  benefits: "福利",
  programBenefits: "计划福利",
  newProgram: "新计划",
  programTiers: "计划层级",
  dragDropMessage: "拖放层级以重新排序",
  tier: "层级",
  remove: "移除",
  addTier: "添加层级",
  editTier: "编辑层级",
  tierBadge: "层级徽章",
  tierDescription: "层级描述",
  tierName: "层级名称",
  deleteTier: "删除层级",
  noRecords: "无数据显示。",
  newBenefit: "新增福利",
  cancel: "取消",
  close: "关闭",
  addBenefit: "添加福利",
  saving: "保存中...",
  existingBenefitName: "该名称的福利已存在。请选择其他名称。",
  highestTierReached: "您已达最高层级！",
  failFetchData: "错误：无法获取数据。请联系管理员。",
  failInitialize: "初始化组件失败",
  failSave: "保存记录失败。请重试或联系管理员。",
  benefitDuplicateNameError: "该名称的福利已存在。请选择其他名称。",
  save: "保存",
  deleteBenefit: "删除福利",
  deleteBenefitMessage: "确定要删除此福利及其所有分配吗？",
  delete: "删除",
  failDelete: "删除记录失败。请重试或联系管理员。",
  edit: "编辑",
  first: "首页",
  last: "末页",
  next: "下一页",
  previous: "上一页",
  editBenefit: "编辑福利",
  noProgramsAvailable: "无可用计划。",
  enrollments: "注册",
  partnerEnrollments: "合作伙伴注册",
  newEnrollment: "新注册",
  allPrograms: "所有计划",
  noTiers: "所选计划暂无层级。请选择其他计划或联系管理员。",
  editEnrollment: "编辑注册",
  addEnrollment: "添加注册",
  deleteEnrollmentMessage: "确定要删除此注册及其所有信息吗？",
  deleteEnrollment: "删除注册",
  duplicateEnrollment: "该合作伙伴已在此计划中注册。",
  noAccountsAvailable: "无可用账户。",
  deleteProgramMessage: "您将删除该计划及其所有数据，包括层级、注册和条件。此操作不可撤销。",
  deleteProgram: "删除合作伙伴计划",
  setting: "设置",
  settings: "设置",
  partnerProgramStatus: "合作伙伴计划状态",
  enablePartnerProgram: "启用合作伙伴计划",
  partnerProgramIsDisabled: "合作伙伴计划已禁用",
  partnerProgramIsActive: "合作伙伴计划已启用",
  confirmationDisablingPartnerProgram: "确定要禁用合作伙伴计划吗？",
  confirmationDisablingPartnerProgramMessage: "层级和福利将被隐藏。",
  ok: "确定",
  settingsSaved: "设置已成功保存！",
  back: "返回",
  benefit: "合作伙伴计划福利",
  benefitTypeError: "必须选择福利类型。",
  benefitNameRequired: "必须填写福利名称。",
  enrollmentDateMustBeBeforeExpiration: "注册日期必须早于过期日期。",
  editProgram: "编辑计划",
  addProgram: "添加计划",
  programNameRequired: "必须填写计划名称。",
  tierNameRequired: "必须填写层级名称。",
  tierInexNameRequired: "层级 {idx}：必须填写名称。",
  duplicateTierName: "重复的层级名称",
  unsavedChangesConfimation: "您有未保存的更改。确定要关闭吗？",
  minTierError: "至少配置一个层级。",
  maxTierError: "最多配置六个层级。",
  duplicateIndexTierName: "层级 {idx}：重复名称 {tierName}。",
  missingProgramId: "无效的计划",
  program: "计划",
  newTier: "新层级",
  clone: "克隆",
  configure: "配置",
  noTiersDefined: "未定义任何层级。",
  criteria: "计划条件",
  noCriteriaDefined: "未定义任何条件。",
  addCriterion: "添加条件",
  noBenefitDefined: "未定义任何福利。",
  atLeastOneTierRequired: "请为计划填写至少一个层级",
  confirm: "确认",
  noMoreAvailableBenefits: "无更多可添加的福利。",
  missingProgramIdOrPartnerAccountId: "信息缺失。请联系管理员。",
  noProgramEnrollments: "您未注册任何合作伙伴计划。请联系管理员。",
  noPartnerAccount: "您必须是合作伙伴才能查看此页面。",
  certifications: "认证",
  numberofOpportunities: "机会数量",
  opportunityRevenue: "机会收入",
  numberofDealRegistrations: "交易注册数量",
  custom: "自定义",
  configureCriterion: "配置条件",
  createCriterion: "创建条件",
  create: "创建",
  criteriaDuplicateNameError: "该名称的条件已存在。请选择其他名称。",
  quantity: "数量",
  revenue: "收入",
  criterionNameRequiredError: "必须填写条件名称。",
  criterionTypeRequiredError: "必须选择条件类型。",
  trainingCourseRequiredError: "认证类型必须选择培训课程。",
  rollupFieldRequiredError: "收入类型必须填写汇总字段。",
  attainmentDateFieldRequired: "必须填写达成日期字段",
  unitofMeasureRequired: "必须填写计量单位。",
  editCriterion: "编辑条件",
  missingFields: "存在缺失信息",
  trainingCourse: "培训课程",
  name: "名称",
  type: "类型",
  filterLogic: "过滤逻辑（可选）",
  filterLogicPlaceHolder: "例如：(1 和 2) 或 3",
  reset: "重置",
  addFilter: "添加过滤器",
  equals: "等于",
  notEquals: "不等于",
  includes: "包含",
  excludes: "不包含",
  greaterThan: "大于",
  lessThan: "小于",
  lessOrEqual: "小于等于",
  greaterOrEqual: "大于等于",
  contains:"包含",
  notContain: "不包含",
  startWith: "以...开始",
  notStartWith: "不以...开始",
  partnerAccount: "合作伙伴账户",
  enrollmentDate: "注册日期",
  expirationDate: "过期日期",
}

export default messages