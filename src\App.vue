<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { registerLicense } from '@syncfusion/ej2-base'
  import { WebSocketChannel } from '@/shared/services/enums'
  import { useIrisWebSocket } from '@/shared/composables/iris-websocket'
  import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
  import { WebSocketSubscriber } from '@/shared/services/websocket-subscriber'
  import { syncFusionLicenseKey } from '@/shared/services/constants'
  import Auth from '@/shared/services/auth'
  import SessionHelper from '@/shared/services/session-helper'
  import LanguageHelper from '@/shared/services/language-helper'
  import FlatLayout from '@/shared/layouts/FlatLayout.vue'
  import ElegantLayout from '@/shared/layouts/ElegantLayout.vue'
  import SmoothnessLayout from '@/shared/layouts/SmoothnessLayout.vue'
  import MagentrixLayout from '@/shared/layouts/MagentrixLayout.vue'
  import SimpleLayout from '@/shared/layouts/SimpleLayout.vue'
  import SetupLayout from '@/shared/layouts/SetupLayout.vue'
  import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
  
  // register syncfusion
  registerLicense(syncFusionLicenseKey)

  const router = useRouter()
  const ready = ref(false)
  const startUpError = ref("")
  const sessionHelper = new SessionHelper()
  const languageHelper = new LanguageHelper()
  let layout = "Flat"

  // Composables
  const websocket = useIrisWebSocket()
  const systemInfoStore = useIrisSystemInfoStore()
  const systemInfo = systemInfoStore.getData()
  let pushNotificationSubscriberId = ''

  router.afterEach((to, from) => {
    if (ready.value && to.name !== from.name)
      sessionHelper.navigated = true
  })

  // Hooks
  onMounted(async () => {
    systemInfo.antiForgeryToken = Auth.getAntiForgeryToken()

    if (systemInfo.env.production) {
      try {
        await getStartupData()
        enableSessionManagement()
      } catch  (error:any) {
        startUpError.value = error.message
      }
    }
    else {
      const response = await Auth.createSession(systemInfo.env.refreshToken)
      
      if (response.success) {
        try {
          await getStartupData()
          ready.value = true
        }
        catch (error:any) {
          startUpError.value = error.message
        }
      }
      else {
        startUpError.value = 'Failed to authenticate'
        //TODO: show a nice message on the screen with a general message
        console.warn(languageHelper.getMessage('createUserSessionFailed'))
      }
    }

    pushNotificationSubscriberId = WebSocketSubscriber.add(WebSocketChannel.IrisCore, getStartupData, true)
    await websocket.initialize()

    ready.value = true
  })

  onUnmounted(() => {
    if (pushNotificationSubscriberId)
      WebSocketSubscriber.remove(pushNotificationSubscriberId)

    sessionHelper.stop()
    websocket.disconnect()
  })

  async function getStartupData() {
      await systemInfo.loadData()
      layout = systemInfo.company.theme.themeName
  }

  function enableSessionManagement() {
    const timeoutSeconds = Math.floor(systemInfo.timeout / 2)
    sessionHelper.start(timeoutSeconds)
  }

  function getLayoutName() {
    // Access the current route
    const route = router.currentRoute.value 
    // Get layout from route meta
    const layoutFromRoute = route.meta?.layout as string
    let theme = layout

    if (layoutFromRoute)
      theme = layoutFromRoute

    return theme
  }

  function getLayout() {   
    const theme = getLayoutName()
    
    if (theme == "Flat")
      return FlatLayout
    else if (theme == "Smoothness")
      return SmoothnessLayout
    else if (theme == "Magentrix")
      return MagentrixLayout
    else if (theme == "Simple")
      return SimpleLayout
    else if (theme == "Elegant")
      return ElegantLayout
    else if (theme == "Setup")
      return SetupLayout
    else
      return FlatLayout
  }

  function shouldLoadLayout() {
    const route = router.currentRoute.value
    const result = route.path == window.location.pathname
    return result
  }
</script>

<template>
  <div class="bg-bg-color text-text-color-400 dark:text-text-color-400-dark">
    <div class="h-screen grid place-items-center max-w-page m-auto" v-if="!ready && !startUpError">
        <div class="flex justify-center content-center">
          <IrisIcon name="spinner-third" class="inline w-10 h-10 animate-spin fill-primary"/>
          <span class="ms-3 text-4xl">{{ languageHelper.getMessage("loading") }}</span>
        </div>
    </div>
    <div v-if="startUpError" class="h-screen grid place-items-center max-w-page m-auto">
        <div class="border rounded-lg p-4 border-border-color">
          <div class="flex justify-center content-center flex-col gap-3">
            <div class="flex justify-center items-center gap-1">
              <IrisIcon name="cloud-xmark" class="inline w-10 h-10"/>
              <span class="text-[1.9rem]">{{ languageHelper.getMessage("error") }}</span>
            </div>
            <div>
              <span class="text-xl">{{ startUpError }}</span>
            </div>
          </div>
        </div>
    </div>
  </div>
  
  <div class="contentWrapper bg-bg-color" v-if="ready">
    <component v-if="shouldLoadLayout()" :is="getLayout()"/>
  </div>
</template>
