<script setup lang="ts">
import { ref, onUnmounted } from 'vue'

let socket: WebSocket
let token: String = ''
const status = ref('')
const message = ref('')
const messages = ref<any[]>([])
const connected = ref(false)
const input = ref()
const url = ref('wss://localhost:7252/api/iriswebsocket/connect')
const endpoints = [
    { name: 'Kestrel', url: 'wss://localhost:7252/api/iriswebsocket/connect'},
    { name: 'IIS', url: 'wss://websocket.magentrix.com/api/iriswebsocket/connect'}
]

onUnmounted(() => disconnect())

function login() {
    const loginUrl = url.value.replace('wss:', 'https:').replace('connect', 'login')
    const options: any = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'ApiKey': 'E750D6AB-9D44-4F35-9D74-29417FCE0B25'
        }
    }

    return fetch(loginUrl, options).then(response => response.text())
}

function initialize() {
    if (token)
        connect()
    else
        login().then(data => {
            token = data
            connect()
        })
}

function connect() {
    if (!isSocketOpen()) {
        const urlWithToken = `${url.value}?token=${token}`
    
        socket = new WebSocket(urlWithToken)
        socket.onopen = e => {
            connected.value = true
            status.value = "Connected"
        }
        socket.onclose = e => {
            connected.value = false
            status.value = "Disconnected"
        }
        socket.onerror = err => {
            status.value = 'An error happened. Please check the console.'
            console.error('Websocket Error: ', err)
        }
        socket.onmessage = (e: any) => messages.value.push(e.data)
    }
}

function disconnect() {
    if (isSocketOpen())
        socket.close()
}

function sendMessage() {
    if (isSocketOpen())
        if (message.value)
            socket.send(message.value)
        else
            status.value = 'Please type a message and try again.'
    else
        status.value = "Can't send the message! Not connected to websocket."

    message.value = ''
    input.value.focus()
}

function isSocketOpen() {
    const result = socket && socket.readyState == socket.OPEN
    return result
}

function clearMessages() {
    messages.value = []
}

function urlChange() {
    disconnect()
    clearMessages()
    setTimeout(() => {
        initialize()
    }, 0)
}

function handleEnter(e: any) {
    if (e.keyCode == 13)
        sendMessage()
}
</script>

<template>
    <div class="ms-5 my-5 w-1/2">
        <div>
            <select v-model="url" @change="urlChange()" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option v-for="endpoint in endpoints" :value="endpoint.url">{{ endpoint.name }}</option>
            </select>
        </div>

        <div class="my-2">
            <button class="btn-primary" @click="initialize()" :class="{ disabled: connected }" :disabled="connected">Connect</button>
            <button class="btn-primary ms-2" @click="disconnect()" :class="{ disabled: !connected }" :disabled="!connected">Disconnect</button>
        </div>

        <p class="my-3 text-red-600" v-if="status"><b>Last Status:</b> {{ status }}</p>

        <div class="flex">
            <input type="text" class="iris-textbox-sm" maxlength="100" v-model="message" ref="input" @keyup="handleEnter" :class="{ disabled: !connected }" :disabled="!connected" />
            <button class="btn-primary ms-2" @click="sendMessage()" :class="{ disabled: !connected }" :disabled="!connected">Send Message</button>
        </div>

        <p class="my-2"><b>Messages:</b></p>
        <div class="p-3 border-solid border-black border-2 rounded-lg">
          <ul>
            <li v-for="message in messages">{{ message }}</li>
          </ul>
        </div>
        <button class="mt-3 btn-primary" @click="clearMessages()" :class="{ disabled: messages.length == 0 }" :disabled="messages.length == 0">Clear Messages</button>
    </div>
</template>

<style scoped>
.disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
</style>