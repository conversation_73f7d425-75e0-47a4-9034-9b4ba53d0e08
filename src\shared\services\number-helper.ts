
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import I18nHelper from '@/shared/services/i18n-helper'

export default class NumberHelper {
    i18nHelper = new I18nHelper()
    systemInfo = useIrisSystemInfoStore().getData()

    formatNumber(value: number, {
            locale = this.systemInfo.currentLocale, 
            isPercentage = false,
            minimumIntegerDigits = undefined,
            minimumFractionDigits = undefined,
            maximumFractionDigits = undefined,
            minimumSignificantDigits = undefined,
            maximumSignificantDigits = undefined
        }: any = {}): string {

            if (value == undefined)
                return ''

            const result = this.i18nHelper.formatNumber(value, locale, isPercentage, {
                minimumIntegerDigits,
                minimumFractionDigits,
                maximumFractionDigits,
                minimumSignificantDigits,
                maximumSignificantDigits
            })

        return result
    }
}