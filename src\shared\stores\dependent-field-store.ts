import { ref } from 'vue'
import { defineStore } from 'pinia'
import Common from '@/shared/services/common'

export interface DependentFieldMetadata {
  formId: string;
  entity:string;
  field:string;
  parentField:string;
  invalidate: Function
}

export const useDependentFieldStore = defineStore('irisDependentFieldStore', () => {
  var registry = ref<{ pubId: string, data: DependentFieldMetadata }[]>([])
  
  function registerComponent(data: DependentFieldMetadata) : string {
    let retVal = ''
    const item = registry.value.find(f => 
         f.data.formId == data.formId 
      && f.data.entity == data.entity
      && f.data.field == data.field
      && f.data.parentField == data.parentField)

    if (item == null || item == undefined) {

      // validate
      if (!data.entity)
        throw new Error("Invalid Subscriber Component Data, missing: Entity.")

      //if (!data.formId)
      //  throw new Error("Invalid Subscriber Component Data, missing: Form ID.")

      if (!data.field)
        throw new Error("Invalid Subscriber Component Data, missing: field.")

      if (!data.invalidate)
        throw new Error("Invalid Subscriber Component Data, missing: invalidate callback.")

      retVal = Common.newGuid()
      registry.value.push({
        pubId : retVal,
        data : data
      })
    }

    return retVal
  }

  function removeComponent(pubId: string) : void {
    registry.value = registry.value.filter(x => x.pubId != pubId)
  }

  function dispatch(pubId: string, value: string) : void {
    const comp = registry.value.find(item => item.pubId == pubId)

    if (comp) {
      // has parent of another component changed
      const matches = registry.value.filter(f => f.data.formId == comp.data.formId 
        && f.data.entity == comp.data.entity
        && f.data.parentField == comp.data.field)
      
      if (matches && matches.length > 0) {
        matches.forEach(child => {
          child.data.invalidate(value)
        })
      }
    }
  }

  return { dispatch, registerComponent, removeComponent }
})
