<script setup lang="ts">
import { ref,onMounted } from 'vue'
import { useIrisPage } from '@/shared/composables/iris-page'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { TabData, TabRouteData } from '@/shared/services/tab-data'
import { GridColumn } from '@/shared/services/grid-column'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { GridAction } from '@/shared/services/grid-action'
import { RequestMethod } from '@/shared/services/enums'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import DataAccess from '@/shared/services/data-access'
import IrisTabs from '@/shared/components/general-controls/IrisTabs.vue'
import IrisSection from '@/shared/components/form-controls/IrisSection.vue'
import IrisField from '@/shared/components/form-controls/IrisField.vue'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'
import IrisRouteTabs from '@/shared/components/general-controls/IrisRouteTabs.vue'
import IrisStaticAssets from '@/shared/components/general-controls/IrisStaticAssets.vue'
import IrisCheckbox from '@/shared/components/form-controls/IrisCheckbox.vue'

const isModelVisible = ref(false)
const base = useIrisPage()
const model:any = ref(null)
const store = useIrisFormControlStore()
const tabItems = ref<TabData[]>()
const playNow = ref(false)
const _cols = ref<GridColumn[]>()
const showMessage = ref(false)
const routeItems = ref<TabRouteData[]>()
const gridActions = ref<GridAction[]>()

tabItems.value = []
tabItems.value!.push({ id:"tab1", label:"Opps" } as TabData)
tabItems.value!.push({ id:"tab2", label:"Files" } as TabData)
tabItems.value!.push({ id:"tab3", label:"Settings" } as TabData)

routeItems.value = []
routeItems.value.push({ id:"rtab1", label:"Tab 1", path:"/global-search" } as TabRouteData)
routeItems.value.push({ id:"rtab2", label:"Tab 2", path:"/partner-tiering" } as TabRouteData)

gridActions.value = []
gridActions.value!.push({ label:"Global Search", path:"/global-search/?kw={!Name}", icon:"globe" } as GridAction)
gridActions.value!.push({ label:"Global Search - Stage", path:"/global-search/?kw={!StageName}" } as GridAction)

onMounted(async () => {
  const dataAccess = new DataAccess()

  base.setPageTitle("Sepehr's Page")
  model.value = await dataAccess.execute('iris/GetSampleIrisData', null, RequestMethod.get)
  console.log(model.value)

  model.value.scholar.Exit_Reason__c = 'Guardian Initiated'
  console.log(model.value.opps)

  populateGridCols()
})

function populateGridCols() {
    _cols.value = []

    const column1 = GridColumn.create({
        name: 'Name',
        sortExpression: 'Name',
        sortDirection: 'A'
    })
    _cols.value?.push(column1)

    const column2 = GridColumn.create({
        name: 'Stage',
        sortExpression: 'Stage',
        sortDirection: 'A'
    })
    _cols.value?.push(column2)

    const column3 = GridColumn.create({
        name: 'RecordTypeId',
        sortExpression: 'RecordType.Name',
        sortDirection: 'A',
        mode: FormControlModeEnum.text
    })
    _cols.value?.push(column3)

    _cols.value?.push(GridColumn.create({
        name: 'IsClosed',
        sortExpression: 'IsClosed',
        sortDirection: 'A'
    }))

    _cols.value?.push(GridColumn.create({
        name: 'CurrencyIsoCode',
        sortExpression: 'CurrencyIsoCode',
        sortDirection: 'A'
    }))

    _cols.value?.push(GridColumn.create({
        name: 'Amount',
        sortExpression: 'Amount',
        sortDirection: 'A'
    }))

    _cols.value?.push(GridColumn.create({
        name: 'CloseDate',
        sortExpression: 'CloseDate',
        sortDirection: 'A'
    }))
}

function showModal() {
    isModelVisible.value = true
}

function onModalHide() {
    isModelVisible.value = false
}

function toggleVideo() {
    playNow.value = !playNow.value
}

function filesSelected(items : any) {
console.log(items)
}
</script>

<template>
    <IrisStaticAssets id="test01" :showSelectButton="true" @on-select='filesSelected' :multiSelect="false"></IrisStaticAssets>
    <br/>
    <br/>
    <div class="p-5">
        <!-- <IrisIcon name="license" width="2rem" height="2rem"/>

        <button type="button" class="btn btn-primary" @click="showMessage=true">Click Me</button> -->
        
        <IrisMessageBox 
            type="Confirm"
            message="Are you sure?"
            title="Hi!" 
            :fullWidthButtons="true"
            :show="showMessage"
            @onPrimaryClick="showMessage=false" 
            @on-cancel-click="showMessage=false" />

        <iris-tabs :items="tabItems!" mode="Underlined" :visible="true">
            <template #tab1>
                View Recent Opps
            </template>
            <template #tab2>
                Hellow world 2
            </template>
            <template #tab3>
                Hellow world 3
            </template>
        </iris-tabs>

        <IrisGrid v-if="model" 
            id="mygrid" 
            :selectable="true"
            :value="model.opps" 
            :cols="_cols!"
            :actions="gridActions"
            :multi-select="true"></IrisGrid>

        <br/>
    

        <div class="p-5 border border-b-0 border-border-color rounded-t-xl">
            <strong>New Contact</strong>
        </div>
        <IrisForm id="f1" v-if="model">
            <!-- <IrisDateTime :id="`dt343`" 
              type="year-month"
              :value="model.opps[1]"
              field="CloseDate"/>

            <IrisDateTime :id="`dt34553`" 
              type="year-month"
              :value="model.opps[2]"
              field="CloseDate"/> -->

            <div v-for="i in 2">
                <IrisSection :id="'section1'+i" cols="two" :title="`My Section ${i}`">
                    
                    <IrisField 
                        :id="`mpc${i}`"
                        :value="model.scholar"
                        field="Exit_Reason__c" />

                    <IrisField
                        :id="'mp'+i"
                        :value="model.scholar"
                        field="Exit_Reason_cont__c" />

                    <IrisField 
                        :id="'someField'+i"
                        label="Account" 
                        :value="model.contact"
                        field="AccountId"
                        mode="html" />

                    <IrisField 
                        :id="'someField2'+i"
                        label="Account" 
                        :value="model.contact"
                        field="AccountId" 
                        hint="account 2 hint"
                        required/>

                    <IrisMultiPicklist 
                        :id="'scholar'+i" 
                        :value="model.scholar"
                        field="Record_Type__c" hint="Record Type hint"/>

                    <IrisCheckbox :id="'ss'+i" label="Is Closed" hint="Sample toggle hint" />  
                </IrisSection>
            </div>
            
        </IrisForm>
        
        <br/>
        
        <!-- <button @click="toggleVideo" type="button" class="btn-primary">Play: {{ playNow }}</button>
        <div class="w-96">
            <iris-video-box 
                src="https://flowbite.com/docs/videos/flowbite.mp4" 
                @onEnded="playNow=false"
                :play="playNow"
                :controls="false">
            </iris-video-box>
            <br/>
            <div>
                <iris-video-box 
                    src="https://vimeo.com/*********" 
                    @onEnded="playNow=false"
                    :play="playNow"
                    :controls="false">
                </iris-video-box>
            </div>
            <iris-video-box 
                src="https://www.youtube.com/watch?v=EqWWWk5EdXY&list=PLRaU5X0id3xbTkVsorMzItCNNp8qVLwQ8" 
                @onEnded="playNow=false"
                :play="playNow"
                :controls="false">
            </iris-video-box>
        <br/>
        <br/>


        </div>
         -->
        
        <br/>
        

        <br/>
        <iris-tabs :items="tabItems!" mode="Pills" :visible="true">
            <template #tab1>
                Hellow world
            </template>
            <template #tab2>
                Hellow world 2
            </template>
            <template #tab3>
                Hellow world 3
            </template>
        </iris-tabs>

        <br/>
        <iris-tabs :items="tabItems!" mode="Default" :visible="true">
            <template #tab1>
                Hellow world
            </template>
            <template #tab2>
                Hellow world 2
            </template>
            <template #tab3>
                Hellow world 3
            </template>
        </iris-tabs>

        <nav class="my-3">
            <RouterLink to="/">Test</RouterLink> |
            <RouterLink to="/global-search">Global Search</RouterLink> | 
            <RouterLink to="/sam-test">Sam Test</RouterLink> |
            <RouterLink to="/nafis-test">Nafis Test</RouterLink>
        </nav>
    </div>

    <hr/>

    <iris-route-tabs :items="routeItems!"></iris-route-tabs>
</template>