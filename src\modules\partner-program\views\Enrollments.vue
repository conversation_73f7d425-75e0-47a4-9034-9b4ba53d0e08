<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import { GridColumn } from '@/shared/services/grid-column'
import { DropDownItem } from '@/shared/services/dropdown-item'
import { GridAction, GridActionMode } from '@/shared/services/grid-action'
import { useIrisPage } from '@/shared/composables/iris-page'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import DataAccess from '@/shared/services/data-access'
import Common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisPagination from '@/shared/components/general-controls/IrisPagination.vue'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import IrisDateTime from '@/shared/components/form-controls/IrisDateTime.vue'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisValidationSummary from '@/shared/components/form-controls/IrisValidationSummary.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'
import IrisField from '@/shared/components/form-controls/IrisField.vue'

// Interfaces
interface Program {
    Id: string
    Name: string
}

interface ProgramTier {
    Id: string
    Name: string
    ProgramId: string
}

// Props
const props = defineProps({
    enablePagination: {
        type: Boolean,
        default: true,
    },
    size: {
        type: Number,
        default: 10,
    }
})

// Constants and Refs
const languageHelper = new LanguageHelper()
const base = useIrisPage()
const dataAccess = new DataAccess()
const enrollments = ref([])
const columns: GridColumn[] = []
const isModelVisible = ref(false)
const enrollmentModel: any = ref(null)
const isSubmitting = ref(false)
const isLoading = ref(false)
const isModalLoading = ref(false)
const formErrors = ref<string[]>([])
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')
const selectedProgram = ref<string>("")
const programs = ref<DropDownItem[]>([])
const programTiers = ref<DropDownItem[]>([])
const gridActions = ref<GridAction[]>()
const totalRows = ref(0)
const pageIndex = ref(0)
const showDeleteMessageBox = ref(false)
const pendingDeleteId = ref<string | null>(null)
const searchTerm = ref('')
const isAccountLoading = ref(false)
const isTierLoading = ref(false)

gridActions.value = []
gridActions.value.push({ 
        label: languageHelper.getMessage("edit"),
        path: "Edit", type: GridActionMode.Button,
        icon: "pencil"
    } as GridAction)
gridActions.value.push({
        label: languageHelper.getMessage("delete"),
        path: "Delete", type: GridActionMode.Button,
        icon: "trash-can"
    } as GridAction)

// Hooks
onMounted(async () => {
    base.setPageTitle(languageHelper.getMessage('partnerProgram'))
    
    try {
        columns.push(
            GridColumn.create({ 
                name: 'ProgramId', 
                sortExpression: 'ProgramId', 
                field: 'ProgramId', 
                label: languageHelper.getMessage("program"), 
                component: "IrisGridLink", 
                data: { urlPattern: '/setup/partner-program/programs/{!ProgramId}' }  
            }),
            GridColumn.create({ 
                name: 'PartnerAccountId', 
                sortExpression: 'PartnerAccountId', 
                field: 'PartnerAccountId', 
                label: languageHelper.getMessage("account") 
            }),
            GridColumn.create({ 
                name: 'ProgramTierId', 
                sortExpression: 'ProgramTierId', 
                field: 'ProgramTierId', 
                label: languageHelper.getMessage("tier"), 
                mode: FormControlModeEnum.text 
            }),
            GridColumn.create({ 
                name: 'EnrollmentDate', 
                sortExpression: 'EnrollmentDate', 
                field: 'EnrollmentDate', 
                label: languageHelper.getMessage("enrollmentDate") 
            }),
            GridColumn.create({ 
                name: 'ExpirationDate', 
                sortExpression: 'ExpirationDate', 
                field: 'ExpirationDate', 
                label: languageHelper.getMessage("expirationDate"), 
                sortDirection: 'D', 
                selected: true 
            })
        )

        await loadPrograms()
        await loadData('EnrollmentDate', 'D', 0, '', null)

    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failInitialize")
    }
})

// Computed
const isSaveDisabled = computed(() => isSubmitting.value || formErrors.value.length > 0)
const isEditMode = computed(() => Boolean(enrollmentModel.value?.Id))

watch(() => enrollmentModel.value?.PartnerAccountId, async () => {
    const msg = languageHelper.getMessage("duplicateEnrollment")

    removeError(msg)

    if (enrollmentModel.value?.PartnerAccountId)
        await validateDuplicateEnrollment()
})
// Functions
function onActionClicked(actionName: string, data: any) {
    if (actionName === 'Edit') {
        enrollmentModel.value = { ...data }
        const programId = data.ProgramId || ''
        const existingTierId = data.ProgramTierId || ''
        const existingAccountId = data.PartnerAccountId || ''

        handleProgramSelection(programId, existingAccountId, existingTierId).then(() => isModelVisible.value = true)
    }
    else if (actionName === 'Delete') {
        pendingDeleteId.value = data.Id ?? null
        showDeleteMessageBox.value = true
    }
}

async function loadData(sortExpr: string, sortDirection: string, pageIndex: number, keyword: string = '', selectedProgram: string | null = null) : Promise<void> {
    isLoading.value = true
    showErrorMessage.value = false

    try {
        const endpoint = `/partnerprogramenrollment/getenrollments`
        const data: any = {
            sortExpression: sortExpr,
            sortDirection: sortDirection,
            keyword: keyword.trim(),
            programId: selectedProgram || null
        }

        data.size = props.enablePagination ? props.size : 0
        data.pinx = props.enablePagination ? pageIndex : 0
        
        const url = Common.constructUrl(endpoint, data)
        const response = await dataAccess.execute(url, RequestMethod.post)

        if (!response) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            enrollments.value = []
            isLoading.value = false
            return
        }
        if (!response.Records || !response.Records.length || !response.Count || !Array.isArray(response.Records)) {
            enrollments.value = []
            isLoading.value = false
            return
        }

        totalRows.value = response.Count
        enrollments.value = response.Records || []
    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
        enrollments.value = []
    } finally {
        isLoading.value = false
    }
}

async function loadPrograms() {
    try {
        const response = await dataAccess.execute('/partnerprogram/getallprograms')

        if (!response || !response.programs) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            programs.value = []
            return
        }

        if (!response.programs.length) {
            programs.value = []
            return
        }

        programs.value = response.programs.map((program: Program) => ({
            value: program.Id,
            label: program.Name
        }))

    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    }
}

async function showModal() {
    isModalLoading.value = true
    showErrorMessage.value = false
    formErrors.value = []
    isSubmitting.value = false

    try {
        await getNewEnrollment()

        if (!enrollmentModel.value) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            return
        }

        const programId = enrollmentModel.value.ProgramId || ""

        await handleProgramSelection(programId)
        isModelVisible.value = true
    } catch (error) {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    } finally {
        isModalLoading.value = false
    }
}
async function loadProgramTiers(programId: string) {
    programTiers.value = []
    isTierLoading.value = true

    try {
        const payload = { programId }
        const response = await dataAccess.execute('partnerprogramtier/gettiersbyprogram', payload)

        if (response && Array.isArray(response.tiers)) {
            programTiers.value = response.tiers.map((tier: ProgramTier) => ({
                value: tier.Id,
                label: tier.Name
            }))
        } else
            programTiers.value = []

        isTierLoading.value = false

    } catch {
        programTiers.value = []
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    } finally {
        isTierLoading.value = false
    }
}

function doSearch() : void 
{
    loadData(
        columns.find(c => c.selected)!.sortExpression,
        columns.find(c => c.selected)!.sortDirection,
        pageIndex.value,
        searchTerm.value
    )
}

async function onConfirmDelete() {
    if (!pendingDeleteId.value)
        return

    try {
        const result = await dataAccess.execute(`/partnerprogramenrollment/deleteenrollment`, { enrollmentId: pendingDeleteId.value })

        pendingDeleteId.value = null
        showDeleteMessageBox.value = false

        if (!result?.Success) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failDelete")
        }

        await loadData(
            columns.find(c => c.selected)!.sortExpression,
            columns.find(c => c.selected)!.sortDirection,
            pageIndex.value,
            searchTerm.value,
            selectedProgram.value
        )
    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage('failDelete')
    }
}

function onCancelDelete() {
    pendingDeleteId.value = null
    showDeleteMessageBox.value = false
}

async function saveEnrollment() {
    formErrors.value = []
    showErrorMessage.value = false
    serverErrorMessage.value = ''
    isSubmitting.value = true

    await validateAllFields()

    if (formErrors.value.length) {
        isSubmitting.value = false
        return
    }

    try {
        const endpoint = isEditMode.value
            ? `/partnerprogramenrollment/updateenrollment`
            : `/partnerprogramenrollment/createenrollment`
        const response = await dataAccess.execute(endpoint, { enrollment: enrollmentModel.value })

        if (!response.Success) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failSave")
        }

        onModalHide()

        await loadData(
            columns.find(c => c.selected)!.sortExpression,
            columns.find(c => c.selected)!.sortDirection,
            pageIndex.value,
            searchTerm.value,
            selectedProgram.value)

    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failSave")
    } finally {
        isSubmitting.value = false
    }
}

function removeError(message: string) {
    const index = formErrors.value.indexOf(message)

    if (index !== -1)
        formErrors.value.splice(index, 1)
}

function addError(message: string) {
    if (!formErrors.value.includes(message))
        formErrors.value.push(message)
}

function validateProgram() {
    const msg = languageHelper.getMessage("programRequired")

    removeError(msg)

    if (!enrollmentModel.value.ProgramId)
        addError(msg)
}

function validatePartnerAccount() {
    const msg = languageHelper.getMessage("accountRequired")
    const duplicateMsg = languageHelper.getMessage("duplicateEnrollment")

    removeError(msg)
    removeError(duplicateMsg)

    if (!enrollmentModel.value.PartnerAccountId && !isEditMode.value)
        addError(msg)
}

function validateTier() {
    const msg = languageHelper.getMessage("tierRequired")

    removeError(msg)

    if (!enrollmentModel.value.ProgramTierId)
        addError(msg)
}

function validateDates() {
    const msg = languageHelper.getMessage("enrollmentDateMustBeBeforeExpiration")
    const enrollDateMsg = languageHelper.getMessage("enrollmentDateRequired")
    const expireDateMsg = languageHelper.getMessage("expirationDateRequired")
    const enrollDate = new Date(enrollmentModel.value.EnrollmentDate)
    const expireDate = new Date(enrollmentModel.value.ExpirationDate)

    removeError(msg)
    removeError(enrollDateMsg)
    removeError(expireDateMsg)

    if (isNaN(enrollDate.getTime()))
        addError(enrollDateMsg)

    if (isNaN(expireDate.getTime()))
        addError(expireDateMsg)

    if (!isNaN(enrollDate.getTime()) && !isNaN(expireDate.getTime()) && enrollDate >= expireDate)
        addError(msg)
}

async function validateDuplicateEnrollment() {
    const msg = languageHelper.getMessage("duplicateEnrollment")
    const programId = enrollmentModel.value?.ProgramId
    const partnerAccountId = enrollmentModel.value?.PartnerAccountId

    removeError(msg)

    if (!programId || !partnerAccountId)
        return

    try {
        const response = await dataAccess.execute('partnerprogramenrollment/checkduplicateenrollment', {
            enrollment: enrollmentModel.value
        })

        if (!response) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            return
        }

        const duplicates = response.Count

        if (duplicates > 0)
            addError(msg)
    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    }
}

async function validateAllFields() {
    formErrors.value = []
    validateProgram()
    validatePartnerAccount()
    validateTier()
    validateDates()
    await validateDuplicateEnrollment()
}

function onModalHide() {
    selectedProgram.value = ''
    isModelVisible.value = false
    enrollmentModel.value = null
    formErrors.value = []
}

async function getNewEnrollment() {
    try {
        const data = await dataAccess.execute('partnerprogramenrollment/getnewenrollment')
        
        if (!data.enrollment) {
            showErrorMessage.value = true
            serverErrorMessage.value = languageHelper.getMessage("failFetchData")
            return
        }

        enrollmentModel.value = data.enrollment
    } catch {
        showErrorMessage.value = true
        serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    }
}

async function sortEnrollments(col: GridColumn) {
    const selectedCol = columns.find(column => column.name == col.name)

    if (selectedCol) {
        columns.forEach(c => c.selected = false)
        selectedCol.selected = true
        selectedCol.sortDirection = col.sortDirection

        await loadData(
            columns.find(c => c.selected)!.sortExpression,
            columns.find(c => c.selected)!.sortDirection,
            pageIndex.value,
            searchTerm.value,
            selectedProgram.value)
    }
}

function onPageChanged(newPage: number) {
    pageIndex.value = newPage
    loadData(columns.find(c => c.selected)!.sortExpression,
        columns.find(c => c.selected)!.sortDirection,
        pageIndex.value,
        searchTerm.value,
        selectedProgram.value)
}

async function handleProgramSelection(programId: string, existingAccountId: string = '', existingTierId: string = '') {
    formErrors.value = []

    if (!enrollmentModel.value) 
        return

    const programChanged = enrollmentModel.value.Program?.Id !== programId

    enrollmentModel.value.ProgramId = programId

    if (programChanged) {
        enrollmentModel.value.PartnerAccountId = ''
        enrollmentModel.value.ProgramTierId = ''
    }

    programTiers.value = []

    if (!programId) 
        return

    await Promise.all([
        loadProgramTiers(programId)
    ])

    if (isEditMode.value && existingAccountId) 
        enrollmentModel.value.PartnerAccountId = existingAccountId

    if (!programChanged && existingTierId) 
        enrollmentModel.value.ProgramTierId = programTiers.value.some(t => t.value === existingTierId)
            ? existingTierId
            : ''
}

async function handleProgramSearch(event: Event) {
    const select = event.target as HTMLSelectElement

    if (!select)
        return

    selectedProgram.value = select.value
    await loadData(
        columns.find(c => c.selected)!.sortExpression,
        columns.find(c => c.selected)!.sortDirection,
        pageIndex.value,
        searchTerm.value,
        select.value
    )
}
</script>

<template>
    <partner-program-setup-nav selected-tab="enrollments" />
    <div
        class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark rounded">
        <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
            {{ serverErrorMessage }}
        </iris-alert>
        <h2 class="text-xl font-semibold mb-4">{{ languageHelper.getMessage("enrollments") }}</h2>
        <div class="flex justify-between items-center mb-4">
            <div class="flex space-x-2">
                <input id="searchInput" v-model="searchTerm" @keyup.enter="doSearch" type="text"
                    :placeholder="languageHelper.getMessage('search') + '...'"
                    class="p-2 overflow-hidden text-text-color bg-bg-color rounded-lg border-border-color
                    focus:border-border-focus focus:ring-1 focus:ring-border-focus
                    dark:text-text-color-dark dark:bg-bg-color-dark dark:border-border-color-dark 
                    placeholder:italic placeholder:text-xs placeholder:text-text-color-400 dark:placeholder:text-text-color-400-dark" />
                <select 
                    v-model="selectedProgram" 
                    @change="handleProgramSearch" 
                    class="px-3 py-2 bg-bg-color border border-border-color rounded-lg focus:ring-2 focus:ring-primary focus:border-primary">
                    <option value="">{{ languageHelper.getMessage('allPrograms') }}</option>
                    <option 
                        v-for="program in programs" 
                        :key="program.value" 
                        :value="program.value">
                        {{ program.label }}
                    </option>
                </select>
            </div>

            <button @click="showModal" class="inline-flex btn-primary" type="button">
                {{ languageHelper.getMessage("newEnrollment") }}
            </button>
        </div>
        <div v-if="isLoading" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <iris-grid id="enrollments" v-if="enrollments && enrollments.length > 0" :value="enrollments" :cols="columns"
            :actions="gridActions" @on-sort="sortEnrollments" @actionClicked="onActionClicked" />
        <div v-else class="text-text-color-400 dark:text-text-color-400-dark">
            <span>{{ languageHelper.getMessage('noRecords') }}</span>
        </div>
        <div class="mt-5" v-if="enablePagination && totalRows > props.size">
            <iris-pagination v-if="enablePagination" :page-changed="onPageChanged" :total="totalRows" :page-size="props.size"
                :current-page="pageIndex" :use-icons-only="true" :next-and-previous-only="false" :size="'sm'"
                :first-btn-lbl='languageHelper.getMessage("first")' :last-btn-lbl='languageHelper.getMessage("last")'
                :next-btn-lbl='languageHelper.getMessage("next")' :prev-btn-lbl='languageHelper.getMessage("previous")'>
            </iris-pagination>
        </div>
    </div>
    <!-- Enrollment Modal -->
    <iris-modal id="enrollment-modal" :show="isModelVisible"
        :title='isEditMode ? languageHelper.getMessage("editEnrollment") : languageHelper.getMessage("addEnrollment")'
        @onHide="onModalHide">
        <template #content>
            <div v-if="isModalLoading" class="flex justify-center items-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
            <iris-form id="enrollmentForm" v-else-if="enrollmentModel">
                <iris-validation-summary formId="enrollmentForm" />
                <!-- Error Messages -->
                <div class="flex items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800" v-if="formErrors.length > 0">
                    <ul>
                        <li class="text-danger" v-for="error in formErrors">{{ error }}</li>
                    </ul>
                </div>
                <div class="grid gap-4 mb-4 grid-cols-2">
                    <div class="col-span-2">
                        <iris-picklist 
                            :enableSearch=true 
                            id="programId" 
                            :value="enrollmentModel" 
                            field="ProgramId"
                            :label="languageHelper.getMessage('program')" 
                            required 
                            :items="programs" 
                            @onChange="handleProgramSelection"
                            :mode="isEditMode ? FormControlModeEnum.html : FormControlModeEnum.edit"
                            :isNullable="false" />
                    </div>
                    <div v-if="enrollmentModel?.ProgramId && isAccountLoading" class="flex justify-center items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                    <div class="col-span-2" v-else-if="enrollmentModel?.ProgramId">
                        <IrisField 
                            id="PartnerAccountId"
                            :value="enrollmentModel"
                            field="PartnerAccountId" 
                            :label="languageHelper.getMessage('partnerAccount')" 
                            required
                            :mode="isEditMode ? FormControlModeEnum.html : FormControlModeEnum.edit"
                            @onChange="validatePartnerAccount"/>
                    </div>
                    <div class="col-span-2" v-if="programTiers.length" >
                        <iris-picklist 
                            id="programTierId" 
                            :value="enrollmentModel" 
                            field="ProgramTierId" 
                            :label="languageHelper.getMessage('tier')"
                            required 
                            :items="programTiers" 
                            @onChange="validateTier"
                            :isNullable="false"/>
                    </div>
                    <div v-else-if="enrollmentModel?.ProgramId && !isTierLoading && !programTiers.length" class="flex col-span-2 items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800">
                        <ul>
                            <li class="text-danger">{{ languageHelper.getMessage('noTiers') }}</li>
                        </ul>
                    </div>
                    <div class="col-span-2">
                        <iris-date-time 
                            id="enrollmentDate" 
                            :value="enrollmentModel" 
                            field="EnrollmentDate"
                            :label="languageHelper.getMessage('enrollmentDate')" 
                            required 
                            @onChange="validateDates"/>
                    </div>
                    <div class="col-span-2">
                        <iris-date-time 
                            id="expirationDate" 
                            :value="enrollmentModel" 
                            field="ExpirationDate"
                            :label="languageHelper.getMessage('expirationDate')" 
                            required 
                            @onChange="validateDates"/>
                    </div>
                </div>
            </iris-form>
        </template>
        <template #footer>
            <div class="flex justify-end space-x-3">
                <button type="button" class="btn-light" @click="onModalHide">
                    {{ languageHelper.getMessage("cancel") }}
                </button>
                <button type="submit" form="enrollmentForm" class="btn-primary"
                    :disabled="isSaveDisabled || isSubmitting || formErrors.length > 0" @click="saveEnrollment">
                    <span v-if="isSubmitting">
                        {{ languageHelper.getMessage("saving") }}
                    </span>
                    <span v-else>
                        {{ isEditMode ? languageHelper.getMessage("save") : languageHelper.getMessage("addEnrollment")
                        }}
                    </span>
                </button>
            </div>
        </template>
    </iris-modal>
    <iris-message-box type="Confirm" :message="languageHelper.getMessage('deleteEnrollmentMessage')"
        :show="showDeleteMessageBox" :title="languageHelper.getMessage('deleteEnrollment')"
        :primaryButtonLabel="languageHelper.getMessage('delete')"
        :cancelButtonLabel="languageHelper.getMessage('cancel')" :icon="'trash-can'" :size="'small'"
        @onPrimaryClick="onConfirmDelete" @onCancelClick="onCancelDelete" />
</template>
