<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { RequestMethod } from '@/shared/services/enums'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { DropDownItem } from '@/shared/services/dropdown-item'
import { useIrisPage } from '@/shared/composables/iris-page'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'
import LanguageHelper from '@/shared/services/language-helper'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'
import Iris<PERSON><PERSON><PERSON>ield from '@/shared/components/form-controls/IrisAssetField.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'

import FieldFilters from '@/modules/partner-program/components/FieldFilters.vue'



/* --- Interfaces --- */
interface Field {
  Id: string    
  Name: string   
  Label: string 
  Type: string 
  Entries?: { Label: string, Value: string }[]
}

interface ApiField {
  Id: string
  Name: string
  Label: string
  FieldType: string
  PicklistEntries?: Array<{ Label: string, Value: string }>
}

interface CriteriaTierValue {
  TierId: string | null
  Value: string
}

interface CriteriaTierRow {
  CriteriaId: string
  CriteriaName: string
  CriteriaType: string
  Details: CriterionDetails
  Tier1?: CriteriaTierValue
  Tier2?: CriteriaTierValue
  Tier3?: CriteriaTierValue
  Tier4?: CriteriaTierValue
  Tier5?: CriteriaTierValue
  Tier6?: CriteriaTierValue
  [key: string]: any
}

interface CriterionDetails {
  FilterDateField: string
  RollupField: string
  TrainingCourseId: string
  UnitOfMeasure: string
  Filters: {
    FilterLogic: string
    Filters: Array<{
      FieldId: string
      Operator: string
      Value: string
      SequenceNo: string
    }>
  }
  Tiers: Array<CriteriaTierValue>
}

interface BenefitValue {
  Value: any
  CurrencyCode?: string | null
  Type: string
}

interface BenefitTierRow {
  BenefitId: string
  BenefitName: string
  BenefitType: string
  Tier1?: BenefitValue
  Tier2?: BenefitValue
  Tier3?: BenefitValue
  Tier4?: BenefitValue
  Tier5?: BenefitValue
  Tier6?: BenefitValue
  [key: string]: any 
}

/* --- Constants and Refs --- */
const route = useRoute()
const languageHelper = new LanguageHelper()
const filterRef = ref()
const base = useIrisPage()
const dataAccess = new DataAccess()
const mode = ref<FormControlModeEnum>(FormControlModeEnum.html)
const showErrorMessage = ref(false)
const serverErrorMessage = ref<string>('')
const isDropdownVisible = ref(false)
const isLoading = ref(false)
const isSaving = ref(false)
const formErrors = ref<string[]>([])

const programData = ref<any>(null)
const tierData: any = ref(null)
const benefitsData: any = ref(null)
const criteriaData: any = ref(null)

const isTierModalVisible = ref(false)
const editingTier = ref<any>(null)
const isNewTier = ref(true)
const tierIdToDelete = ref<string | null>(null)
const isTierDeleteMessageBoxVisible = ref(false)
const benefitTierRows = ref<BenefitTierRow[]>([])
const allProgramBenefitsOptions: any = ref([])

//Criteria related
const criteriaTierRows = ref<CriteriaTierRow[]>([])
const editingCriterion = ref<CriteriaTierRow | null>(null)
const isCriterionModalVisible = ref(false)
const certificationCourses = ref<DropDownItem[]>([])
const isCriteriaEditing = ref(false)
const entityFields = ref<ApiField[]>([])
const dateFields = ref<DropDownItem[]>([])
const amountFields = ref<DropDownItem[]>([])

/* --- Lifecycle Hooks --- */
onMounted(async () => {
  base.setPageTitle(languageHelper.getMessage('partnerProgram'))

  try{
    await loadData()
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failInitialize") 
  }
})

/* --- Computed --- */
const programId = computed(() => {
  const idParam = route.params.id
  return Array.isArray(idParam) ? idParam[0] : idParam
})

const isDataValid = computed<boolean>(() => {
  return typeof programId.value === "string" && programId.value.trim() !== ""
})

const dropdownValues = computed<string[]>(() => [
  languageHelper.getMessage('newTier'),
  languageHelper.getMessage('clone'),
  languageHelper.getMessage('delete')
])

const benefitYesNoItems = computed((): DropDownItem[] => [
  {
    label: languageHelper.getMessage('yes'),
    value: "1",
    color: '',
    selected: false,
    helperText: '',
    removable: false
  },
  {
    label: languageHelper.getMessage('no'),
    value: "0",
    color: '',
    selected: false,
    helperText: '',
    removable: false
  }
])

const criteriaTypeOptions = computed((): DropDownItem[] => [
  { label: languageHelper.getMessage('certifications'), value: 'Certifications', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('numberofDealRegistrations'), value: 'Number of Deal Registrations', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('numberofOpportunities'), value: 'Number of Opportunities', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('opportunityRevenue'), value: 'Opportunity Revenue', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('custom'), value: 'Custom', color: '', selected: false, helperText: '', removable: false }
])

const unitOfMeasureOptions = computed((): DropDownItem[] => [
  { label: languageHelper.getMessage('revenue'), value: 'Revenue', color: '', selected: false, helperText: '', removable: false },
  { label: languageHelper.getMessage('quantity'), value: 'Quantity', color: '', selected: false, helperText: '', removable: false }
])

const criterionType = computed(() => editingCriterion.value?.CriteriaType)
const isCertifications = computed(() => criterionType.value === 'Certifications')
const isDeal = computed(() => criterionType.value === 'Number of Deal Registrations')
const isOpportunity = computed(() => criterionType.value === 'Number of Opportunities')
const isRevenue = computed(() => criterionType.value === 'Opportunity Revenue')
const isCustom = computed(() => criterionType.value === 'Custom')
const fields = computed<Field[]>(() => mapEntityFields(entityFields.value))

const sequenceOptions = computed<DropDownItem[]>(() => {
  const max = Math.min((tierData.value?.length || 0) + (isNewTier.value ? 1 : 0), 6)

  const currentSeq = editingTier.value?.sequence
  const options: DropDownItem[] = []

  for (let i = 1; i <= max; i++) {
    options.push(new DropDownItem(i.toString(), i.toString(), '', i === currentSeq))
  }

  return options
})
/* --- Watch --- */

/* --- Functions --- */
async function loadData() {
  isLoading.value = true
  showErrorMessage.value = false

  if (!isDataValid.value) {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("missingProgramId") 
    isLoading.value = false
    return
  }

  try {
    const endpoint = `/partnerprogram/getprogrambyid`
    const payload = {
      programId: programId.value
    }

    const response = await dataAccess.execute(
      endpoint,
      payload
    )

    if (!response.Success || !response.Data) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
      isLoading.value = false
      return
    }

    tierData.value = response.Data.tiers || []
    criteriaData.value = response.Data.criteria || []
    benefitsData.value = response.Data.benefits || []
    programData.value = response.Data.program || {}

    if (!tierData.value || !criteriaData.value || !benefitsData.value || !programData.value) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
      isLoading.value = false
      return
    }

    buildCriteriaTierRows()
    buildBenefitTierRows()

    isLoading.value = false
    
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
    benefitsData.value = []
    tierData.value = []
    criteriaData.value = []
    programData.value = {}
  } finally {
    isLoading.value = false
  }
}

function buildCriteriaTierRows() {
  const maxTiers = 6
  const rows: CriteriaTierRow[] = []

  for (const criterion of criteriaData.value) {
    let parsedDetails: CriterionDetails = {
      FilterDateField: '',
      RollupField: '',
      TrainingCourseId: '',
      UnitOfMeasure: '',
      Filters: {
        FilterLogic: 'AND',
        Filters: []
      },
      Tiers: []
    }

    // Parse JSON from Values
    if (criterion.Values) {
      try {
        parsedDetails = JSON.parse(criterion.Values)
      } catch (error) {
        console.warn(`Failed to parse criterion.Values for ID ${criterion.Id}:`, error)
      }
    }

    const row: CriteriaTierRow = {
      CriteriaId: criterion.Id,
      CriteriaName: criterion.Name,
      CriteriaType: criterion.Type,
      Details: parsedDetails
    }

    // Map tier values from parsedDetails.tiers to tier1..tier6
    for (let i = 0; i < maxTiers; i++) {
      const tier = tierData.value[i]
      if (!tier) 
        continue

      const matchedTier = parsedDetails.Tiers?.find((t: any) => t.TierId?.trim() === tier.Id?.trim())

      row[`Tier${i + 1}` as keyof CriteriaTierRow] = {
        TierId: tier.Id,
        Value: matchedTier?.Value ?? ''
      }
    }

    rows.push(row)
  }
  criteriaTierRows.value = rows
}


function buildBenefitTierRows() {
  const grouped = new Map<string, any[]>()
  for (const entry of benefitsData.value) {
    const BenefitId = entry.ProgramBenefit.Id

    if (!grouped.has(BenefitId)) 
      grouped.set(BenefitId, [])

    grouped.get(BenefitId)?.push(entry)
  }

  const rows: BenefitTierRow[] = []
  const maxTiers = 6

  for (const [BenefitId, Entries] of grouped.entries()) {
    const sample = Entries[0]
    const row: BenefitTierRow = {
      BenefitId,
      BenefitName: sample.ProgramBenefit.Name,
      BenefitType: sample.ProgramBenefit.BenefitType
    }

    for (let i = 0; i < maxTiers; i++) {
      const tier = tierData.value[i]

      if (!tier) 
        continue

      const entry = Entries.find(e => e.ProgramTier.Id === tier.Id)

      row[`Tier${i + 1}` as keyof BenefitTierRow] = {
        Value: entry?.Value ?? "",
        Type: sample.ProgramBenefit.BenefitType,
        CurrencyCode: entry?.CurrencyIsoCode ?? null
      }
    }

    rows.push(row)
  }

  benefitTierRows.value = rows
}

async function toggleToEdit() {
  if (mode.value !== FormControlModeEnum.edit) {
    await loadEditData(programId.value)
    mode.value = FormControlModeEnum.edit
  } else {
    await loadEditData(programId.value)
    mode.value = FormControlModeEnum.html
  }
}

async function selectOption(option: string) {
  if (option === 'New Tier') {
    await openNewTierModal()
  } else if (option === 'Delete') {
    // handleDeleteProgram()
  }
  else if (option === 'Clone') {
    // showCloneProgram()
  }

  isDropdownVisible.value = false
}

async function loadEditData(programId: string) {
  try {
    await loadBenefits()
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    mode.value = FormControlModeEnum.html
  }
}

async function handleSubmit() {
  if (isSaving.value) 
    return 

  try {

    isSaving.value = true
    isLoading.value = true
    showErrorMessage.value = false

    const programIdValue = programData.value?.Id

    if (!programIdValue) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("missingProgramId") 
      return
    }

    // --- Build Criteria Payload ---
    const criteriaPayload = criteriaTierRows.value.map(row => {
      const tiers = Object.entries(row)
        .filter(([key]) => key.startsWith('Tier'))
        .map(([_, val]) => ({
          TierId: (val as CriteriaTierValue)?.TierId,
          Value: (val as CriteriaTierValue)?.Value
        }))
        .filter(tv => typeof tv.TierId === 'string' && tv.TierId && tv.Value !== undefined) // Only allow non-null string tierId
        .map(tv => ({
          TierId: tv.TierId as string,
          Value: tv.Value
        }))

      const Details = row.Details || {}

      Details.Tiers = tiers

      return {
        Name: row.CriteriaName?.trim() || '',
        Type: row.CriteriaType || '',
        ProgramId: programIdValue,
        Values: JSON.stringify(Details)
      }
    })

    // --- Build Benefit Payload ---
    const benefitsPayload: any[] = []

    for (const row of benefitTierRows.value) {
      for (let i = 1; i <= 6; i++) {
        const tier = row[`Tier${i}` as keyof BenefitTierRow] as BenefitValue | undefined
        const tierId = tierData.value[i - 1]?.Id

        if (!tier || !tierId) 
          continue

        benefitsPayload.push({
          ProgramBenefitId: row.BenefitId,
          ProgramId: programIdValue,
          ProgramTierId: tierId,
          Value: tier.Value,
          CurrencyIsoCode: tier.CurrencyCode || null
        })
      }
    }

    // --- Final Unified Payload ---
    const payload = {
      programId: programIdValue,
      criteria: criteriaPayload,
      benefits: benefitsPayload
    }
    const response = await dataAccess.execute(
      '/partnerprogram/saveprogramdetails',
      payload
    )
    
    if (!response.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failSave")
    }
    mode.value = FormControlModeEnum.html
  } catch  {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failSave")
  } finally {
    isSaving.value = false
    isLoading.value = false
  }
}


async function loadBenefits() {
  try {
    isLoading.value = true
    showErrorMessage.value = false
    const response = await dataAccess.execute('partnerprogrambenefit/getallbenefits')

    if (!response) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      isLoading.value = false
      return
    }

    allProgramBenefitsOptions.value = response.benefits || []
    isLoading.value = false

  } catch {
    allProgramBenefitsOptions.value = []
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    mode.value = FormControlModeEnum.html
  } finally {
    isLoading.value = false
  }
}

function addNewBenefitRow() {
  const newRow: BenefitTierRow = {
    BenefitId: '',
    BenefitName: '',
    BenefitType: ''
  }

  benefitTierRows.value.push(newRow)
}

function removeCriteriaRow(index: number) {
  if (index >= 0 && index < criteriaTierRows.value.length) {
    criteriaTierRows.value.splice(index, 1)
  }
}

function removeBenefitRow(index: number) {
  if (index >= 0 && index < benefitTierRows.value.length) {
    benefitTierRows.value.splice(index, 1)
  }
}

function availableBenefitOptions(currentId: string) {
  const selectedIds = benefitTierRows.value.map(row => row.BenefitId)
  return allProgramBenefitsOptions.value
    .filter((opt: any) => opt.Id === currentId || !selectedIds.includes(opt.Id))
    .map((opt: any) => ({
      label: opt.Name,
      value: opt.Id
    }))
}

function handleBenefitSelectionChange(row: BenefitTierRow, newId: string) {
  const selected = allProgramBenefitsOptions.value.find((opt: any) => opt.Id === newId)

  if (!selected) 
    return

  row.BenefitId = selected.Id
  row.BenefitName = selected.Name
  row.BenefitType = selected.BenefitType

  for (let i = 0; i < 6; i++) {
    const tier = tierData.value[i]

    if (!tier) 
      continue

    const key = `Tier${i + 1}` as keyof BenefitTierRow
    row[key] = {
      Value: '',
      Type: selected.BenefitType,
      CurrencyCode: selected.CurrencyIsoCode || null
    }
  }
}

async function openNewTierModal() {
  try {
    const payload = { programId: programId.value }

    const data = await dataAccess.execute('partnerprogramtier/getnewtier',
      payload
    )
    if (!data || !data.tier) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      return
    }

    editingTier.value = data.tier
    isNewTier.value = true
    isTierModalVisible.value = true 

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
  }
}

function validateTier(): boolean {
  formErrors.value = []

  const name = editingTier?.value.Name?.trim() ?? ''

  if (!name) {
    formErrors.value.push(languageHelper.getMessage('tierNameRequired'))
  } else {
    // Check for duplicate name (case-insensitive)
    const isDuplicate = tierData.value.some((t: any) =>
      t.Name?.toLowerCase() === name.toLowerCase() &&
      t.Id !== editingTier.value?.Id // exclude self in edit mode
    )

    if (isDuplicate) {
      formErrors.value.push(languageHelper.getMessage('duplicateTierName'))
    }
  }

  return formErrors.value.length === 0
}



function openEditTierModal(tier: any) {
  editingTier.value = { ...tier }
  isNewTier.value = false
  isTierModalVisible.value = true
}

async function saveTier() {
  if (!editingTier.value) 
    return

  if (!validateTier())
    return

  const endpoint = isNewTier.value
    ? '/partnerprogramtier/createtier'
    : '/partnerprogramtier/updatetier'

  const payload = {
    tier: editingTier.value
  }

  const response = await dataAccess.execute(endpoint, payload)

  if (!response.tier) {
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    showErrorMessage.value = true
    return
  }

  await loadData()
  isTierModalVisible.value = false
  editingTier.value = null
}

function onTierModalHide() {
  isTierModalVisible.value = false
  editingTier.value = null
  isNewTier.value = true
}

function openDeleteTier(tierId: string) {
  tierIdToDelete.value = tierId
  isTierDeleteMessageBoxVisible.value = true
}

function cancelDeleteTier() {
  tierIdToDelete.value = null
  isTierDeleteMessageBoxVisible.value = false
}

async function confirmDeleteTier() {
  if (!tierIdToDelete.value) 
    return

  const payload = { 
    tierId: tierIdToDelete.value
  }

  const response = await dataAccess.execute('/partnerprogramtier/deletetier', payload)

  if (!response?.Success) {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failDelete")
  }

  await loadData()
  isTierModalVisible.value = false
  tierIdToDelete.value = null
  editingTier.value = null
  isTierDeleteMessageBoxVisible.value = false
}

function openNewCriterionModal() {
  const newId = crypto.randomUUID?.() || `crit-${Date.now()}`
  mode.value = FormControlModeEnum.edit

  editingCriterion.value = {
    CriteriaId: newId,
    CriteriaName: '',
    CriteriaType: '',
    Tier1: undefined,
    Tier2: undefined,
    Tier3: undefined,
    Tier4: undefined,
    Tier5: undefined,
    Tier6: undefined,
    Details: {
      FilterDateField: '',
      RollupField: '',
      TrainingCourseId: '',
      UnitOfMeasure: '',
      Filters: {
        FilterLogic: 'AND',
        Filters: []
      },
      Tiers: []
    }
  }

  formErrors.value = []

  isCriterionModalVisible.value = true
}

function saveCriterionConfig() {
  const criterion = editingCriterion.value

  formErrors.value = []

  if (!criterion) 
    return

  if (!criterion.CriteriaName || criterion.CriteriaName.trim() === '')
    formErrors.value.push(languageHelper.getMessage("criterionNameRequiredError"))

  if (!criterion.CriteriaType || criterion.CriteriaType.trim() === '') 
    formErrors.value.push(languageHelper.getMessage("criterionTypeRequiredError"))

  if (!validateCriterionDetails()) 
    return

  validateCriteriaName()

  if (formErrors.value.length > 0) 
    return

  if (!isCriteriaEditing.value) {
    for (let i = 0; i < 6; i++) {
      const tier = tierData.value[i]

      if (!tier) 
        continue

      const key = `Tier${i + 1}` as keyof CriteriaTierRow
      criterion[key] = {
        TierId: tier.Id,
        Value: ''
      }
    }
  }

  const filtersJson = filterRef.value.getJson()
  criterion.Details.Filters = filtersJson

  const finalValue = JSON.stringify(criterion.Details)

  if (isCriteriaEditing.value) {
    const index = criteriaTierRows.value.findIndex((c: any) => c.CriteriaId === criterion.CriteriaId)
    if (index !== -1) {
      criteriaTierRows.value[index] = {
        ...criterion,
        Value: finalValue
      }
    }
  } else {
    criteriaTierRows.value.push({
      ...criterion,
      Value: finalValue
    })
  }


  onCriterionModalHide()
}

function onCriterionModalHide() {
  isCriterionModalVisible.value = false
  isCriteriaEditing.value = false
  editingCriterion.value = null
  formErrors.value = []
}

function validateCriteriaName() {
  if (!editingCriterion.value?.CriteriaName) return

  const trimmedName = editingCriterion.value.CriteriaName.trim().toLowerCase()

  const isDuplicate = criteriaTierRows.value.some(c =>
    c.CriteriaId !== editingCriterion.value?.CriteriaId && // exclude self (for edit case)
    c.CriteriaName?.trim().toLowerCase() === trimmedName
  )

  const errorMsg = languageHelper.getMessage("criteriaDuplicateNameError")

  // Add/remove error message
  const existingIndex = formErrors.value.findIndex(e => e === errorMsg)

  if (isDuplicate && existingIndex === -1) {
    formErrors.value.push(errorMsg)
  } else if (!isDuplicate && existingIndex > -1) {
    formErrors.value.splice(existingIndex, 1)
  }
}

function validateCriterionDetails(): boolean {
  const c = editingCriterion.value
  const errors: string[] = []

  if (!c) 
    return false

  // Trimmed name check (already done elsewhere but safe to include)
  if (!c.CriteriaName?.trim())
    errors.push(languageHelper.getMessage("criterionNameRequiredError"))

  if (!c.CriteriaType) 
    errors.push(languageHelper.getMessage("criterionTypeRequiredError"))

  if (isCertifications.value)
    if (!c.Details?.TrainingCourseId) 
      errors.push(languageHelper.getMessage("trainingCourseRequiredError"))

  if (isRevenue.value)
    if (!c.Details?.RollupField?.trim()) 
      errors.push(languageHelper.getMessage("rollupFieldRequiredError"))

  if (isRevenue.value || isOpportunity.value || isDeal.value)
    if (!c.Details?.FilterDateField?.trim())
      errors.push(languageHelper.getMessage("attainmentDateFieldRequired"))
    
  if (isCustom.value) 
    if (!c.Details?.UnitOfMeasure?.trim()) {
      errors.push(languageHelper.getMessage("unitofMeasureRequired"))
    }

  formErrors.value = errors
  return errors.length === 0
}

async function editCriterion(existing: any) {
  isCriteriaEditing.value = true

  editingCriterion.value = JSON.parse(JSON.stringify(existing))

  if (editingCriterion?.value?.CriteriaType === 'Certifications') 
    await loadCertificationCourses()

  if (editingCriterion.value)
    editingCriterion.value.Details = {
      FilterDateField: existing.Details?.FilterDateField || '',
      RollupField: existing.Details?.RollupField || '',
      TrainingCourseId: existing.Details?.TrainingCourseId || '',
      UnitOfMeasure: existing.Details?.UnitOfMeasure || '',
      Filters: {
        FilterLogic: existing.Details?.Filters?.FilterLogic || 'AND',
        Filters: existing.Details?.Filters?.Filters || []
      },
      Tiers: existing.Details?.Tiers || []
    }
  

  isCriterionModalVisible.value = true
}

function removeFormError(fieldKey: string) {
  if (fieldKey === 'criterionName') {
    const requiredError = languageHelper.getMessage('criterionNameRequiredError')
    formErrors.value = formErrors.value.filter(err => err !== requiredError)
  }

  if (fieldKey === 'criterionType') {
    const typeError = languageHelper.getMessage('criterionTypeRequiredError')
    formErrors.value = formErrors.value.filter(err => err !== typeError)
  }

  if (fieldKey === 'TrainingCourseId') {
    const courseError = languageHelper.getMessage('trainingCourseRequiredError')
    formErrors.value = formErrors.value.filter(err => err !== courseError)
  }
}

async function onCriteriaTypeChanged(type: string) {

  if (!editingCriterion.value) 
    return

  editingCriterion.value.CriteriaType = type
  removeFormError('criterionType')

  if (isCertifications.value) {
    loadCertificationCourses()
  } else if (isDeal.value) {
    loadLeadFields()
  } else if (isOpportunity.value) {
    loadOpportunityFields()
  } else if (isRevenue) {
    loadOpportunityFields()
  }

  editingCriterion.value.Details = {
    FilterDateField: '',
    RollupField: '',
    TrainingCourseId: '',
    UnitOfMeasure: '',
    Filters: {
      Filters: [],
      FilterLogic: ''
    },
    Tiers: []
  }
}

async function loadCertificationCourses() {
  try {
    const response = await dataAccess.execute('/partnerprogramtiercriteria/getcertificationcourses')

    if (response && response?.courses && response?.entityFields) {
      certificationCourses.value = response.courses.map((c: any) => new DropDownItem(c.Name, c.Id))
      entityFields.value = response.entityFields || []
    } else {
      certificationCourses.value = []
      entityFields.value = []
    }
    console.log("entity",entityFields)
  } catch {
    certificationCourses.value = []
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    return
  }
}

async function loadLeadFields() {
  try {
    const response = await dataAccess.execute('/partnerprogramtiercriteria/getleadfields')

    if (response && response?.dateFields && response?.entityFields) {
      dateFields.value = response.dateFields.map((c: any) => new DropDownItem(c.Name, c.Id))
      entityFields.value = response.entityFields || []
    } else {
      dateFields.value = []
      entityFields.value = []
    }
  } catch {
    dateFields.value = []
    entityFields.value = []
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    return
  }
}

async function loadOpportunityFields() {
  try {
    const response = await dataAccess.execute('/partnerprogramtiercriteria/getopportunityfields')

    if (response && response?.dateFields && response?.entityFields) {
      dateFields.value = response.dateFields.map((c: any) => new DropDownItem(c.Name, c.Id))
      amountFields.value = response.amountFields.map((c: any) => new DropDownItem(c.Name, c.Id))
      entityFields.value = response.entityFields || []
    } else {
      dateFields.value = []
      entityFields.value = []
      amountFields.value = []
    }
  } catch {
    dateFields.value = []
    entityFields.value = []
    amountFields.value = []
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    return
  }
}

function mapEntityFields(apiFields: ApiField[]): Field[] {
  return apiFields
    .map(f => ({
      Id:    f.Id,
      Name:  f.Name,
      Label: f.Label,
      Type:  normalizeType(f.FieldType),
      Entries: f.PicklistEntries?.map(entry => ({
        Label: entry.Label,
        Value: entry.Value
      })) || []
    }))
}

function normalizeType(apiType: string): string {
  switch (apiType) {
    case 'Date': 
    case 'DateTime':
      return 'Date'
    case 'Picklist':
    case 'PicklistMultiSelect':
      return apiType
    case 'Number':
    case 'Currency':
      return 'Number'
    case 'CheckBox':
      return 'CheckBox'
    default:
      return 'Text'
  }
}
</script>

<template>
  <partner-program-setup-nav selected-tab="programs" />
  <div class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm">
    <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
      {{ serverErrorMessage }}
    </iris-alert>
    <div class="mt-4">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">{{ languageHelper.getMessage("program") }} : {{ programData?.Name }}</h1>
        <div class="flex justify-end space-x-2 items-center">
          <div v-if="mode === FormControlModeEnum.edit" class="flex space-x-2">
            <button class="btn-light" @click="toggleToEdit">{{ languageHelper.getMessage("cancel") }}</button>
            <button class="btn-primary flex items-center" @click="handleSubmit" :disabled="isSaving">
              <span v-if="isSaving">
                {{ languageHelper.getMessage("saving") }}
              </span>
              <span v-else>
                {{ languageHelper.getMessage("save") }}
              </span>
            </button>
          </div>
          <div v-else class="flex space-x-2 items-center">
            <div class="relative">
              <button class="btn-light" @click="isDropdownVisible = !isDropdownVisible">...</button>
              <div v-if="isDropdownVisible" class="absolute right-0 mt-2 w-48 text-text-color-100 bg-bg-color-200 border border-border-color rounded-lg shadow-lg z-10 dark:text-text-color-100-dark dark:bg-bg-color-200-dark dark:border-border-color-dark focus:outline-none focus:ring-4 focus:ring-border-color overflow-hidden">
                <ul class="py-1">
                  <li v-for="(opt, i) in dropdownValues" :key="i" class="block px-5 py-2.5 text-sm font-medium cursor-pointer hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark" @click="selectOption(opt)">
                    {{ opt }}
                  </li>
                </ul>
              </div>
            </div>
            <button class="btn-primary" @click="toggleToEdit">{{ languageHelper.getMessage("edit") }}</button>
          </div>
        </div>
      </div>
    </div>
    <br />
    <div class="border border-border-color rounded-lg overflow-x-auto">
      <table class="table-fixed w-full border-collapse">
        <thead>
          <tr v-if="tierData?.length" class="border-b">
            <th class="w-16 px-3 py-2 text-left"></th>
            <th class="w-1/4 px-3 py-6 text-left"></th>
            <th v-if="tierData.length >= 1" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[0]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[0]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[0])" 
                />
                </div>
                <span>{{ tierData[0]?.Name }}</span>
              </div>
            </th>
            <th v-if="tierData.length >= 2" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[1]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[1]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[1])" 
                />
                </div>
                <span>{{ tierData[1]?.Name }}</span>
                </div>
              </th>
            <th v-if="tierData.length >= 3" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[2]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[2]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[2])" 
                />
                </div>
                <span>{{ tierData[2]?.Name }}</span>
                </div>
              </th>
            <th v-if="tierData.length >= 4" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[3]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[3]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[3])" 
                />
                </div>
                <span>{{ tierData[3]?.Name }}</span>
                </div>
              </th>
            <th v-if="tierData.length >= 5" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[4]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[4]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[4])" 
                />
                </div>
                <span>{{ tierData[4]?.Name }}</span>
                </div>
              </th>
            <th v-if="tierData.length >= 6" class="py-6 px-2 text-center">
              <div class="flex flex-col items-center space-y-2">
                <div class="flex items-center space-x-2">
                  <img 
                    v-if="tierData[5]?.Badge" 
                    :src="Common.getBaseUrl() + '/Contents/Assets/' + tierData[5]?.Badge" 
                    class="h-8 w-8 object-contain" />
                     <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"
                  @click="openEditTierModal(tierData[5])" 
                />
                </div>
                <span>{{ tierData[5]?.Name }}</span>
                </div>
              </th>
          </tr>
          <tr v-else>
            <td colspan="2"  class="py-6 text-center text-text-color-400" >
              {{ languageHelper.getMessage("noTiersDefined") }}
            </td>
          </tr>
        </thead>
        <tbody>
          <tr v-if="tierData?.length" class="border-b">
            <th class="px-3 py-6 text-left whitespace-nowrap" :colspan="(tierData?.length || 0) + 2">
              <h2 class="">{{ languageHelper.getMessage("criteria") }}</h2>
            </th>
          </tr>
          <!-- Criteria Section -->
          <tr v-if="criteriaTierRows.length === 0">
            <td :colspan="(tierData?.length || 0) + 2" class="text-center text-text-color-400 py-6">
              {{ languageHelper.getMessage("noCriteriaDefined") }}
            </td>
          </tr>
          <tr v-for="(row, i) in criteriaTierRows" :key="row.CriteriaId" class="border-b">
            <td class="px-3 py-6 text-left">
              <button v-if="mode === FormControlModeEnum.edit" @click="removeCriteriaRow(i)">
                <iris-icon name="xmark" width="1rem" height="1rem" />
              </button>
            </td>
            <td class="px-3 py-6 text-left">
              <template v-if="mode === FormControlModeEnum.edit">
                <div class="flex items-center gap-2">
                  <iris-textbox
                    v-if="mode === FormControlModeEnum.edit"
                    :id="`criteria-${row.CriteriaId}`"
                    :value="row.CriteriaName"
                    :mode="mode"
                    placeholder="Enter criterion name"
                    required
                    @onChange="val => row.CriteriaName = val"
                  />
                  <button @click="editCriterion(row)" class="ml-2 text-gray-500 hover:text-primary">
                    <iris-icon name="cog" width="1rem" height="1rem" class="cursor-pointer"/>
                  </button>
                </div>
              </template>
              <template v-else>{{ row.CriteriaName }}</template>
            </td>
            <td v-if="tierData.length >= 1" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier1?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier1?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier1!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier1?.Value !== '' && row.Tier1?.Value !== null && row.Tier1?.Value !== undefined">
                  ≥ {{ row.Tier1?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
            <td v-if="tierData.length >= 2" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier2?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier2?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier2!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier2?.Value !== '' && row.Tier2?.Value !== null && row.Tier2?.Value !== undefined">
                  ≥ {{ row.Tier2?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
            <td v-if="tierData.length >= 3" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier3?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier3?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier3!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier3?.Value !== '' && row.Tier3?.Value !== null && row.Tier3?.Value !== undefined">
                  ≥ {{ row.Tier3?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
            <td v-if="tierData.length >= 4" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier4?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier4?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier4!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier4?.Value !== '' && row.Tier4?.Value !== null && row.Tier4?.Value !== undefined">
                  ≥ {{ row.Tier4?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
            <td v-if="tierData.length >= 5" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier5?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier5?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier5!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier5?.Value !== '' && row.Tier5?.Value !== null && row.Tier5?.Value !== undefined">
                  ≥ {{ row.Tier5?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
            <td v-if="tierData.length >= 6" class="px-3 py-6 text-center">
              <template v-if="mode === FormControlModeEnum.edit">
                <iris-textbox 
                :value="row.Tier6?.Value" 
                :id="`criteriaTierValue-${row.CriteriaId}-${row.Tier6?.TierId}`" 
                :mode="mode" 
                @onChange="val => row.Tier6!.Value = val"
                />
              </template>
              <template v-else>
                <span v-if="row.Tier6?.Value !== '' && row.Tier6?.Value !== null && row.Tier6?.Value !== undefined">
                  ≥ {{ row.Tier6?.Value }}
                </span>
                <span v-else></span>
              </template>
            </td>
          </tr>
          <tr v-if="mode === FormControlModeEnum.edit" class="border-b">
            <td class="px-3 py-4 text-left">
              <iris-icon name="plus" width="1rem" height="1rem" class="text-gray-400" />
            </td>
            <td class="px-3 py-6 text-left whitespace-nowrap" :colspan="(tierData?.length || 0) + 1">
              <button class="btn-light" @click="openNewCriterionModal">
                {{ languageHelper.getMessage("addCriterion") }}
              </button>
            </td>
          </tr>
          <!-- Benefit Section -->
          <tr class="border-b">
            <th :colspan="(tierData?.length || 0) + 2" class="px-3 py-6 text-left whitespace-nowrap">
              <h2 class="">{{ languageHelper.getMessage("benefits") }}</h2>
            </th>
          </tr>
          <tr v-if="benefitTierRows.length === 0">
            <td :colspan="(tierData?.length || 0) + 2" class="text-center text-text-color-400 py-6">
              {{ languageHelper.getMessage("noBenefitDefined") }}
            </td>
          </tr>
          <tr v-for="(row, i) in benefitTierRows" :key="row.BenefitId" class="border-t">
            <td class="px-3 py-6 text-left">
              <button v-if="mode === FormControlModeEnum.edit" @click="removeBenefitRow(i)">
                <iris-icon name="xmark" width="1rem" height="1rem" />
              </button>
            </td>
            <td class="px-3 py-6 text-left">
              <iris-picklist
                v-if="mode === FormControlModeEnum.edit"
                :id="`benefit-${row.BenefitId || i}`"
                :value="row.BenefitId"
                :items="availableBenefitOptions(row.BenefitId)"
                placeholder="Select a Benefit"
                :mode="mode"
                :isNullable="false"
                :enableSearch="true"
                class="w-full"
                @onChange="id => handleBenefitSelectionChange(row, id)"
              />
              <template v-else>{{ row.BenefitName }}</template>
            </td>

            <td v-if="tierData.length >= 1" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier1">
                <!-- Yes/No -->
                <template v-if="row.Tier1?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier1`"
                    :value="String(row.Tier1.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier1!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier1`"
                    :value="String(row.Tier1?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier1!.Value = val"
                  />
                </template>
              </template>
            </td>
            <td v-if="tierData.length >= 2" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier2">
                <template v-if="row.Tier2?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier2`"
                    :value="String(row.Tier2?.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier2!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier2`"
                    :value="String(row.Tier2?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier2!.Value = val"
                  />
                </template>
              </template> 
            </td>
            <td v-if="tierData.length >= 3" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier3">
                <template v-if="row.Tier3?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier3`"
                    :value="String(row.Tier3?.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier3!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier3`"
                    :value="String(row.Tier3?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier3!.Value = val"
                  />
                </template>
              </template>
            </td>
            <td v-if="tierData.length >= 4" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier4">
                <template v-if="row.Tier4?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier4`"
                    :value="String(row.Tier4.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier4!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier4`"
                    :value="String(row.Tier4?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier4!.Value = val"
                  />
                </template>
              </template>
            </td>
            <td v-if="tierData.length >= 5" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier5">
                <template v-if="row.Tier5?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier5`"
                    :value="String(row.Tier5.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier5!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier5`"
                    :value="String(row.Tier5?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier5!.Value = val"
                  />
                </template>
              </template>
            </td>
            <td v-if="tierData.length >= 6" class="px-3 py-6 text-center">
              <template v-if="row.BenefitId && row.Tier6">
                <template v-if="row.Tier6?.Type === 'Yes/No'">
                  <iris-picklist
                    :id="`benefitTierValue-${row.BenefitId}-Tier6`"
                    :value="String(row.Tier6?.Value)"
                    :items="benefitYesNoItems"
                    :isNullable="true"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier6!.Value = val === null ? null : Number(val)"
                  />
                </template>
                <template v-else>
                  <iris-textbox
                    :id="`benefitTierValue-${row.BenefitId}-Tier6`"
                    :value="String(row.Tier6?.Value)"
                    :mode="mode"
                    class="w-full"
                    @onChange="val => row.Tier6!.Value = val"
                  />
                </template>
              </template>
            </td>
          </tr>
          <tr v-if="mode === FormControlModeEnum.edit" class="border-t">
            <td class="px-3 py-4 text-left">
              <iris-icon name="plus" width="1rem" height="1rem" class="text-gray-400" />
            </td>
            <td class="px-3 py-6 text-left whitespace-nowrap" :colspan="(tierData?.length || 0) + 1">
              <button class="btn-light" @click="addNewBenefitRow">
                {{ languageHelper.getMessage("addBenefit") }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-if="mode === FormControlModeEnum.edit" class="mt-4 flex justify-end space-x-2">
      <button class="btn-light" @click="toggleToEdit">{{ languageHelper.getMessage("cancel") }}</button>
      <button class="btn-primary" @click="handleSubmit" :disabled="isSaving">Save</button>
    </div>
    <br />
  </div>
  <iris-modal
    size="large"
    id="tier-modal" 
    :show="isTierModalVisible"
    :title='isNewTier ? languageHelper.getMessage("addTier") : languageHelper.getMessage("editTier")'
    @onHide="onTierModalHide"
    >
    <template #content>
      <iris-alert v-if="formErrors.length > 0" type="danger" class="mb-4">
        <ul class="list-disc list-inside">
            <li v-for="(error, index) in formErrors" :key="index">{{ error }}</li>
        </ul>
      </iris-alert>
      <div v-if="editingTier" class="grid grid-cols-2 gap-4">
        <div>
          <iris-textbox
            id="tier-name"
            :value="editingTier.Name"
            field="Name"
            :label="languageHelper.getMessage('tierName')" 
            required
            :mode="FormControlModeEnum.edit"
            @onChange="val => {
              if (editingTier) {
                editingTier.Name = val
              }
              if (formErrors.length > 0) {
                formErrors = formErrors.filter(err => err !== languageHelper.getMessage('tierNameRequired'))
              }
            }"
          />
          </div>
          <div>
          <iris-picklist
            id="tier-sequence"
            :value="String(editingTier!.Sequence ?? '')"
            :items="sequenceOptions"
            :label="languageHelper.getMessage('sequence')"
            :isNullable="false"
            :mode="FormControlModeEnum.edit"
            required
            @onChange="val => editingTier!.Sequence = Number(val)"
          />
        </div>
      </div>
      <div class="mt-4">
        <iris-textbox
          id="tier-description"
          :value="editingTier?.Description ?? ''"
          :mode="FormControlModeEnum.edit"
          field="Description"
          :label="languageHelper.getMessage('description')"
          @onChange="val => {
            if (editingTier) {
              editingTier.Description = val
            }
          }"
        />
      </div>
      <div class="mt-4">
        <iris-asset-field 
          id="tier-badge" 
          :value="editingTier?.Badge ?? ''"
          @onChange="(value: string) => {
            if (editingTier) editingTier.Badge = value
          }"
          :label="languageHelper.getMessage('badge')" 
          :allowedExtensions="['.jpg', '.jpeg', '.png']"
          hint="Select an image for the tier picture. Minimum size: 100 x 100 pixels." 
        />
      </div>
    </template>
    <template #footer>
      <button class="btn-light" @click="isTierModalVisible = false">
        {{ languageHelper.getMessage('cancel') }}
      </button>
      <button class="btn-primary" @click="saveTier">
        {{ isNewTier ? languageHelper.getMessage("newTier") : languageHelper.getMessage("save") }}
      </button>
      <button v-if="!isNewTier && tierData.length > 1"
        class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        @click="openDeleteTier(editingTier!.Id)"
      >
      {{ languageHelper.getMessage('delete') || 'Delete' }}
    </button>

    </template>
  </iris-modal>
  <iris-modal
    size="large"
    id="criterion-modal"
    :show="isCriterionModalVisible"
    :title="isCriteriaEditing ? languageHelper.getMessage('editCriterion') : languageHelper.getMessage('createCriterion')"
    @onHide="onCriterionModalHide"
  >
    <template #content>
      <iris-alert v-if="formErrors.length > 0" type="danger" class="mb-4">
        <ul class="list-disc list-inside">
          <li v-for="(error, index) in formErrors" :key="index">{{ error }}</li>
        </ul>
      </iris-alert>
      <div class="grid gap-4 mb-4 grid-cols-2">
        <div class="col-span-2">
          <iris-textbox
            id="criterionName"
            :value="editingCriterion?.CriteriaName"
            :mode="FormControlModeEnum.edit"
            :label="languageHelper.getMessage('name')"
            required
            @onChange="val => {
              editingCriterion!.CriteriaName = val
              removeFormError('criterionName')
              validateCriteriaName()
            }"
          />
        </div>
        <div class="col-span-2">
          <iris-picklist
            id="criterionType"
            :value="editingCriterion?.CriteriaType"
            field="CriteriaType"
            :label="languageHelper.getMessage('type')"
            :items="criteriaTypeOptions"
            :isNullable="false"
            required
            :mode="FormControlModeEnum.edit"
            @onChange="onCriteriaTypeChanged"
          />
        </div>
        <div v-if="isCertifications" class="col-span-2">
          <!-- Training Course -->
          <iris-picklist
            id="TrainingCourseId"
            :value="editingCriterion?.Details?.TrainingCourseId"
            field="TrainingCourseId"
            :items="certificationCourses"
            :label="languageHelper.getMessage('trainingCourse')"
            :isNullable="false"
            :mode="FormControlModeEnum.edit"
            :enableSearch=true
            @onChange="val => {
              editingCriterion!.Details.TrainingCourseId = val
              removeFormError('TrainingCourseId')
            }"
          />
          
        </div>
        <div v-if="isRevenue" class="col-span-2">
          <!-- Rollup Field -->
          <iris-picklist
            id="RollupField"
            :value="editingCriterion?.Details.RollupField"
            field="RollupField"
            :items="amountFields"
            :label="languageHelper.getMessage('rollupField')"
            :mode="FormControlModeEnum.edit"
            @onChange="val => editingCriterion!.Details.RollupField = val"
          />
        </div>
        <div v-if="isRevenue || isDeal || isOpportunity" class="col-span-2">
          <!-- Attainment Date Field -->
          <iris-picklist
            id="FilterDateField"
            :value="editingCriterion?.Details.FilterDateField"
            field="FilterDateField"
            :items="dateFields"
            :label="languageHelper.getMessage('attainmentDateField')"
            :mode="FormControlModeEnum.edit"
            @onChange="val => editingCriterion!.Details.FilterDateField = val"
          />
        </div>
        <div v-if="isCustom" class="col-span-2">
          <!-- Unit of Measure -->
          <iris-picklist
            id="UnitOfMeasure"
            :value="editingCriterion?.Details.UnitOfMeasure"
            :items="unitOfMeasureOptions"
            :label="languageHelper.getMessage('unitofMeasure')"
            :isNullable="false"
            :mode="FormControlModeEnum.edit"
            @onChange="val => editingCriterion!.Details.UnitOfMeasure = val"
          />
        </div>
        <div class="col-span-2">
          <field-filters ref="filterRef" :fields="fields" :existingFilters="editingCriterion?.Details?.Filters"/>
        </div>
      </div>
    </template>
    <template #footer>
      <button class="btn-light" @click="onCriterionModalHide">
        {{ languageHelper.getMessage('cancel') }}
      </button>
      <button class="btn-primary" @click="saveCriterionConfig">
        {{ isCriteriaEditing ? languageHelper.getMessage('save') : languageHelper.getMessage('addCriterion') }}
      </button>
    </template>
  </iris-modal>
  <iris-messageBox
    type="Confirm" 
    :message="languageHelper.getMessage('deleteTierMessage')"
    :show="isTierDeleteMessageBoxVisible"
    :title="languageHelper.getMessage('deleteThisTier')" 
    :primaryButtonLabel="languageHelper.getMessage('delete')"
    :cancelButtonLabel="languageHelper.getMessage('cancel')"
    :icon="'trash-can'"
    :size="'small'"
    @onPrimaryClick="confirmDeleteTier"
    @onCancelClick="cancelDeleteTier" />
</template>