<script setup lang="ts">
    import { onMounted } from 'vue'
    import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
    import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
    import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
    import Iris<PERSON>ogo from '@/shared/components/theming-controls/IrisLogo.vue'
    import IrisUserDropDown from '@/shared/components/theming-controls/IrisUserDropDown.vue'
    import IrisAppLauncher from '@/shared/components/theming-controls/IrisAppLauncher.vue'
    import IrisLanguagePicker from '@/shared/components/theming-controls/IrisLanguagePicker.vue'
    import IrisSocialLinks from '@/shared/components/theming-controls/IrisSocialLinks.vue'
    import IrisFooterLinks from '@/shared/components/theming-controls/IrisFooterLinks.vue'
    import IrisInstanceType from '@/shared/components/theming-controls/IrisInstanceType.vue'
    import IrisCustomFooter from '@/shared/components/theming-controls/IrisCustomFooter.vue'
    import IrisGlobalSearchBox from '@/modules/global-search/components/IrisGlobalSearchBox.vue'

    const props = defineProps({
        showMenu: {
            type: Boolean,
            default: true
        }
    })

    const systemInfo = useIrisSystemInfoStore().getData()

    onMounted(() => {
        document.body.classList.add('bg-bg-color')
        document.body.classList.add('dark:bg-bg-color-dark')
    })
</script>
<template>
    <div class="antialiased bg-bg-color dark:bg-bg-color-dark">
        <nav class="site-page-header bg-header-bg dark:bg-header-bg-dark border-b border-border-muted px-4 py-3.5 dark:border-border-muted-dark fixed left-0 right-0 top-0 z-50">
            <div class="flex flex-wrap justify-between items-center">
                <div class="h-10 flex justify-start items-center">
                    <button
                        data-drawer-target="drawer-navigation"
                        data-drawer-toggle="drawer-navigation"
                        aria-controls="drawer-navigation"
                        class="p-1 mr-2 text-header-text rounded-lg cursor-pointer md:hidden 
                        hover:ring-2 hover:ring-header-text dark:hover:ring-header-text-dark 
                        focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark
                        dark:text-header-text-dark"
                    >
                        <svg
                        aria-hidden="true"
                        class="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                        >
                        <path
                            fill-rule="evenodd"
                            d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                            clip-rule="evenodd"
                        ></path>
                        </svg>
                        <svg
                        aria-hidden="true"
                        class="hidden w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                        >
                        <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                        ></path>
                        </svg>
                        <span class="sr-only">Toggle sidebar</span>
                    </button>
                    <IrisLogo 
                        :url="systemInfo.company.theme.logoUrl" 
                        :logo-click-url="systemInfo.company.logoClickUrl" 
                        class="site-logo-img h-12 max-w-32 sm:max-w-52 flex items-center justify-center"/>
                </div>
                <div class="flex items-center gap-4">
                    <div class="global-search-small-screens block" v-show="systemInfo.company.system.globalSearch">
                        <div class="flex items-center justify-end">
                            <div class="siteHeader-items flex items-center">
                                <IrisGlobalSearchBox class="text-header-text dark:text-header-text-dark"/>
                            </div>
                        </div>
                    </div>
                    <!-- Apps -->
                    <div class="hidden lg:inline-flex" v-if="systemInfo.company.instance.instanceName">
                        <IrisInstanceType :value="systemInfo.company.instance"/>
                    </div>
                    
                    <IrisAppLauncher :value="systemInfo.apps" v-if="showMenu" class="p-1 text-header-text dark:text-header-text-dark
                        opacity-80 hover:opacity-100
                        rounded focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark
                    "/>
                    <IrisLanguagePicker 
                        class="p-1 text-header-text dark:text-header-text-dark
                                opacity-80 hover:opacity-100 rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"
                        :value="systemInfo.company.langauges" 
                        :showLabel=false
                        :showFlags="systemInfo.company.system.languageFlagsEnabled" 
                        :selected="systemInfo.userInfo.user.lang"
                    />
                    <IrisUserDropDown 
                        :value="systemInfo.userInfo" 
                        :showUserName="false"
                        class="p-1 text-header-text dark:text-header-text-dark opacity-80 hover:opacity-100 
                            rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"/>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside
            class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full 
            bg-nav-bg border-r border-border-muted md:translate-x-0 dark:bg-nav-bg-dark dark:border-border-muted-dark"
            aria-label="Sidenav"
            id="drawer-navigation"
            >
            <div class="overflow-y-auto py-5 px-3 h-full bg-nav-bg dark:bg-nav-bg-dark">
                <div class="space-y-2">
                    <ul>
                        <li v-for="(menu, index) in systemInfo.menuItems" :key="index" class="flex items-center">
                            <IrisLink 
                                :path="menu.link" 
                                :target="menu.target" 
                                class="flex-1 whitespace-nowrap items-center p-2 w-full text-base font-medium
                                text-nav-text hover:bg-nav-bg-hover rounded-lg transition duration-75 group 
                                dark:text-nav-text-dark dark:hover:bg-nav-bg-hover truncate"
                                aria-current="page">
                                <IrisIcon :name="menu.icon" class="flex-shrink-0 me-2" width="1.1rem" height="1.1rem"/>
                                {{ menu.text }}
                            </IrisLink>
                        </li>
                    </ul>
                </div>
            </div>
        </aside>

        <main class="p-4 md:ml-64 h-auto pt-20 min-h-[calc(100vh-170px)]">
            <RouterView />
        </main>

        <div class="p-4 md:ml-64 h-auto border-t border-border-color">
            <div class="footer">
                <IrisCustomFooter :value="systemInfo.company.customFooterCells" :alignment="systemInfo.company.customFooterCellsAlignment"/>
                <div class="flex justify-start flex-wrap gap-4 flex-col xl:gap-20 xl:flex-row">
                    <IrisSocialLinks :value="systemInfo.company.socialLinks" 
                    class="inline-flex items-center p-4 text-primary bg-bg-color-200 dark:bg-bg-color-200-hover-dark opacity-80 hover:opacity-100 rounded-full" />
                    <div>
                        <IrisFooterLinks :value="systemInfo.company.footerData" class="text-primary hover:text-primary-hover" />
                    </div>
                </div>
            </div>
        </div>
  </div>
    
</template>