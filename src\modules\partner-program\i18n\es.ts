const messages = {
  partnerProgram: "Programa de Socios",
  partnerManagement: "Gestión de Socios",
  helponthispage: "Ayuda en esta página",
  search: "Buscar",
  programs: "Programas",
  account: "Cuenta",
  partnerPrograms: "Programas de Socios",
  serverError: "¡Algo salió mal!",
  loading: "Cargando...",
  yes: "Sí",
  no: "No",
  benefits: "Beneficios",
  programBenefits: "Beneficios del Programa",
  newProgram: "Nuevo Programa",
  programTiers: "Niveles del Programa",
  dragDropMessage: "Arrastra y suelta los niveles para reordenarlos",
  tier: "Nivel",
  remove: "Eliminar",
  addTier: "Ag<PERSON>gar <PERSON>",
  editTier: "Editar Nivel",
  tierBadge: "Insignia de Nivel",
  tierDescription: "Descripción del Nivel",
  tierName: "Nombre del Nivel",
  deleteTier: "Eliminar Nivel",
  noRecords: "No hay datos para mostrar.",
  newBenefit: "Nuevo Beneficio",
  cancel: "Cancelar",
  close: "Cerrar",
  addBenefit: "Agregar Beneficio",
  saving: "Guardando...",
  existingBenefitName: "Ya existe un beneficio con este nombre. Por favor elige otro nombre.",
  highestTierReached: "¡Estás en el nivel más alto!",
  failFetchData: "Error: No se pueden obtener los datos. Contacta al administrador",
  failInitialize: "Error al inicializar los componentes",
  failSave:"Error al guardar el registro. Intenta de nuevo o contacta al administrador.",
  benefitDuplicateNameError: "Ya existe un beneficio con este nombre. Por favor elige otro nombre.",
  save: "Guardar",
  deleteBenefit: "Eliminar Beneficio",
  deleteBenefitMessage: "¿Estás seguro de eliminar este beneficio y todas sus asignaciones?",
  delete: "Eliminar",
  failDelete:"Error al eliminar el registro. Intenta de nuevo o contacta al administrador.",
  edit: "Editar",
  first: "Primero",
  last: "Último",
  next: "Siguiente",
  previous: "Anterior",
  editBenefit: "Editar Beneficio",
  noProgramsAvailable: "No hay programas disponibles.",
  enrollments: "Inscripciones",
  partnerEnrollments: "Inscripciones de Socios",
  newEnrollment: "Nueva Inscripción",
  allPrograms: "Todos los Programas",
  noTiers: "No hay niveles disponibles para el programa seleccionado. Selecciona otro programa o contacta al administrador.",
  editEnrollment: "Editar Inscripción",
  addEnrollment: "Agregar Inscripción",
  deleteEnrollmentMessage: "¿Estás seguro de eliminar esta inscripción y toda la información?",
  deleteEnrollment: "Eliminar Inscripción",
  duplicateEnrollment: "Este socio ya tiene una inscripción activa en este programa.",
  noAccountsAvailable: "No hay cuentas disponibles.",
  deleteProgramMessage: "Estás a punto de eliminar este programa y todos sus datos, incluidos niveles, inscripciones y criterios. Esta acción no se puede deshacer.",
  deleteProgram: "Eliminar Programa de Socios",
  setting: "Configuración",
  settings: "Configuraciones",
  partnerProgramStatus: "Estado del Programa de Socios",
  enablePartnerProgram: "Activar Programa de Socios",
  partnerProgramIsDisabled: "El Programa de Socios está desactivado",
  partnerProgramIsActive: "El Programa de Socios está activo",
  confirmationDisablingPartnerProgram: "¿Estás seguro de que deseas desactivar el Programa de Socios?",
  confirmationDisablingPartnerProgramMessage: "La visibilidad de los niveles y beneficios será oculta.",
  ok: "Aceptar",
  settingsSaved: "¡Configuraciones guardadas exitosamente!",
  back: "Atrás",
  benefit: "Beneficio del Programa de Socios",
  benefitTypeError: "Se requiere el tipo de beneficio.",
  benefitNameRequired: "Se requiere el nombre del beneficio.",
  enrollmentDateMustBeBeforeExpiration: "La fecha de inscripción debe ser anterior a la fecha de expiración.",
  editProgram: "Editar Programa",
  addProgram: "Agregar Programa",
  programNameRequired: "Se requiere el nombre del programa.",
  tierNameRequired: "Se requiere el nombre del nivel.",
  tierInexNameRequired: "Nivel {idx}: se requiere el nombre.",
  duplicateTierName: "Nombre de nivel duplicado",
  unsavedChangesConfimation: "Tienes cambios sin guardar. ¿Estás seguro de que deseas cerrar?",
  minTierError: "Debes configurar al menos un nivel.",
  maxTierError: "Puedes configurar como máximo seis niveles.",
  duplicateIndexTierName: "Nivel {idx}: nombre duplicado {tierName}.",
  missingProgramId: "Programa no válido",
  program: "Programa",
  newTier: "Nuevo Nivel",
  clone: "Clonar",
  configure: "Configurar",
  noTiersDefined: "No hay niveles definidos.",
  criteria: "Criterios del Programa",
  noCriteriaDefined: "No hay criterios definidos.",
  addCriterion: "Agregar Criterio",
  noBenefitDefined: "No hay beneficios definidos.",
  atLeastOneTierRequired: "Introduce al menos un nivel para el programa",
  confirm: "Confirmar",
  noMoreAvailableBenefits: "No hay más beneficios disponibles para agregar.",
  missingProgramIdOrPartnerAccountId: "Falta información. Contacta a la administración.",
  noProgramEnrollments: "No estás inscrito en un programa de socios. Contacta a tu administrador.",
  noPartnerAccount: "Debes ser socio para ver esta página.",
  certifications: "Certificaciones",
  numberofOpportunities: "Número de Oportunidades",
  opportunityRevenue: "Ingresos por Oportunidades",
  numberofDealRegistrations: "Número de Registros de Ofertas",
  custom: "Personalizado",
  configureCriterion: "Configurar Criterio",
  createCriterion: "Crear Criterio",
  create: "Crear",
  criteriaDuplicateNameError: "Ya existe un criterio con este nombre. Por favor elige otro nombre.",
  quantity: "Cantidad",
  revenue: "Ingresos",
  criterionNameRequiredError: "Se requiere el nombre del criterio.",
  criterionTypeRequiredError: "Se requiere el tipo de criterio.",
  trainingCourseRequiredError: "Se requiere un curso de formación para las certificaciones.",
  rollupFieldRequiredError: "Se requiere un campo de acumulación para los ingresos.",
  attainmentDateFieldRequired: "Se requiere el campo de fecha de obtención",
  unitofMeasureRequired: "Se requiere la unidad de medida.",
  editCriterion: "Editar Criterio",
  missingFields: "Faltan datos",
  trainingCourse: "Curso de Formación",
  name: "Nombre",
  type: "Tipo",
  filterLogic: "Lógica del Filtro (opcional)",
  filterLogicPlaceHolder: "p. ej. (1 y 2) o 3",
  reset: "Restablecer",
  addFilter: "Agregar Filtro",
  equals: "igual a",
  notEquals: "no igual a",
  includes: "incluye",
  excludes: "excluye",
  greaterThan: "mayor que",
  lessThan: "menor que",
  lessOrEqual: "menor o igual que",
  greaterOrEqual: "mayor o igual que",
  contains: "contiene",
  notContain: "no contiene",
  startWith: "comienza con",
  notStartWith: "no comienza con",
  partnerAccount: "Cuenta de Socio",
  enrollmentDate: "Fecha de Inscripción",
  expirationDate: "Fecha de Expiración",
}

export default messages