import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { initFlowbite } from 'flowbite'
import { i18n } from '@/shared/services/i18n-helper'
import { UploaderPlugin } from '@syncfusion/ej2-vue-inputs'
import App from '@/App.vue'
import router from '@/router'
import '@/shared/assets/scss/main.scss'

const app = createApp(App)
const pinia = createPinia()

app.config.globalProperties.window = window
app.mixin({
    mounted() {
        initFlowbite()
    }
})
app.use(i18n)
app.use(pinia)
app.use(router)
app.use(UploaderPlugin)
app.provide('pinia', pinia)
app.mount('#iris-app')
