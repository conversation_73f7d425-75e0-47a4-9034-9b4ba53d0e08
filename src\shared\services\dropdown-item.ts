export class DropDownItem {
    label : string = ''
    value : string = ''
    color : string = ''
    selected: boolean = false
    helperText: string = ''
    removable: boolean = true
    
    constructor(label: string, value: string, color?: string, removable: boolean = true) {
        this.label = label
        this.value = value
        this.removable = removable

        if (color != null && color != undefined)
            this.color = color
        else 
            this.color = "#d1d5db"
    }
}