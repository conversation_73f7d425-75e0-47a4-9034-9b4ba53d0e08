const messages = {
    invalidCurrency: "Invalid currency code: {currencyCode}",
    endpointRequired: "Endpoint is required",
    queryRequired: "Query is required",
    idRequired: "Id is required",
    entityNameRequired: "Entity Name is required",
    dataRequired: "Data is required",
    invalidTime: "Invalid time format",
    pingFailed: "Ping failed!",
    invalidTimeout: "Invalid timeout",
    createUserSessionFailed: "Failed to create user session",
    isRequired: "{label} is required",
    notKnownProp: "is not a known property",
    invalidProp: "Invalid value for [{prop}] property",
    invalidLookupField: "Invalid Lookup field [{field}]",
    invalidMaxLength: "Maximum length for the [{prop}] property is {length} characters",
    pageTitleRequired: "Page title is required",
    pageTitleMaxLength: "Maximum length for the page title is {maxTitleLength} characters",
    invalidItem: "Invalid {item}",
    invalidMaxRange: "Max value should be greater than min value.",
    invalidRange: "The value has to be between {min} and {max}.",
    remove: "Remove",
    select: "Select",
    selectAll: "Select All",
    close: "Close",
    search: "Search",
    showAllResults: 'Show all results for "{search}"',
    loading: "Loading...",
    recentRecords: "Recent Records",
    required: "Required",
    duplicateId: "Duplicated id: {id}",
    results: "Results",
    all: "All",
    getWithData: "Model will be ignored with the GET request.",
    singleSelectedColumnOnly: "Only a single column can be selected for sorting.",
    months: "January,February,March,April,May,June,July,August,September,October,November,December",
    year: "Year",
    month: "Month",
    day: "Day",
    selectDate: "Select Date",
    error: "Error",
    emailsDisabled: "All emails are disabled on this portal. To re-enable emails, see documentation.",
    noDataToDisplay: "No data to display."
}

export default messages