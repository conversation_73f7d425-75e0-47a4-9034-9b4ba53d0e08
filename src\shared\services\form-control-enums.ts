export enum FormControlModeEnum {
    edit = 'edit',
    disabled = 'disabled',
    html = 'html',
    text = 'text'
}

export enum CheckboxOutputModeEnum {
    checkbox = 'checkbox',
    toggle = 'toggle'
}

export enum TextboxTypeEnum {
    text = 'text',
    textArea = 'text-area',
    number = 'number',
    percent = 'percent',
    currency = 'currency',
    url = 'url',
    email = 'email',
    phone = 'phone',
    password = 'password',
    secret = 'secret'
}

export enum ComponentSizeEnum {
    default = 'default',
    large = 'large',
    small = 'small'
}

export enum HorizontalAlignmentEnum {
    left = 'left',
    center = 'center',
    right = 'right'
}

export enum AlertTypeEnum {
    light = "light",
    primary = "primary",
    success = "success",
    danger = "danger",
    warning = "warning"
}

export enum ModalSizeEnum {
    small = "small",
    medium = "medium",
    large = "large",
    extraLarge = 'extra-large',
    full = "full"
}

export enum ModalPlacementEnum {
    topLeft = "top-left",
    topRight = "top-right",
    topCenter = "top-center",
    centerLeft = "center-left",
    centerRight = "center-right",
    center = "center",
    bottomLeft = "bottom-left",
    bottomRight = "bottom-right",
    bottomCenter = "bottom-center"
}

export enum PlacementEnum {
    top = "top",
    right = "right",
    left = "left",
    bottom = "bottom"
}

export enum DateTimePickerTypeEnum {
    date = 'date',
    time = 'time',
    dateTime = 'date-time',
    yearMonth = 'year-month'
}