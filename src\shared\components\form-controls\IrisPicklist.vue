<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, computed, getCurrentInstance } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { DropDownItem } from '@/shared/services/dropdown-item'
import { constants } from '@/shared/services/constants'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { useDependentFieldStore } from '@/shared/stores/dependent-field-store'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    searchPlaceholder: String,
    placeholder: String,
    isNullable: {
        type: Boolean,
        default: true 
    },
    enableSearch: {
        type: Boolean,
        default: false 
    },
    value: [String, Object],
    items: Array<DropDownItem>,
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    appendWhenNotFound: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})


// Composables
const languageHelper = new LanguageHelper()
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const depedentRegistry = useDependentFieldStore()
const isVisible = ref(true)
const $el = ref<HTMLElement>()
const $dropdownMenu = ref<HTMLElement>()
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _items = ref(props.items)
const _searchTerm = ref("")
const activeIndex = ref(-2)
const _placeHolder = ref(`--${base.languageHelper.getMessage('select').toUpperCase()}--`)
const _searchPlaceholder = ref(`--${base.languageHelper.getMessage('search').toUpperCase()}--`)
let subscriptionId = ''
let valuesHaveColor = false
let uniqueId: string = "gs" +common.newGuid().replace(/-/g, '')

// computed
const filteredItems = computed(() => {
    const filteredValues = getFilteredValues()

    if (!_searchTerm.value)
        return filteredValues
    
    const regex = new RegExp(_searchTerm.value.trim(), 'i')
    return filteredValues.filter(item => regex.test(item.label))
})

const moveDown = () => {
  if (activeIndex.value < _items.value?.length!) {
    activeIndex.value++
  }
};

const moveUp = () => {
  if (activeIndex.value > -1) {
    activeIndex.value--
  }
};

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())
    
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)
        store.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)

        if (base.isBound()) {
            // register picklist
            subscriptionId = depedentRegistry.registerComponent({ 
                field: props.field!,
                parentField: base.fieldMetadata.value.ControllerName,
                entity: (props.value! as any).__Metadata().Name,
                formId: formId!,
                invalidate: invalidateState
            })
        }
    }
})

onUnmounted(() => {
    if (isVisible.value) {
        store.remove(base.uniqueId)
        depedentRegistry.removeComponent(subscriptionId)
    }
})

watch(_value, () => {

    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)

        //call dependency component registry
        depedentRegistry.dispatch(subscriptionId, _value.value)
    }
})

watch(() => [
    props.value,
    props.field, 
    props.required,
    props.label, 
    props.mode,
    props.hint,
    props.items
], () => initialize(true))

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation) 
        validate()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label
        _items.value = props.items 
        
        if (_items.value == null || _items.value.length == 0) {
            _items.value = []
            
            if (base.fieldMetadata.value?.PicklistEntries?.length) {
                base.fieldMetadata.value.PicklistEntries.forEach((element: { Label: string; Value: string; Color: string }) => {
                    if (element.Color)
                        valuesHaveColor = true

                    _items.value?.push(new DropDownItem(element.Label, element.Value, element.Color))
                })
            }
        }

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
        _items.value = props.items

        _items.value?.forEach(item => {
            if (item.color)
                valuesHaveColor = true
        })
    }

    if (_value.value) {
        _items.value?.forEach(f => f.selected = false)

        const selectedValue = _items.value?.find(f => f.value == _value.value)
            
        if (selectedValue != null)
            selectedValue.selected = true
        else if (props.appendWhenNotFound) {
            const missingItem = new DropDownItem(_value.value, _value.value, '')
            missingItem.selected = true
            _items.value?.push(missingItem)
        }
    }

    if (props.placeholder)
        _placeHolder.value = props.placeholder
    
    if (props.searchPlaceholder)
        _searchPlaceholder.value = props.searchPlaceholder
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true
    
    var errors: ModelError[] = []

    if (_required.value && !_value.value) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0
    base.addErrors(errors)

    return result
}

function selectItem(selectedValue: string) : void {
    _items.value?.forEach(f => f.selected = false)
    const selectedItem = _items.value?.find(f => f.value == selectedValue)

    if (selectedItem)
        selectedItem.selected = true

    _value.value = selectedValue
    $dropdownMenu.value?.classList.add('hidden')
}

const selectItemWithEnter = () => {
  if (activeIndex.value == -1) {
    const selectedItem = ""
    selectItem(selectedItem)
  }
  else if (activeIndex.value >= 0 && activeIndex.value < _items.value!.length) {
    const selectedItem = _items.value![activeIndex.value].value
    selectItem(selectedItem)
  }
}

function onBtnClicked() : void {
    _searchTerm.value = ''
}

function getFilteredValues() : Array<DropDownItem> {
   
   // By record type if exists
   const model = (props.value! as any)
   const rtFieldName = 'RecordTypeId'
   let finalItems = _items.value

   if (base.isBound()) {

        if (rtFieldName in model) {
            const recordTypeId = model[rtFieldName]

            if (recordTypeId
                && model.__Metadata != null 
                && model.__Metadata.RecordTypes != null 
                && model.__Metadata.RecordTypes.length > 0) {

                const rt = model.__Metadata.RecordTypes.find((f: { Id: any, Name: String, IsActive: Boolean }) => f.Id == recordTypeId)

                if (rt.RecordTypePicklistSettings != null && rt.RecordTypePicklistSettings.length > 0) {
                    const picklistSetting = rt.RecordTypePicklistSettings.find((f: { EntityField: string | undefined }) => f.EntityField == props.field)

                    if (picklistSetting != null && picklistSetting.Values) {
                        const values = removeFirstAndLastLetters(picklistSetting.Values).split(',')
                        finalItems = finalItems?.filter(item => values.includes(item.value))
                    }
                }
            }
        }

        // dependent picklist
        if (base.fieldMetadata.value.ControllerName 
            && base.fieldMetadata.value.DependentPicklists != null) {
                
            const controllerValue = model[base.fieldMetadata.value.ControllerName]

            if (controllerValue) {
                const values = base.fieldMetadata.value.DependentPicklists[controllerValue]

                if (values) {
                    const allowedValues = values.map((f: { Value: string }) => f.Value)
                    finalItems = finalItems?.filter(item => allowedValues.includes(item.value))
                }
            }
            else if (_mode.value != FormControlModeEnum.html && _mode.value != FormControlModeEnum.text)
                _mode.value = FormControlModeEnum.disabled
        }
    }

   return finalItems!
}

function removeFirstAndLastLetters(input: string): string {
   if (input.length <= 2) {
       // If the input has 0 or 1 characters, return an empty string
       return ''; 
   } else {
       // Slice the string to remove the first and last characters
       return input.slice(1, -1); 
   }
}

function invalidateState(value: string) : void {
   if (_mode.value == FormControlModeEnum.html || _mode.value == FormControlModeEnum.text)
       return;

   //parent controling field has requested the state to be reset
   _items.value?.forEach(item => item.selected = false)
   _value.value = ''
   getFilteredValues()

   if (value)
       _mode.value = FormControlModeEnum.edit
   else
       _mode.value = FormControlModeEnum.disabled
}

function getSelected() {
    const selectedItem = _items.value?.find(f => f.value == _value.value)

    if (selectedItem)
        return selectedItem
    else if (_value.value)
        return { label: _value.value!, color: '' }
    else
        return null
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="mode == FormControlModeEnum.text">
            {{ getSelected()?.label }}
        </template> 
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label"> 
                {{ _label }} 
                <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
            </label>

            <div v-if="mode == FormControlModeEnum.html && _value">
                <span class="inline-flex items-center px-2 py-1 me-2 text-sm font-medium rounded bg-bg-color-200 dark:bg-page-200-dark border border-border-color">
                    <span v-if="valuesHaveColor" class="flex w-2.5 h-2.5 rounded-full me-1.5 flex-shrink-0" :style='{ backgroundColor: getSelected()?.color }'></span>
                    {{ getSelected()?.label }}
                </span>
            </div>

            <div v-if="mode != FormControlModeEnum.html" class="relative">
                <button :id="id" 
                    :data-dropdown-toggle="id + '_dropdown'"
                    data-dropdown-placement="bottom"
                    data-dropdown-trigger="click"
                    data-dropdown-offset-skidding="0"
                    data-dropdown-offset-distance="5"
                    data-dropdown-delay="300"
                    @click="onBtnClicked" 
                    :disabled="mode == FormControlModeEnum.disabled" 
                    class="bg-bg-color-200 dark:bg-page-200-dark focus:ring-1 focus:outline-none focus:ring-primary text-sm font-medium rounded-lg justify-between p-2 w-full text-center inline-flex items-center border"               
                    :class="{
                        'border-border-color hover:border-border-focus dark:border-border-color-dark': mode != FormControlModeEnum.disabled,
                        'border-border-muted cursor-not-allowed dark:border-border-muted-dark' : mode == FormControlModeEnum.disabled,
                        'text-text-color-400': !_value
                    }"
                    type="button"
                    @keydown.down.prevent="moveDown"
                    @keydown.up.prevent="moveUp"
                    @keydown.enter.prevent="selectItemWithEnter">
                        {{ (_value)? getSelected()?.label : _placeHolder }}
                        <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                        </svg>
                </button>
                
                <div ref="$dropdownMenu" :id="id + '_dropdown'" class="z-10 hidden border border-border-color bg-bg-color divide-y divide-border-muted rounded shadow overflow-hidden w-full dark:border-border-color-dark dark:bg-bg-color-dark dark:divide-border-muted-dark">
                    <div v-if="enableSearch" class="p-3">
                        <label :for="uniqueId" class="sr-only">{{ languageHelper.getMessage('search') }}</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                <svg class="w-4 h-4 text-text-color-400 dark:text-text-color-400-dark" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input type="text" 
                                v-model="_searchTerm" 
                                :id="uniqueId" 
                                class="block w-full p-2 ps-10 text-sm text-text-color-200 border border-border-color rounded-lg bg-bg-color-100
                                 focus:ring-border-focus focus:border-border-focus
                                 dark:text-text-color-200-dark dark:border-border-color-dark dark:bg-bg-color-100-dark"                                
                                :placeholder="_searchPlaceholder">
                        </div>
                    </div>
                    <ul class="py-2 text-sm text-text-color-200 dark:text-text-color-200-dark" :aria-labelledby="id">
                        <li v-if="isNullable">
                            <a href="javascript:void(0);" 
                            @click="selectItem('')"
                            :class="{ 'bg-bg-color-200 dark:bg-bg-color-200-dark': _value == '' || _value == null || activeIndex == -1 }"
                            class="block px-4 py-2 hover:bg-bg-color-200 dark:hover:bg-bg-color-200-dark">
                            {{ _placeHolder }}
                        </a>
                        </li>
                        <li v-for="(item, index) in filteredItems">
                            <a href="javascript:void(0);" 
                                @click="selectItem(item.value)"
                                :class="{ 'bg-bg-color-200 dark:bg-bg-color-200-dark': item.selected || index == activeIndex }"
                                class="block px-4 py-2 hover:bg-bg-color-200 dark:hover:bg-bg-color-200-dark">
                                {{ item.label }}
                                <p v-if="item.helperText" class="text-xs font-normal text-text-color-400 dark:text-text-color-400-dark"> {{ item.helperText }}</p>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Errors -->
                <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
            </div>
        </template>
    </div>
</template>