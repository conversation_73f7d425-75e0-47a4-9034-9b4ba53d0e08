<script setup lang="ts">
    import { onMounted, onUnmounted, ref } from 'vue'
    import { useRouter } from 'vue-router'
    import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
    import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
    import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
    import IrisUserDropDown from '@/shared/components/theming-controls/IrisUserDropDown.vue'
    import IrisInstanceType from '@/shared/components/theming-controls/IrisInstanceType.vue'
    import DataAccess from '@/shared/services/data-access'
    import Common from '@/shared/services/common'

    // Define the structure of the menu items
    interface MenuItem {
        url: string
        iconSvg: string
        label: string
        subNavItems: MenuItem[]
        isNew: boolean
        isBeta: boolean
    }

    const systemInfo = useIrisSystemInfoStore().getData()
    const setupInfo = ref<MenuItem[]>([])
    const openSubMenuIndex = ref<number | null>(null)
    const currentYear = ref(new Date().getFullYear())
    const cssFileId = "setup_iris_css"
    const isLoading = ref(true)
    const router = useRouter()

    function toggleSubMenu(index: number, menuItem: MenuItem): void {
        if (menuItem && menuItem.subNavItems.length > 0) {
            if (openSubMenuIndex.value === index) {
                // If the same submenu is clicked, close it
                openSubMenuIndex.value = null
            } else {
                // Otherwise, open the clicked submenu and close any previously opened one
                openSubMenuIndex.value = index
            }
        }
        else {
            if (Common.IsIrisPath(menuItem.url, router))
                router.push(menuItem.url)
            else
                window.location.href = menuItem.url
        }
    }

    function isNestedMenuActive(menu: MenuItem): boolean {
        const currentRoute = router.currentRoute.value
        const selectedMenuUrl = currentRoute.meta.selectedMenuUrl as string

        return selectedMenuUrl.startsWith(menu.url)
    }

    onMounted(async () => {
        Common.addCssFile(cssFileId,'/_assets/css/iris/setup.css')
            .then(() => { 
                isLoading.value = false
            })

        const dataAccess = new DataAccess()
        setupInfo.value = await dataAccess.execute('/setup/home/<USER>')

        const currentRoute = router.currentRoute.value

        setupInfo.value.forEach((menu, index) => {
            if (menu.subNavItems && menu.subNavItems.length > 0) {
                const matchingSub = menu.subNavItems.find(sub => {
                    const selectedMenuUrl = currentRoute.meta.selectedMenuUrl as string
                    return selectedMenuUrl.startsWith(sub.url)
                });

                if (matchingSub) 
                    openSubMenuIndex.value = index;
            }
        });

        isLoading.value = false;
    })

    onUnmounted(() => { 
        Common.removeCssFile(cssFileId)
    })
</script>

<template>
    <div class="antialiased bg-bg-color dark:bg-bg-color-dark" v-if="!isLoading">
        <nav class="site-page-header bg-header-bg dark:bg-header-bg-dark px-4 py-3.5 dark:border-border-muted-dark fixed left-0 right-0 top-0 z-50">
            <div class="flex flex-wrap justify-between items-center">
                <div class="h-10 flex justify-start items-center">
                    <button
                        data-drawer-target="drawer-navigation"
                        data-drawer-toggle="drawer-navigation"
                        aria-controls="drawer-navigation"
                        class="p-1 mr-2 text-header-text rounded-lg cursor-pointer md:hidden 
                        hover:ring-2 hover:ring-header-text dark:hover:ring-header-text-dark 
                        focus:ring-2 focus:ring-header-text dark:focus:ring-header-text-dark
                        dark:text-header-text-dark"
                    >
                        <svg
                        aria-hidden="true"
                        class="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                        >
                        <path
                            fill-rule="evenodd"
                            d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                            clip-rule="evenodd"
                        ></path>
                        </svg>
                        <svg
                        aria-hidden="true"
                        class="hidden w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                        >
                        <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                        ></path>
                        </svg>
                        <span class="sr-only">Toggle sidebar</span>
                    </button>
                    <img :src="Common.getBaseUrl() + '/_assets/images/magentrix-logo-inverted-sm.png'" crossorigin="anonymous" />

                </div>
                <div class="flex items-center gap-4">
                    <!-- Apps -->
                    <div class="hidden lg:inline-flex" v-if="systemInfo.company.instance.instanceName">
                        <IrisInstanceType :value="systemInfo.company.instance"/>
                    </div>

                    <IrisUserDropDown 
                        :value="systemInfo.userInfo" 
                        :showUserName="false"
                        class="p-1 text-header-text dark:text-header-text-dark opacity-80 hover:opacity-100 
                            rounded-full focus:ring-header-text dark:focus:ring-header-text-dark"/>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside
            class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full 
            bg-nav-bg md:translate-x-0 dark:bg-nav-bg-dark dark:border-border-muted-dark"
            aria-label="Sidenav"
            id="drawer-navigation"
            >
            <div class="overflow-y-auto py-5 h-full bg-nav-bg dark:bg-nav-bg-dark">
                <div class="space-y-2">
                    <ul>
                        <li v-for="(menu, index) in setupInfo" :key="index" class="relative hover:cursor-pointer">
                            <IrisLink 
                                :path="menu.url ?? 'javascript:void(0)'" 
                                class="flex whitespace-nowrap items-center justify-between p-3 px-5 w-full text-base font-medium
                                text-nav-text hover:opacity-100 transition duration-75 group 
                                dark:text-nav-text-dark truncate"
                                :class="openSubMenuIndex === index ? 'opacity-100' : 'opacity-80'"
                                @click.prevent="toggleSubMenu(index, menu)"
                                aria-current="page">
                                <div class="flex items-center justify-between w-full">
                                    <div class="flex items-center gap-5">
                                        <IrisIcon :name="menu.iconSvg" class="flex-shrink-0" width="1.1rem" height="1.1rem"/>
                                        <span>{{ menu.label }}</span>
                                    </div>
                                    <IrisIcon v-if="menu.subNavItems && menu.subNavItems.length > 0" 
                                        name="chevron-right" 
                                        class="flex-shrink-0 transition-transform duration-300" 
                                        :class="openSubMenuIndex === index ? 'rotate-90' : ''"
                                        width="0.7rem" height="0.7rem"/>
                                </div>
                                
                            </IrisLink>

                            <!-- Nested Submenu -->
                            <ul v-if="menu.subNavItems && menu.subNavItems.length > 0 && openSubMenuIndex === index" class="space-y-1">
                                <li v-for="(subMenu, subIndex) in menu.subNavItems" :key="subIndex" 
                                class="flex justify-between items-center hover:bg-nav-bg-hover"
                                :class="isNestedMenuActive(subMenu) ? 'opacity-100' : 'opacity-80'">
                                    <IrisLink
                                    :path="subMenu.url"
                                    class="flex whitespace-nowrap items-center py-2 pr-5 pl-12 w-full text-sm font-medium 
                                    text-nav-text transition duration-75 group truncate"  
                                    >
                                    {{ subMenu.label }}
                                    </IrisLink>
                                    <span v-if="subMenu.isNew" class="bg-primary text-primary-text text-xs font-semibold px-2 py-1 rounded-full float-right">
                                        new
                                    </span>
                                    <span v-if="subMenu.isBeta" class="bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded-full float-right">
                                        beta
                                    </span>
                                </li>
                            </ul>

                        </li>
                    </ul>
                </div>
            </div>
        </aside>

        <main class="p-8 md:ml-64 h-auto pt-24 min-h-screen">
            <RouterView />
        </main>

        <div class="p-4 md:ml-64 h-auto border-t border-border-color">
            <div class="footer">
                <div class="flex justify-start flex-wrap gap-4 flex-col xl:gap-20 xl:flex-row">
                    {{ currentYear }} © Magentrix Corporation - All rights reserved.
                </div>
            </div>
        </div>
    </div>
</template>