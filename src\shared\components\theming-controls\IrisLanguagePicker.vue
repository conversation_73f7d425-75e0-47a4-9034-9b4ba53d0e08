<script setup lang="ts">
import { computed, onMounted, ref, useAttrs, watch, type PropType } from 'vue'
import { Dropdown, type DropdownOptions } from 'flowbite'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

interface LangItem {
    label: string;
    value: string;
}

defineOptions({
  inheritAttrs: false
})

const props = defineProps({
    value: {
        type: Array as PropType<Array<LangItem>>,
        required: true
    },
    selected: {
        type: String,
        required: true
    },
    showFlags: {
        type: Boolean,
        default: true
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

const computedStyleClass = computed(() => {
  const conditionalClasses = props.showLabel ? '' : 'focus:ring-2'
  const attrs = useAttrs()

  return [attrs.class, conditionalClasses].filter(Boolean).join(' ')
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

watch(() => [props.value, props.selected], () => initialize())

const componentId = `lang${Common.newGuid()}`
const selectedLangLabel = ref("")
const systemInfo = useIrisSystemInfoStore().getData()
const $dropdownMenu = ref<HTMLElement>()
const $dropdownBtn = ref<HTMLElement>()
let dropdown : Dropdown

const selectedLangValue = computed(() => {
    const selectedLang = props.value.find(lang => lang.label === selectedLangLabel.value);
    return selectedLang ? selectedLang.value : null;
});

onMounted(() => { 
    initialize()
})

function initialize() {
    const options: DropdownOptions = {
        placement: 'bottom',
        triggerType: 'click'
    }

    dropdown = new Dropdown($dropdownMenu.value, $dropdownBtn.value, options, { id:componentId, override: true });

    setPropValues()
}

function setPropValues() {
    const selectedItem = getSelected()

    if (selectedItem)
        selectedLangLabel.value = selectedItem.label
}

async function changeLang(langId: string) {

    // push notification will force UI refresh, no need to manually change the store value.
    var dataAccess = new DataAccess()
    await dataAccess.execute('/home/<USER>', { newLang: langId })

    // we directly change the store value (we mainly do this for guest user)
    systemInfo.userInfo.user.lang = langId

    // if user is guest user we need to call getStartUpData manually
    if (systemInfo.isGuest)
        await systemInfo.loadData()
    
    dropdown.hide()
    emits('onChange', langId)

    return true
}

function getSelected() : LangItem | null {
    const selected = props.value.find(f => f.value == props.selected)

    if (selected)
        return selected
    else
        return null
}
</script>
<template>
    <button 
        v-if="value && value.length > 1"
        ref="$dropdownBtn"
        :id="'btn' + componentId" 
        type="button" 
        class="btn-mag-lang-picker flex items-center gap-1"
        :class="computedStyleClass"
    >
        <IrisIcon v-if="!showFlags" name="globe"
            aria-hidden="true" height="1.5rem" width="1.5rem"/>

        <img v-if="showFlags" 
            :src="`${Common.getBaseUrl()}/_assets/images/flags/${selectedLangValue}.svg`" 
            class="h-6 flex-initial" />

        <span v-if="showLabel" class="selected-language-name max-w-20 truncate"> {{ selectedLangLabel }}</span>
        
        <IrisIcon v-if="showLabel" name="chevron-down" height="0.7rem" width="0.7rem"/>
    </button>
    <div 
        v-if="value && value.length > 1"
        :id="componentId"
        ref="$dropdownMenu"
        data-popper-placement="bottom-end"
        class="z-10 hidden bg-bg-color divide-y divide-border-muted rounded-lg shadow overflow-hidden border border-border-color dark:border-border-color-dark dark:divide-border-muted-dark"
        :class="{ 
            'w-44': (value.length <= 10),
            'w-80': value.length > 10
        }">
        <ul class="py-2 text-sm text-text-color dark:text-text-color-dark">
            <li v-for="(lang, index) in value">
                <a href="javascript:void(0)" @click.prevent="async () => await changeLang(lang.value)" 
                    :class="{
                        'flex px-4 py-2 cursor-pointer hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark': true,
                        'bg-bg-color-200 dark:bg-bg-color-200-dark': lang.label === selectedLangLabel
                    }"
                >
                    <img v-if="showFlags" 
                        :src="`${Common.getBaseUrl()}/_assets/images/flags/${lang.value}.svg`" 
                        class="h-6 flex-initial mr-3" />
                    <span :title="lang.label" class='text-responsive uppercase'>{{ lang.label }}</span>
                </a>
            </li>
        </ul>
    </div>
</template>