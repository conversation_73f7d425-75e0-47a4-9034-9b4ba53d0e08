<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import LanguageHelper from '@/shared/services/language-helper'

const props = defineProps({ formId: String })
const store = useIrisFormControlStore()
const errors = computed(() => store.getErrors(props.formId))
const languageHelper = new LanguageHelper()

onMounted(() => {
    if (props.formId && !document.getElementById(props.formId))
        throw new Error(languageHelper.getMessage('invalidProp', { prop: 'formId' }))
})
</script>

<template>
    <div class="flex items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800" v-if="errors.length > 0">
        <ul>
            <li class="text-danger" v-for="error in errors">{{ error.message }}</li>
        </ul>
    </div>
</template>
