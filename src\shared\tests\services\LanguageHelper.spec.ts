import { describe, it, expect, beforeAll } from "vitest"
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from "pinia"
import LanguageHelper from "@/shared/services/language-helper"
import I18nHelper from "@/shared/services/i18n-helper"

describe("LanguageHelper", () => {
    let languageHelper: LanguageHelper
    let i18nHelper: I18nHelper

    beforeAll(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
        languageHelper = new LanguageHelper()
        i18nHelper = new I18nHelper()
    })
    
    it("Test default locale message", () => {
        const result = languageHelper.getMessage('hello')

        expect(result).toEqual('Hello, world!')
    })

    it("Test default locale message with single parameter", () => {
        const params = { name: 'Leo' }
        const result = languageHelper.getMessage('bye', params)

        expect(result).toEqual('Goodbye Leo!')
    })

    it("Test default locale message with multiple parameters", () => {
        const params = ['<PERSON>', '<PERSON>']
        const result = languageHelper.getMessage('hi', params)

        expect(result).toEqual('<PERSON>')
    })

    it("Test french language message without changing the it globally", () => {
        const result = languageHelper.getMessage('hello', null, 'fr')

        expect(result).toEqual('Bonjour le monde!')
    })

    it("Test french language message by changing it globally", () => {
        i18nHelper.changeLanguage('fr')

        const result = languageHelper.getMessage('hello')

        expect(result).toEqual('Bonjour le monde!')
        i18nHelper.resetLocale()
    })
})