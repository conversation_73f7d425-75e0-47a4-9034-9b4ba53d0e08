const routes = [
    {
        //When a user navigates to /partner-program, the Index component will be displayed.
        path: '/partner-program',
        name: 'partner-program',
        component: () => import('@/modules/partner-program/views/Index.vue')
    },
    {
        path: '/setup/partner-program/programs',
        name: 'partner-program-programs',
        component: () => import('@/modules/partner-program/views/Programs.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/programs'
        }
    },
    {
        path: '/setup/partner-program/enrollments',
        name: 'partner-program-enrollments',
        component: () => import('@/modules/partner-program/views/Enrollments.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/enrollments'
        }
    },
    {
        path: '/setup/partner-program/benefits',
        name: 'partner-program-benefits',
        component: () => import('@/modules/partner-program/views/Benefits.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/benefits'
        }
    },
    {
        path: '/setup/partner-program/settings',
        name: 'partner-program-settings',
        component: () => import('@/modules/partner-program/views/Settings.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/settings'
        }
    },
    {
        path: '/setup/partner-program/programs/:id',
        name: 'partner-program-program-details',
        component: () => import('@/modules/partner-program/views/ProgramDetails.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/programs'
        }
    },
    {
        path: '/setup/partner-program/benefits/:id',
        name: 'partner-program-benefit-details',
        component: () => import('@/modules/partner-program/views/BenefitDetails.vue'),
        meta: {
            layout: 'Setup',
            selectedMenuUrl: '/setup/partner-program/benefits'
        }
    }
]

export default routes