import en_messages from '@/i18n/en'
import fr_messages from '@/i18n/fr'
import de_messages from '@/i18n/de'
import es_messages from '@/i18n/es'
import it_messages from '@/i18n/it'
import ja_messages from '@/i18n/ja'
import zh_messages from '@/i18n/zh'
import ru_messages from '@/i18n/ru'
import pt_messages from '@/i18n/pt'

const shortDate = { year: 'numeric', month: 'short', day: 'numeric' }
const longDate = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }
const shortTime = { hour: 'numeric', minute: '2-digit' }
const longTime = { hour: 'numeric', minute: '2-digit', second: '2-digit' }

const dateTimeFormats = {
    shortDate: { ...shortDate },
    longDate: { ...longDate },
    shortTime: { ...shortTime },
    longTime: { ...longTime },
    longTimeMs: { ...longTime, fractionalSecondDigits: 3 },
    shortDateTime: { ...shortDate, ...shortTime },
    longDateTime: { ...longDate, ...longTime },
    longDateTimeMs: { ...longDate, ...longTime, fractionalSecondDigits: 3 }
}

const currencyCodes: any = {
    'de-AT': 'EUR',
    'de-CH': 'CHF',
    'de-DE': 'EUR',
    'de-LI': 'CHF',
    'de-LU': 'EUR',
    'en-AU': 'AUD',
    'en-CA': 'CAD',
    'en-GB': 'GBP',
    'en-IE': 'EUR',
    'en-US': 'USD',
    'en-ZA': 'ZAR',
    'es-AR': 'ARS',
    'es-BO': 'BOB',
    'es-CL': 'CLP',
    'es-CO': 'COP',
    'es-CR': 'CRC',
    'es-DO': 'DOP',
    'es-EC': 'USD',
    'es-ES': 'EUR',
    'es-GT': 'GTQ',
    'es-HN': 'HNL',
    'es-MX': 'MXN',
    'es-NI': 'NIO',
    'es-PA': 'PAB',
    'es-PE': 'PEN',
    'es-PR': 'USD',
    'es-PY': 'PYG',
    'es-SV': 'SVC',
    'es-US': 'USD',
    'es-UY': 'UYU',
    'es-VE': 'VES',
    'fr-CA': 'CAD',
    'fr-FR': 'EUR',
    'it-IT': 'EUR',
    'ja-JP': 'JPY',
    'pt-BR': 'BRL',
    'pt-PT': 'EUR',
    'ru-RU': 'RUB',
    'zh-CN': 'CNY'
}

export const locales = { 
    'de-AT': { ...getLocaleInfo('de-AT'), messages: getLanguageInfo('de') },
    'de-CH': { ...getLocaleInfo('de-CH'), messages: getLanguageInfo('de') },
    'de-DE': { ...getLocaleInfo('de-DE'), messages: getLanguageInfo('de') },
    'de-LI': { ...getLocaleInfo('de-LI'), messages: getLanguageInfo('de') },
    'de-LU': { ...getLocaleInfo('de-LU'), messages: getLanguageInfo('de') },
    'en-AU': { ...getLocaleInfo('en-AU'), messages: getLanguageInfo('en') },
    'en-CA': { ...getLocaleInfo('en-CA'), messages: getLanguageInfo('en') },
    'en-GB': { ...getLocaleInfo('en-GB'), messages: getLanguageInfo('en') },
    'en-IE': { ...getLocaleInfo('en-IE'), messages: getLanguageInfo('en') },
    'en-US': { ...getLocaleInfo('en-US'), messages: getLanguageInfo('en') },
    'en-ZA': { ...getLocaleInfo('en-ZA'), messages: getLanguageInfo('en') },
    'es-AR': { ...getLocaleInfo('es-AR'), messages: getLanguageInfo('es') },
    'es-BO': { ...getLocaleInfo('es-BO'), messages: getLanguageInfo('es') },
    'es-CL': { ...getLocaleInfo('es-CL'), messages: getLanguageInfo('es') },
    'es-CO': { ...getLocaleInfo('es-CO'), messages: getLanguageInfo('es') },
    'es-CR': { ...getLocaleInfo('es-CR'), messages: getLanguageInfo('es') },
    'es-DO': { ...getLocaleInfo('es-DO'), messages: getLanguageInfo('es') },
    'es-EC': { ...getLocaleInfo('es-EC'), messages: getLanguageInfo('es') },
    'es-ES': { ...getLocaleInfo('es-ES'), messages: getLanguageInfo('es') },
    'es-GT': { ...getLocaleInfo('es-GT'), messages: getLanguageInfo('es') },
    'es-HN': { ...getLocaleInfo('es-HN'), messages: getLanguageInfo('es') },
    'es-MX': { ...getLocaleInfo('es-MX'), messages: getLanguageInfo('es') },
    'es-NI': { ...getLocaleInfo('es-NI'), messages: getLanguageInfo('es') },
    'es-PA': { ...getLocaleInfo('es-PA'), messages: getLanguageInfo('es') },
    'es-PE': { ...getLocaleInfo('es-PE'), messages: getLanguageInfo('es') },
    'es-PR': { ...getLocaleInfo('es-PR'), messages: getLanguageInfo('es') },
    'es-PY': { ...getLocaleInfo('es-PY'), messages: getLanguageInfo('es') },
    'es-SV': { ...getLocaleInfo('es-SV'), messages: getLanguageInfo('es') },
    'es-US': { ...getLocaleInfo('es-US'), messages: getLanguageInfo('es') },
    'es-UY': { ...getLocaleInfo('es-UY'), messages: getLanguageInfo('es') },
    'es-VE': { ...getLocaleInfo('es-VE'), messages: getLanguageInfo('es') },
    'fr-CA': { ...getLocaleInfo('fr-CA'), messages: getLanguageInfo('fr') },
    'fr-FR': { ...getLocaleInfo('fr-FR'), messages: getLanguageInfo('fr') },
    'it-IT': { ...getLocaleInfo('it-IT'), messages: getLanguageInfo('it') },
    'ja-JP': { ...getLocaleInfo('ja-JP'), messages: getLanguageInfo('ja') },
    'pt-BR': { ...getLocaleInfo('pt-BR'), messages: getLanguageInfo('pt') },
    'pt-PT': { ...getLocaleInfo('pt-PT'), messages: getLanguageInfo('pt') },
    'ru-RU': { ...getLocaleInfo('ru-RU'), messages: getLanguageInfo('ru') },
    'zh-CN': { ...getLocaleInfo('zh-CN'), messages: getLanguageInfo('zh') }
}

export function getLanguageInfo(language: string) {
    switch(language) {
        case 'en':
            return en_messages
            break
        case 'fr':
            return fr_messages
            break
        case 'de':
            return de_messages
            break
        case 'es':
            return es_messages
            break
        case 'it':
            return it_messages
            break
        case 'ja':
            return ja_messages
            break
        case 'zh':
            return zh_messages
            break
        case 'ru':
            return ru_messages
            break
        case 'pt':
            return pt_messages
            break
        default:
            return en_messages
    }
}

function getLocaleInfo(local: string) {
    const currencyCode = currencyCodes[local]
    const result = {
        currency: {
            style: 'currency',
            currency: currencyCode
        },
        decimal: {
            style: 'decimal'
        },
        percent: {
            style: 'percent'
        },
        ...dateTimeFormats
    }

    return result
}