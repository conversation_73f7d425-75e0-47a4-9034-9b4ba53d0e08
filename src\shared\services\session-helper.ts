import DataAccess from '@/shared/services/data-access'
import LanguageHelper from '@/shared/services/language-helper'
import { RequestMethod } from '@/shared/services/enums'

export default class SessionHelper {
    private lastPing: number | null = null
    private timeoutSeconds: number | null = null
    private intervalId: any

    private _navigated = false
    get navigated() { return this._navigated }
    set navigated(value: boolean) {
        this._navigated = value
        
        if (this.trackRouting)
            this.trackRouting()
    }

    private get timeoutMilliseconds() { return this.timeoutSeconds! * 1000 }

    dataAccess = new DataAccess()
    languageHelper = new LanguageHelper()

    start(timeoutSeconds: number): void {
        if (timeoutSeconds < 0)
            throw new Error(this.languageHelper.getMessage('invalidTimeout'))

        this.timeoutSeconds = timeoutSeconds
        this.trackRouting()
        this.checkInactivity()
    }

    stop(): void {
        clearInterval(this.intervalId)
    }

    trackRouting(): void {
        const elapsed = !this.lastPing || (Date.now() - this.lastPing) > this.timeoutMilliseconds
    
        if (elapsed)
            this.ping()
    }

    checkInactivity(): void {
        this.intervalId = setInterval(() => {
            if (this.navigated)
                this.ping()
        }, this.timeoutMilliseconds);
    }

    ping(): void {
        this.dataAccess.execute('iris/ping', null, RequestMethod.get).then(res => {
            this.lastPing = Date.now()
            this.navigated = false
        }).catch(err => {
            console.error(this.languageHelper.getMessage('pingFailed'), err)
        })
    }
}