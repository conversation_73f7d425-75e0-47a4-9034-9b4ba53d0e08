<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { Drawer, type DrawerOptions } from 'flowbite'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: '' // Default to an empty string if no title is provided
  },
  show: {
    type: Boolean,
    default: false
  },
  placement: {
    type: String,
    default: 'left'
  },
  backdrop: {
    type: Boolean,
    default: true
  }
});

const emits = defineEmits(['onShow', 'onHide', 'onToggle']);
const $el = ref<HTMLElement | undefined>();

// Define options for the Drawer
const options: DrawerOptions = {
  placement: props.placement as 'left' | 'right' | 'top' | 'bottom',
  backdrop: props.backdrop,
  backdropClasses: 'bg-text-color-400 dark:bg-base-400-dark opacity-50 fixed inset-0 z-40',
  onHide: () => {
    emits('onHide'); // <PERSON><PERSON> listens to this to update its 'show' state
  },
  onShow: () => {
    emits('onShow');
  },
  onToggle: () => { // 'event' parameter removed as it wasn't used
    emits('onToggle');
  }
};

let drawer: Drawer | undefined;

onMounted(() => {
  if ($el.value) {
    drawer = new Drawer($el.value, options);
  }
  // Initialize visibility based on the initial 'show' prop
  // Ensures drawer state matches prop on component mount
  if (props.show) {
    if (drawer?.isHidden()) {
        drawer?.show();
    }
  } else {
    if (drawer?.isVisible()){
        drawer?.hide();
    }
  }
});

// Watch for changes in the 'show' prop to control the drawer
watch(() => props.show, (newValue) => {
  if (newValue) {
    drawer?.show();
  } else {
    drawer?.hide();
  }
});

function closeDrawer(): void {
  drawer?.hide();
}

</script>

<template>
  <div
    :id="id"
    ref="$el"
    class="fixed top-0 right-0 z-50 h-screen overflow-y-auto transition-transform translate-x-full bg-bg-color dark:bg-bg-color-dark lg:w-[500px]"
    tabindex="-1"
    aria-labelledby="drawer-right-label"
  >
    <div class="flex flex-col h-full">
      <button
        type="button"
        class="absolute top-4 right-4 inline-flex items-center p-1 text-sm bg-transparent rounded"
        @click="closeDrawer"
        aria-label="Close"
      >
        <IrisIcon name="xmark" />
      </button>

      <header v-if="title" class="flex justify-between items-center p-4">
        <h2 class="text-lg font-semibold" id="drawer-right-label">{{ title }}</h2>
      </header>

      <div v-if="title" class="flex-1 overflow-y-auto px-4">
        <slot name="content"></slot>
      </div>
      <slot v-else name="content"></slot>
      <footer class="flex justify-between gap-4 mt-4 px-4 pb-4">
        <slot name="footer"></slot>
      </footer>
    </div>
  </div>
</template>