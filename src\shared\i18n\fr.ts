const messages = {
    invalidCurrency: "Code de devise invalide: {currencyCode}",
    endpointRequired: "Le point de terminaison est requis",
    queryRequired: "La requête est requise",
    idRequired: "L'identifiant est requis",
    entityNameRequired: "Le nom de l'entité est requis",
    dataRequired: "Des données sont requises",
    invalidTime: "Format d'heure invalide",
    pingFailed: "Échec du ping!",
    invalidTimeout: "Délai d'attente non valide",
    createUserSessionFailed: "Échec de la création de la session utilisateur",
    isRequired: "{label} est obligatoire",
    notKnownProp: "n'est pas une propriété connue",
    invalidProp: "Valeur non valide pour la propriété [{prop}]",
    invalidLookupField: "Champ de recherche invalide [{field}]",
    invalidMaxLength: "La longueur maximale de la propriété [{prop}] est de {length} caractères",
    pageTitleRequired: "Le titre de la page est requis",
    pageTitleMaxLength: "La longueur maximale du titre de la page est de {maxTitleLength} caractères",
    invalidItem: "{item} invalide",
    invalidMaxRange: "La valeur maximale doit être supérieure à la valeur minimale.",
    invalidRange: "La valeur doit être comprise entre {min} et {max}.",
    remove: "Supprimer",
    select: "Sélectionner",
    selectAll: "Sélectionner tout",
    close: "Fermer",
    search: "Recherche",
    showAllResults : 'Afficher tous les résultats pour "{search}"',
    loading: "Chargement...",
    recentRecords: "Enregistrements récents",
    required: "Requis",
    duplicateId: "Identifiant dupliqué: {id}",
    results: "Résultats",
    all: "Toute",
    getWithData: "Le modèle sera ignoré avec la requête GET.",
    singleSelectedColumnOnly: "Seule une seule colonne peut être sélectionnée pour le tri.",
    months: "Janvier, février, mars, avril, mai, juin, juillet, août, septembre, octobre, novembre, décembre",
    year: "Année",
    month: "Mois",
    day: "Jour",
    selectDate: "Sélectionner une date",
    error: "Erreur",
    emailsDisabled: "Tous les e-mails sont désactivés sur ce portail. Pour réactiver les e-mails, consultez la documentation.",
    noDataToDisplay : "Aucune donnée à afficher."
}

export default messages