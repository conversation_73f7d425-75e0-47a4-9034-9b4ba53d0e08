import { RequestMethod, ContentType } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Common from '@/shared/services/common'
import Auth from '@/shared/services/auth'
import I18nHelper from '@/shared/services/i18n-helper'
import LanguageHelper from '@/shared/services/language-helper'
import RequestPermission from '@/shared/services/request-permission'
import DatabaseError from '@/shared/services/database-error'

export default class DataAccess {
    i18nHelper = new I18nHelper()
    languageHelper = new LanguageHelper()
    restVersion = '3.0'

    query(query: string, permissions: RequestPermission | null = null): Promise<any> {
        if (!query)
          throw new Error(this.languageHelper.getMessage('queryRequired'))
    
        const path = '/iris/query'
        const model = { query, permissions }
        const data = this.convertToEncodedKeyValuePairs(model)
        const result = this.postData(path, data, ContentType.form_url_encoded)

        return result
    }

    execute(path: string, model: any = null, method = RequestMethod.post): Promise<any> {
        if (!path)
          throw new Error(this.languageHelper.getMessage('endpointRequired'))
        
        if (method == RequestMethod.get && model)
          throw new Error(this.languageHelper.getMessage('getWithData'))
        
        if (method === RequestMethod.get)
          return this.getData(path)
        else {
          var data = model ? this.convertToEncodedKeyValuePairs(model) : null

          return this.postData(path, data, ContentType.form_url_encoded)
        }
    }

    retrieve(id: string, permissions: RequestPermission | null = null): Promise<any> {
        if (!id)
          throw new Error(this.languageHelper.getMessage('idRequired'))
    
        const model = { id, permissions }
        const data = this.convertToEncodedKeyValuePairs(model)
        const path = '/iris/retrieve' + '?' + data
        
        return this.getData(path)
    }

    create(entityName: string, data: any): Promise<any> {
        if (!entityName)
          throw new Error(this.languageHelper.getMessage('entityNameRequired'))
    
        if (!data)
          throw new Error(this.languageHelper.getMessage('dataRequired'))
    
        const path = `/api/${this.restVersion}/entity/${entityName}`
    
        return this.postData(path, data)
    }

    edit(entityName: string, data: any): Promise<any> {
        if (!entityName)
          throw new Error(this.languageHelper.getMessage('entityNameRequired'))
    
        if (!data)
          throw new Error(this.languageHelper.getMessage('dataRequired'))
    
        const path = `/api/${this.restVersion}/entity/${entityName}`
    
        return this.patchData(path, data)
    }

    upsert(entityName: string, data: any): Promise<any> {
      if (!entityName)
        throw new Error(this.languageHelper.getMessage('entityNameRequired'))
  
      if (!data)
        throw new Error(this.languageHelper.getMessage('dataRequired'))
  
      const path = `/api/${this.restVersion}/entity/${entityName}`
  
      return this.putData(path, data)
    }

    delete(entityName: string, id: string, permanent: boolean = false): Promise<any> {
        if (!id)
          throw new Error(this.languageHelper.getMessage('idRequired'))

        if (!entityName)
          throw new Error(this.languageHelper.getMessage('entityNameRequired'))

        const path = `/api/${this.restVersion}/entity/${entityName}/${id}?isPermanent=${permanent}`
    
        return this.deleteData(path)
    }

    deleteMany(entityName: string, ids: string[], permanent: boolean = false): Promise<any> {
        if (!ids)
          throw new Error(this.languageHelper.getMessage('idRequired'))

        if (!entityName)
          throw new Error(this.languageHelper.getMessage('entityNameRequired'))
    
        const path = `/api/${this.restVersion}/entity/${entityName}?isPermanent=${permanent}`
    
        return this.deleteData(path, ids)
    }

    convertToEncodedKeyValuePairs(obj: any): string {
        let result = ''

        for (const [key, value] of Object.entries(obj)) {
          let modifiedValue: string
          
          if (value === null || value === undefined)
            modifiedValue = ''
          else if (typeof value == 'object')
            modifiedValue = JSON.stringify(value)
          else
            modifiedValue = value.toString()
          
          result += `${key}=${encodeURIComponent(modifiedValue)}&`
        }

        if (result.endsWith('&'))
          result = result.substring(0, result.length - 1)

        return result
    }

    async getData(path: string): Promise<any> {
      const url = Common.getFullUrl(path)
      const options = this.getRequestOptions()
      const response = await fetch(url, options)

      if (this.isForbidden(response))
        return

      const result = await response.json()

      if (!response.ok)
        this.throwError(result)

      if (this.isIrisData(result))
          this.addModelExtendedProps(result)

      return result
    }

    async postData(path: string, data: any, contentType: ContentType = ContentType.json): Promise<any> {
        const url = Common.getFullUrl(path)
        const options = { ...this.getRequestOptions(RequestMethod.post, contentType), body: contentType == ContentType.json ? JSON.stringify(data) : data }
        const response = await fetch(url, options)

        if (this.isForbidden(response))
          return

        const result = await response.json()

        if (!response.ok)
          this.throwError(result)
        
        if (this.isIrisData(result))
            this.addModelExtendedProps(result)

        this.fillIdsOnCreate(data, result)

        if (data?.hasOwnProperty('ModifiedOn'))
          data.ModifiedOn = (new Date()).toISOString().slice(0, -1)

        return result
    }

    async patchData(path: string, data: any): Promise<any> {
        const url = Common.getFullUrl(path)
        const options = { ...this.getRequestOptions(RequestMethod.patch), body: JSON.stringify(data) }
        const response = await fetch(url, options)

        if (this.isForbidden(response))
          return

        const result = await response.json()

        if (!response.ok)
          this.throwError(result)

        if (this.isIrisData(result))
            this.addModelExtendedProps(result)
    
        if (data?.hasOwnProperty('ModifiedOn'))
          data.ModifiedOn = (new Date()).toISOString().slice(0, -1)

        return result
    }
    
    async putData(path: string, data: any): Promise<any> {
      const url = Common.getFullUrl(path)
      const options = { ...this.getRequestOptions(RequestMethod.put), body: JSON.stringify(data) }
      const response = await fetch(url, options)

      if (this.isForbidden(response))
        return

      const result = await response.json()

      if (!response.ok)
        this.throwError(result)
      
      if (this.isIrisData(result))
          this.addModelExtendedProps(result)

      if (!data.Id)
        this.fillIdsOnCreate(data, result)

      if (data?.hasOwnProperty('ModifiedOn'))
        data.ModifiedOn = (new Date()).toISOString().slice(0, -1)

      return result
    }

    async deleteData(path: string, data: any = null): Promise<any> {
        const url = Common.getFullUrl(path)
        const options = { ...this.getRequestOptions(RequestMethod.delete), body: data ? JSON.stringify(data) : null }
        const response = await fetch(url, options)

        if (this.isForbidden(response))
          return

        const result = await response.json()

        if (!response.ok)
          this.throwError(result)
    
        return result
    }

    getRequestOptions(method: RequestMethod = RequestMethod.get, contentType: ContentType = ContentType.json): any {
        let options: any = {
          method: method,
          headers: {
            'Content-Type': contentType,
            'MAG-StrictMode': 'false'
          }
        }

        const systemInfo = useIrisSystemInfoStore().getData()

        if (method == RequestMethod.post)
          options.headers.__RequestVerificationToken = systemInfo.antiForgeryToken

        if (systemInfo.env.production)
          options.credentials = 'include'
        else
          options.headers.Authorization = Auth.sessionInfo?.token

        return options
    }

    isIrisData(obj: any) {
        if (obj == null)
          return false
    
        return obj.hasOwnProperty('__metadatas')
    }

    addModelExtendedProps(model: any): void {
      const entityTypes = model.__entityTypes
      const permissions = model.__permissions
      const metadatas = model.__metadatas
      const ignore = ['__entityTypes', '__permissions', '__metadatas']
  
      for (const [key, value] of Object.entries(model)) {
        if (value === null || typeof value !== 'object' || ignore.includes(key))
          continue
  
        const entityType = entityTypes[key]
  
        if (!entityType)
          continue
  
        if (Array.isArray(value)) {
          value.forEach(v => {
            v['__EntityName'] = entityType;
            this.addPermissionProperty(key, v, permissions);
            v['__Metadata'] = () => metadatas[entityType];
            v['__Errors'] = [];
            this.addExtendedPropsToRelatedFields(v, entityType, metadatas, permissions);
          })
        } else {
          (value as any)['__EntityName'] = entityType;
          this.addPermissionProperty(key, value, permissions);
          (value as any)['__Metadata'] = () => metadatas[entityType];
          (value as any)['__Errors'] = [];
          this.addExtendedPropsToRelatedFields(value, entityType, metadatas, permissions);
        }
      }
    }
      
    addExtendedPropsToRelatedFields(value: any, entityType: string, metadatas: any, permissions: any): void {
      var relations = metadatas[entityType].Relationships

      relations.forEach((x: any) => {
        if (value[x.Name]) {
          var r_EntityType = x.Entity === 'Person' ? 'User' : x.Entity;
          value[x.Name]['__EntityName'] = r_EntityType;
          this.addPermissionProperty(x.Name, value[x.Name], permissions);
          value[x.Name]['__Metadata'] = () => metadatas[r_EntityType];
          value[x.Name]['__Errors'] = [];
        }
      })
    }
  
    addPermissionProperty(key: string, item: any, permissions: any): void {
      var permission = null
      const permissionItems = permissions[key] ? permissions[key].items : null
      
      if (permissionItems) {
        const result = permissionItems.filter((x: any) => x['id'] === item['Id'])
        
        if (result.length > 0) {
          const permissionItem = result[0]
          const c = permissions[key].create
          const u = permissionItem.update
          const d = permissionItem.delete
  
          permission = { "create": c, "update": u, "delete": d }
        }
      }
  
      item['__Permission'] = permission
    }
  
    fillIdsOnCreate(data: any | [], result: any | []): void {
      if (data == null)
        return
  
      const isArray = Array.isArray(data)
  
      if (isArray) {
        const count = data.length
  
        if (count == 0)
          return
          
        for (var i = 0; i < count; i++)
          if (data[i].Id == undefined && result[i].Id)
            data[i].Id = result[i].Id
      }
      else {
        if (data.Id == undefined && result.Id)
          data.Id = result.Id
      }
    }

    isForbidden(response: any): boolean {
      if (!window.top)
        return false

      const result = response.ok && response.redirected && response.url.toLowerCase().includes('/user/login')

      if (!result)
        return false

      let url = Common.getFullUrl('/user/login')
      let returnUrl = window.top.location.pathname + window.top.location.search

      if (returnUrl == '/')
        returnUrl = ''

      if (returnUrl)
        url += '?returnurl=' + encodeURIComponent(returnUrl)

      window.location.replace(url)

      return true
    }

    throwError(error: any) {
      if (typeof error === 'string')
        throw new Error(error)
      else if (error.hasOwnProperty('message'))
        throw new Error(error.message)
      else if (error.hasOwnProperty('errors'))
        throw new DatabaseError("Database Error", error.errors)
      else
        throw new Error('Unknown error!') 
    }
}
