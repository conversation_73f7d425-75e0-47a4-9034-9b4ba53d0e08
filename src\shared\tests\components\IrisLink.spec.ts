import { describe, it, expect } from "vitest"
import { VueWrapper, mount } from "@vue/test-utils"
import Iris<PERSON>ink from "@/shared/components/general-controls/IrisLink.vue"
import router from '@/router'

describe("IrisLink", () => {
    const createWrapper = (slot: string | null = null): VueWrapper => {
        const wrapper = mount(IrisLink, {
            props: { path: '' },
            slots: slot ? { default: slot } : {},
            global: { plugins: [router] }
        })

        return wrapper
    }

    it("Test component initialization", () => {
        expect(IrisLink).toBeTruthy()
    })

    it("External link (https)", async () => {
        const wrapper = createWrapper()

        await wrapper.setProps({ path: 'magentrix.com' })

        const result = wrapper.attributes("href")

        expect(result).toEqual('https://magentrix.com')
    })

    it("External link (http)", async () => {
        const wrapper = createWrapper()

        await wrapper.setProps({ path: 'magentrix.com', secure: false })

        const result = wrapper.attributes("href")

        expect(result).toEqual('http://magentrix.com')
    })

    it("Internal link", async () => {
        const wrapper = createWrapper()

        await wrapper.setProps({ path: 'global-search' })

        const result = wrapper.text()

        expect(result).toEqual('global-search')
    })

    it("Internal link (caption)", async () => {
        const wrapper = createWrapper('Global Search')

        await wrapper.setProps({ path: 'global-search' })

        const result = wrapper.text()

        expect(result).toEqual('Global Search')
    })

    it("External link (target)", async () => {
        const wrapper = createWrapper()

        await wrapper.setProps({ path: 'magentrix.com', target: '_self' })

        const result = wrapper.attributes()['target']

        expect(result).toEqual('_self')
    })

    it("External link (class)", async () => {
        const wrapper = createWrapper()

        await wrapper.setProps({ path: 'magentrix.com', class: 'text-red-800' })

        const result = wrapper.attributes()['class']

        expect(result).toEqual('text-red-800')
    })
})