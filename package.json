{"name": "iris-vue-ts", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@pinia/testing": "^1.0.2", "@syncfusion/ej2-base": "^30.1.38", "@syncfusion/ej2-vue-filemanager": "^30.1.38", "@syncfusion/ej2-vue-inputs": "^30.1.38", "@syncfusion/ej2-vue-navigations": "^30.1.37", "@vimeo/player": "^2.29.0", "dayjs": "^1.11.10", "flowbite": "^2.3.0", "flowbite-datepicker": "^1.2.6", "pinia": "^3.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.17", "vue-i18n": "^11.1.9", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/node": "^24.0.10", "@types/sortablejs": "^1.15.8", "@types/vimeo__player": "^2.18.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.19", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "sass": "^1.89.2", "tailwindcss": "^3.4.3", "typescript": "^5.8.3", "vite": "^7.0.1", "vitest": "^3.2.4", "vue-tsc": "^3.0.1"}}