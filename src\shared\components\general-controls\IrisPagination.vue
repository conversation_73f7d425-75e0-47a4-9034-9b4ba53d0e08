<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    total: {
        type: Number,
        default: 0
    },
    pageSize: {
        type: Number,
        default: 1 // Default value for pageSize
    },
    currentPage: {
        type: Number,
        default: 0 // Default value for currentPage
    },
    nextAndPreviousOnly: {
        type: Boolean,
        default: false
    },
    useIconsOnly: {
        type: Boolean,
        default: false
    },
    size: String,
    firstBtnLbl: String,
    LastBtnLbl: String,
    nextBtnLbl: String,
    prevBtnLbl: String,
    title_aria_label: String,
    pageChanged: Function
})

const _currentPage = ref(props.currentPage)

onMounted(() => {
    initialize()
})

const pageCount = computed(() => (Math.ceil(props.total / props.pageSize) - 1 < 0) ? 0 : Math.ceil(props.total / props.pageSize) - 1)

function initialize() {
    if (props.pageSize <= 0)
        throw new Error("Invalid page size.")

    if (props.total < 0)
        throw new Error("Invalid total records.")
    
    if (props.total == 0)
        return

    // populate props into refs:
    _currentPage.value = props.currentPage
}

function firstClicked() {
    if (_currentPage.value == 0)
        return

    _currentPage.value = 0
    pageChanged()
}

function lastClicked () {
    if (_currentPage.value == pageCount.value)
        return

    _currentPage.value = pageCount.value
    pageChanged()
}

function prevClicked() {
    if (_currentPage.value == 0)
        return

    _currentPage.value = _currentPage.value - 1
    pageChanged()
}

function nextClicked() {
    if (_currentPage.value == pageCount.value)
        return;

    _currentPage.value = _currentPage.value + 1
    pageChanged()
}

function pageChanged() {
    if (props.pageChanged)
        props.pageChanged(_currentPage.value)
}
</script>

<template>
    <div class="flex items-center justify-center">
        <nav aria-label='page navigation'>
            <ul class="inline-flex -space-x-px text-sm pagination" :class="{['pagination-' + size]: size !== undefined && size !== null}">
                <li v-if="!nextAndPreviousOnly" :class="{'disabled': _currentPage == 0}">
                    <a
                        href="javascript:void(0)"
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight border border-e-0 
                        border-border-color rounded-s-lg nav-first
                        dark:border-border-color-dark"
                        :class="(_currentPage == 0 ?'disabled cursor-not-allowed text-text-color-400 dark:text-text-color-400-dark' : 'hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark')" 
                        @click.prevent="_currentPage == 0 ? null : firstClicked()"
                        :aria-label="firstBtnLbl">
                        <span v-if="useIconsOnly">
                            <IrisIcon 
                                name="backward-step" 
                                :class="'iris-icon ' + (_currentPage == 0? 'text-text-color-400 dark:text-text-color-400-dark' : 'text-primary hover:text-primary-hover')" >
                            </IrisIcon>
                        </span>
                        <span v-if="!useIconsOnly">
                            <span aria-hidden="true">&laquo;</span>{{firstBtnLbl}}
                        </span>
                    </a>
                </li>
                <li :class="{'disabled': _currentPage == 0}">
                    <a 
                        href="javascript:void(0)"
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight border 
                        border-border-color nav-previous
                        dark:border-border-color-dark"
                        :class="(_currentPage == 0 ?'disabled cursor-not-allowed text-text-color-400 dark:text-text-color-400-dark' : 'hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark')"
                        @click.prevent="_currentPage == 0 ? null :prevClicked()" 
                        :aria-label="prevBtnLbl">
                        <span v-if="useIconsOnly">
                            <IrisIcon 
                                name="play" 
                                :class="'iris-icon rotate-180 ' + (_currentPage == 0? 'text-text-color-400 dark:text-text-color-400-dark' : 'text-primary hover:text-primary-hover')"
                                >
                            </IrisIcon>
                        </span>
                        <span v-if="!useIconsOnly"><span aria-hidden="true">&laquo;</span>{{prevBtnLbl}}</span>
                    </a>
                </li>
                <li :class="{'disabled': _currentPage == pageCount}">
                    <a 
                        href="javascript:void(0)"
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight border border-border-color 
                        nav-next dark:border-border-color-dark"
                        :class="(_currentPage == pageCount ?'disabled cursor-not-allowed text-text-color-400 dark:text-text-color-400-dark' : 'hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark')"
                        @click.prevent="_currentPage == pageCount ? null : nextClicked()" 
                        :aria-label="nextBtnLbl">
                        <span v-if="useIconsOnly">
                                <IrisIcon 
                                    name="play" 
                                    :class="'iris-icon ' + (_currentPage == pageCount? 'text-text-color-400 dark:text-text-color-400-dark' : 'text-primary hover:text-primary-hover')"
                                >
                            </IrisIcon>
                        </span>
                        <span v-if="!useIconsOnly">{{nextBtnLbl}}<span aria-hidden="true">&raquo;</span></span>
                    </a>
                </li>
                <li v-if="!nextAndPreviousOnly" :class="{'disabled': _currentPage == pageCount}">
                    <a 
                        href="javascript:void(0)"
                        class="flex items-center justify-center px-3 h-8 ms-0 leading-tight border border-border-color rounded-e-lg 
                            nav-last dark:border-border-color-dark"
                        :class="(_currentPage == pageCount ?'disabled cursor-not-allowed text-text-color-400 dark:text-text-color-400-dark' : 'hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark')"
                        @click.prevent="_currentPage == pageCount ? null : lastClicked()" 
                        :aria-label="LastBtnLbl">
                        <span v-if="useIconsOnly">
                            <IrisIcon 
                                name="forward-step" 
                                :class="'iris-icon ' + (_currentPage == pageCount? 'text-text-color-400 dark:text-text-color-400-dark' : 'text-primary hover:text-primary-hover')" 
                            >
                            </IrisIcon>
                        </span>
                        <span v-if="!useIconsOnly">
                            {{LastBtnLbl}}<span aria-hidden="true">&raquo;</span>
                        </span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</template>