@use "sass:math";
@use 'sass:color';
 
@import 'mixins.scss';

// Usage example
:root {
  $text-color: #400240;
  $bg-color: #ffffff;
  $nav-bg-color: #b776db;
  $primary-color: #289202;
  $secondary-color: #ffb3eb;
  $header-bg-color: #223a22;

  --mag-nav-text-color: #000000;
  --mag-page-text-color: #{$text-color};
  --mag-nav-bg-color: #{$nav-bg-color};
  --mag-page-bg-color: #{$bg-color};
  --mag-primary-color: #{$primary-color};
  --mag-secondary-color: #{$secondary-color};
  --mag-header-bg-color: #{$header-bg-color};

  --mag-primary-font: 'Lobster';
  --mag-heading-font: 'GravitasOne';
  --mag-page-width: 1320px;
  --mag-box-shadow: 0 .375rem .75rem rgba(140,152,164,.075);
  --mag-border-radius: 0.5rem;

  @include lighten-darken-color($text-color, "mag-base", -20);
  @include lighten-darken-color($text-color, "mag-base-100", 15);
  --mag-base-200: #{$text-color};
  @include lighten-darken-color($text-color, "mag-base-300", 35);
  @include lighten-darken-color($text-color, "mag-base-400", 48);
  @include lighten-darken-color($text-color, "mag-base-500", 95);

  @include calculate-color-shade($bg-color, "mag-page-bg-100", 4, "lighten");
  @include calculate-color-shade($bg-color, "mag-page-bg-200", 7);
  @include calculate-color-shade($bg-color, "mag-page-bg-300", 12);
  @include calculate-color-shade($bg-color, "mag-element-border-color", 10);
  @include calculate-color-shade($bg-color, "mag-element-border-muted-color", 13);

  @include calculate-color-shade($header-bg-color,"mag-header-text-color", 70);
  @include calculate-color-shade($header-bg-color, "mag-header-bg-200", 7);
  @include calculate-color-shade($header-bg-color, "mag-header-bg-300", 12);
   
  @include calculate-color-shade($nav-bg-color, "mag-nav-hover-bg-color", 15);
  @include calculate-color-shade($nav-bg-color,"mag-nav-active-bg-color", 10);

  @include calculate-color-shade($primary-color,"mag-primary-text-color", 90);
  @include calc-primary-hover-color($primary-color,"mag-primary-hover-color");

  @include calculate-color-shade($primary-color, "mag-element-border-focus-color", 10);

  @include calculate-color-shade($secondary-color, "mag-secondary-text-color", 90);
  @include calculate-color-shade($secondary-color, "mag-secondary-hover-color", 15);
  @include calculate-color-shade($secondary-color, "mag-secondary-border-color", 20);
  
}