import { nextTick } from 'vue'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import Toast from '@/shared/components/general-controls/IrisToast.vue';

// Mock the IrisIcon component
vi.mock('@/shared/components/general-controls/IrisIcon.vue', () => ({
  default: {
    name: 'IrisIcon',
    props: ['name', 'class'],
    template: '<div class="iris-icon" :data-icon="name"></div>'
  }
}))

describe.skip('Toast Component', () => {
  let wrapper: VueWrapper<any>

  // Clean up timers after each test
  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllTimers()
  })

  beforeEach(() => {
    vi.useFakeTimers()
  })

  describe.skip('Props and Default Values', () => {
    it('should render with default props', () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message'
        }
      })

      expect(wrapper.props().type).toBe('default')
      expect(wrapper.props().closable).toBe(true)
      expect(wrapper.props().duration).toBe(0)
      expect(wrapper.props().position).toBe('top-right')
      expect(wrapper.props().show).toBe(false)
      expect(wrapper.props().iconName).toBe(null)
    })

    it('should generate unique ID when not provided', () => {
      const wrapper1 = mount(Toast, { props: { message: 'Test 1' } })
      const wrapper2 = mount(Toast, { props: { message: 'Test 2' } })

      expect(wrapper1.props().id).toMatch(/^flowbite-toast-/)
      expect(wrapper2.props().id).toMatch(/^flowbite-toast-/)
      expect(wrapper1.props().id).not.toBe(wrapper2.props().id)

      wrapper1.unmount()
      wrapper2.unmount()
    })

    it('should use custom ID when provided', () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          id: 'custom-toast-id'
        }
      })

      expect(wrapper.props().id).toBe('custom-toast-id')
    })
  })

  describe.skip('Visibility Control', () => {
    it('should not be visible by default', () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message'
        }
      })

      expect(wrapper.find('[role="alert"]').exists()).toBe(false)
    })

    it('should be visible when show prop is true', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      expect(wrapper.find('[role="alert"]').exists()).toBe(true)
    })

    it('should toggle visibility when show prop changes', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: false
        }
      })

      expect(wrapper.find('[role="alert"]').exists()).toBe(false)

      await wrapper.setProps({ show: true })
      expect(wrapper.find('[role="alert"]').exists()).toBe(true)

      await wrapper.setProps({ show: false })
      expect(wrapper.find('[role="alert"]').exists()).toBe(false)
    })
  })

  describe.skip('Message Display', () => {
    it('should display the provided message', async () => {
      const message = 'This is a test toast message'
      wrapper = mount(Toast, {
        props: {
          message,
          show: true
        }
      })

      await nextTick()
      expect(wrapper.text()).toContain(message)
    })
  })

  describe.skip('Icon Display', () => {
    it('should display icon when iconName is provided', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          iconName: 'check'
        }
      })

      await nextTick()
      const icons = wrapper.findAll('.iris-icon')
      expect(icons.length).toBeGreaterThan(0)
      expect(icons[0].attributes('data-icon')).toBe('check')
    })
  })

  describe.skip('Close Button', () => {
    it('should show close button by default', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      expect(wrapper.find('button').exists()).toBe(true)
    })

    it('should hide close button when closable is false', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          closable: false
        }
      })

      await nextTick()
      expect(wrapper.find('button').exists()).toBe(false)
    })

    it('should dismiss toast when close button is clicked', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const closeButton = wrapper.find('button')
      expect(closeButton.exists()).toBe(true)

      await closeButton.trigger('click')
      expect(wrapper.emitted('update:show')).toBeTruthy()
      expect(wrapper.emitted('update:show')[0]).toEqual([false])
    })
  })

  describe.skip('Auto-close Functionality', () => {
    it('should not auto-close when duration is 0', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          duration: 0
        }
      })

      await nextTick()
      vi.advanceTimersByTime(5000)
      await nextTick()

      expect(wrapper.find('[role="alert"]').exists()).toBe(true)
      expect(wrapper.emitted('update:show')).toBeFalsy()
    })

    it('should auto-close after specified duration', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          duration: 3000
        }
      })

      await nextTick()
      expect(wrapper.find('[role="alert"]').exists()).toBe(true)

      vi.advanceTimersByTime(2999)
      await nextTick()
      expect(wrapper.emitted('update:show')).toBeFalsy()

      vi.advanceTimersByTime(1)
      await nextTick()
      expect(wrapper.emitted('update:show')).toBeTruthy()
      expect(wrapper.emitted('update:show')[0]).toEqual([false])
    })

    it('should restart auto-close timer when show prop becomes true', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: false,
          duration: 2000
        }
      })

      await wrapper.setProps({ show: true })
      vi.advanceTimersByTime(1000)

      await wrapper.setProps({ show: false })
      await wrapper.setProps({ show: true })

      // Should not have auto-closed yet since timer restarted
      vi.advanceTimersByTime(1000)
      await nextTick()
      expect(wrapper.emitted('update:show')).toBeFalsy()

      // Should auto-close after full duration from restart
      vi.advanceTimersByTime(1000)
      await nextTick()
      expect(wrapper.emitted('update:show')).toBeTruthy()
    })
  })

  describe.skip('Events', () => {
    it('should emit update:show when internal visibility changes', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const closeButton = wrapper.find('button')
      await closeButton.trigger('click')

      expect(wrapper.emitted('update:show')).toBeTruthy()
      expect(wrapper.emitted('update:show')[0]).toEqual([false])
    })

    it('should emit closed event when toast hides', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const closeButton = wrapper.find('button')
      await closeButton.trigger('click')

      expect(wrapper.emitted('closed')).toBeTruthy()
      expect(wrapper.emitted('closed').length).toBe(1)
    })

    it('should emit closed event on auto-close', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          duration: 1000
        }
      })

      await nextTick()
      vi.advanceTimersByTime(1000)
      await nextTick()

      expect(wrapper.emitted('closed')).toBeTruthy()
    })
  })

  describe.skip('Position Classes', () => {
    const positionTests = [
      { position: 'top-right', expectedClasses: ['fixed', 'z-50', 'top-5', 'right-5'] },
      { position: 'top-left', expectedClasses: ['fixed', 'z-50', 'top-5', 'left-5'] },
      { position: 'bottom-right', expectedClasses: ['fixed', 'z-50', 'bottom-5', 'right-5'] },
      { position: 'bottom-left', expectedClasses: ['fixed', 'z-50', 'bottom-5', 'left-5'] },
      { position: 'top-center', expectedClasses: ['fixed', 'z-50', 'top-5', 'left-1/2', '-translate-x-1/2'] },
      { position: 'bottom-center', expectedClasses: ['fixed', 'z-50', 'bottom-5', 'left-1/2', '-translate-x-1/2'] },
    ]

    positionTests.forEach(({ position, expectedClasses }) => {
      it(`should apply correct classes for ${position} position`, async () => {
        wrapper = mount(Toast, {
          props: {
            message: 'Test message',
            show: true,
            position
          }
        })

        await nextTick()
        const toastElement = wrapper.find('[role="alert"]')
        expectedClasses.forEach(className => {
          expect(toastElement.classes()).toContain(className)
        })
      })
    })

    it('should not apply position classes for static position', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          position: 'static'
        }
      })

      await nextTick()
      const toastElement = wrapper.find('[role="alert"]')
      expect(toastElement.classes()).not.toContain('fixed')
      expect(toastElement.classes()).not.toContain('z-50')
    })
  })

  describe.skip('Accessibility', () => {
    it('should have proper ARIA attributes', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          id: 'test-toast'
        }
      })

      await nextTick()
      const toastElement = wrapper.find('[role="alert"]')
      expect(toastElement.exists()).toBe(true)
      expect(toastElement.attributes('id')).toBe('test-toast')
    })

    it('should have proper close button accessibility', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const closeButton = wrapper.find('button')
      expect(closeButton.attributes('aria-label')).toBe('Close toast')
      expect(closeButton.find('.sr-only').text()).toBe('Close')
    })
  })

  describe.skip('Component Cleanup', () => {
    it('should clear timer on unmount', async () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
      
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          duration: 5000
        }
      })

      await nextTick()
      wrapper.unmount()

      expect(clearTimeoutSpy).toHaveBeenCalled()
    })
  })

  describe.skip('CSS Classes', () => {
    it('should apply base toast classes', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const toastElement = wrapper.find('[role="alert"]')
      const expectedBaseClasses = ['flex', 'items-center', 'w-full', 'max-w-xs', 'p-4', 'bg-bg-color-200', 'rounded', 'shadow-lg']
      
      expectedBaseClasses.forEach(className => {
        expect(toastElement.classes()).toContain(className)
      })
    })

    it('should apply correct text positioning with icon', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true,
          iconName: 'check'
        }
      })

      await nextTick()
      const messageElement = wrapper.find('.text-text-color')
      expect(messageElement.classes()).toContain('ms-3')
      expect(messageElement.classes()).not.toContain('w-full')
    })

    it('should apply full width to text when no icon', async () => {
      wrapper = mount(Toast, {
        props: {
          message: 'Test message',
          show: true
        }
      })

      await nextTick()
      const messageElement = wrapper.find('.text-text-color')
      expect(messageElement.classes()).toContain('w-full')
      expect(messageElement.classes()).not.toContain('ms-3')
    })
  })
})