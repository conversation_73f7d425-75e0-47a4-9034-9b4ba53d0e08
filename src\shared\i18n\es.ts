const messages = {
    invalidCurrency: "Código de moneda no válido: {currencyCode}",
    endpointRequired: "Se requiere un punto final",
    queryRequired: "Se requiere una consulta",
    idRequired: "Se requiere una identificación",
    entityNameRequired: "Se requiere el nombre de la entidad",
    dataRequired: "Se requieren datos",
    invalidTime: "Formato de hora no válido",
    pingFailed: "¡Error de ping!",
    invalidTimeout: "Tiempo de espera no válido",
    createUserSessionFailed: "No se pudo crear la sesión de usuario",
    isRequired: "{label} es requerido",
    notKnownProp: "no es una propiedad conocida",
    invalidProp: "Valor no válido para la propiedad [{prop}]",
    invalidLookupField: "Campo de búsqueda no válido [{field}]",
    invalidMaxLength: "La longitud máxima para la propiedad [{prop}] es de {length} caracteres",
    pageTitleRequired: "Se requiere título de la página",
    pageTitleMaxLength: "La longitud máxima para el título de la página es {maxTitleLength} caracteres",
    invalidItem: "Elemento no válido {item}",
    invalidMaxRange: "El valor máximo debe ser mayor que el valor mínimo.",
    invalidRange: "El valor debe estar entre {min} y {max}.",
    remove: "Eliminar",
    select: "Seleccionar",
    selectAll: "Seleccionar todo",
    close: "Cerrar",
    search: "Buscar",
    showAllResults: 'Mostrar todos los resultados para "{search}"',
    loading: "Cargando...",
    recentRecords: "Registros recientes",
    required: "Requerido",
    duplicateId: "ID duplicada: {id}",
    results: "Resultados",
    all: "Todo",
    getWithData: "El modelo será ignorado con la solicitud GET.",
    singleSelectedColumnOnly: "Solo se puede seleccionar una columna para ordenar.",
    months: "Enero,Febrero,Marzo,Abril,Mayo,Junio,Julio,Agosto,Septiembre,Octubre,Noviembre,Diciembre",
    year: "Año",
    month: "Mes",
    day: "Día",
    selectDate: "Seleccionar fecha",
    error: "Error",
    emailsDisabled: "Todos los correos electrónicos están deshabilitados en este portal. Consulte la documentación para reactivarlos.",
    noDataToDisplay : "No hay datos para mostrar."
}

export default messages