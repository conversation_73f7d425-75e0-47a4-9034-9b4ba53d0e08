import { DateTimeFormat } from '@/shared/services/enums'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import Common from '@/shared/services/common'
import CurrencyHelper from '@/shared/services/currency-helper'
import NumberHelper from '@/shared/services/number-helper'
import DateTimeHelper from '@/shared/services/dateTime-helper'

export default class ModelBinder {
  currencyHelper = new CurrencyHelper()
  numberHelper = new NumberHelper()
  dateTimeHelper = new DateTimeHelper()

  formatData(value: any, fieldMetadata: any, record: any = null, localizeDateTime: boolean = true, isDayJsFormat: boolean = false): string {
    if (!value || !fieldMetadata)
      return value

    const type = fieldMetadata.FieldType
    var result = String(value)

    switch(type) {
      case 'Password':
      case 'Secret':
        const len = (result.length < 3 || result.length > 30) ? 15 : result.length
        result = "*".repeat(len)
        break
      case 'Picklist':
      case 'GlobalPicklist':
        const entries: any[] = fieldMetadata.PicklistEntries
        const item = entries.find(x => x.Value == result)
        result = item ? item.Label : result
        break
      case 'Currency':
      case 'Number':
      case 'AutoNumber':
      case 'Percent':
        var num: number = typeof value === 'string' ? Number(value) : value

        if (!Common.isNumber(num, true))
          return value

        if (type == 'Currency') {
          const systemInfo = useIrisSystemInfoStore().getData()
          // For now this will work only for Native and Salesforce. Need to implement Dynamics later.
          const currencyCode = record && record.CurrencyIsoCode ? record.CurrencyIsoCode : systemInfo.company.CurrencyIsoCode
          result = this.currencyHelper.formatCurrency(num, currencyCode)
        }
        else if (fieldMetadata.DisplayFormatString)
          result = Common.formatAutoNumber(num, fieldMetadata.DisplayFormatString)
        else if (fieldMetadata.IsFileSize)
          result = Common.formatFileSize(num)
        else {
          const isPercentage = type == 'Percent'
          num = isPercentage ? num / 100 : num
          const digits = fieldMetadata.Precision
          result = this.numberHelper.formatNumber(num, { 
            isPercentage,
            minimumFractionDigits: digits, 
            maximumFractionDigits: digits
          })
        }
        break
      case 'Date':
      case 'DateTime':
        var parsedDateTime = value instanceof Date ? value : new Date(value)
        const isValidDateTime = !isNaN(parsedDateTime.getDate())

        if (isValidDateTime) {
          if (localizeDateTime)
            parsedDateTime = this.dateTimeHelper.convertToLocal(parsedDateTime)

          if (type == 'DateTime') {
            // DateTime
            if (!fieldMetadata.DisplayFormatString && !fieldMetadata.TimeDisplayOption)
              result = this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.shortDateTime)
            else if (!fieldMetadata.DisplayFormatString && fieldMetadata.TimeDisplayOption) {
              if (fieldMetadata.TimeDisplayOption == 'Seconds')
                result = this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.shortDate) + ', ' + this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.longTime)
              else if (fieldMetadata.TimeDisplayOption == 'Milliseconds')
                result = this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.shortDate) + ', ' + this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.longTimeMs)
              else
                result = this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.shortDateTime)
            }
            else {
              const format = isDayJsFormat ? fieldMetadata.DisplayFormatString : this.dateTimeHelper.convertDotNetDateTimeFormatToDayJs(fieldMetadata.DisplayFormatString, true)
              result = this.dateTimeHelper.customFormatDateTime(parsedDateTime, format)
            }
          } else {
            // Date
            if (fieldMetadata.DisplayFormatString) {
              const format = isDayJsFormat ? fieldMetadata.DisplayFormatString : this.dateTimeHelper.convertDotNetDateTimeFormatToDayJs(fieldMetadata.DisplayFormatString, true)
              result = this.dateTimeHelper.customFormatDateTime(parsedDateTime, format)
            } else {
              result = this.dateTimeHelper.formatDateTime(parsedDateTime, DateTimeFormat.shortDate)
            }
          }
        }

        break
      case 'Time':
        var parsedTime = value instanceof Date ? value : new Date(value)
        const isValidTime = !isNaN(parsedTime.getTime())

        if (isValidTime) {
          if (localizeDateTime)
            parsedTime = this.dateTimeHelper.convertToLocal(parsedTime)

          if (fieldMetadata.DisplayFormatString) {
            const format = isDayJsFormat ? fieldMetadata.DisplayFormatString : this.dateTimeHelper.convertDotNetDateTimeFormatToDayJs(fieldMetadata.DisplayFormatString, true)
            result = this.dateTimeHelper.customFormatDateTime(parsedTime, format)
          }
          else if (fieldMetadata.TimeDisplayOption == 'Seconds')
            result = this.dateTimeHelper.formatDateTime(parsedTime, DateTimeFormat.longTime)
          else if (fieldMetadata.TimeDisplayOption == 'Milliseconds')
            result = this.dateTimeHelper.formatDateTime(parsedTime, DateTimeFormat.longTimeMs)
          else
            result = this.dateTimeHelper.formatDateTime(parsedTime, DateTimeFormat.shortTime)
        }
        
        break
      default:
        result = fieldMetadata.DisplayFormatString ? null : value
        break;
    }

    return result
  }
}
