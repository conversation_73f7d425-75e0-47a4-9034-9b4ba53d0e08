<script setup lang="ts">
import { ref } from 'vue'
import { CheckboxOutputModeEnum, FormControlModeEnum } from '@/shared/services/form-control-enums'

const props = defineProps({
    fields: {
        type: Object,
        default: {}
    },
    refs: {
        type: Object,
        default: {}
    },
    methods: {
        type: Object,
        default: {}
    }
})

const _value = ref(props.refs._value)
const _required = ref(props.refs._required)
const _readOnly = ref(props.refs._readOnly)
const _mode = ref(props.refs._mode)

function getProperClass(): string {
    const result = props.fields.outputMode == CheckboxOutputModeEnum.toggle ? 'sr-only peer' : 'iris-checkbox'
    return result
}
</script>

<template>
    <input
        type="checkbox"
        v-model="_value"
        class="iris-checkbox"
        :class="[{ 'is-invalid': !methods.isValid() }, getProperClass()]"
        :id="fields.id"
        :disabled="methods.isDisabled()"
        :required="_required"
        :readonly="_readOnly"
        :tabindex="_mode == FormControlModeEnum.edit ? fields.tabIndex : undefined" />
</template>