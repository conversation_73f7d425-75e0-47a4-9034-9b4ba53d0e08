<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iris</title>
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/66df38460e.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://kit.fontawesome.com/66df38460e.css" crossorigin="anonymous"/>
    <link href="https://devcdn.magentrix.com/fonts/css/abrilfatface.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/bevan.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/cabin.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/ekmukta.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/gravitasone.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/lato.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/lobster.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/mavenpro.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/merriweather.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/montserrat.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/nunito.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/oldstandardtt.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/opensans.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/oswald.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/playfairdisplay.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/ptsans.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/ptserif.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/raleway.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/roboto.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/robotocondensed.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/sanchez.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/sourcesanspro.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/vollkorn.css" rel="stylesheet" type="text/css">
    <link href="https://devcdn.magentrix.com/fonts/css/worksans.css" rel="stylesheet" type="text/css">

  </head>
  <body>
    <div id="iris-app"></div>
    <script type="module" src="/src/main.ts"></script>
    <!-- Flowbite Datapicker -->
    <script type="module" src="./node_modules/flowbite-datepicker/dist/js/datepicker-full.min.js"></script>
    <script>
      (() => {
  /**
   * Function to load a CSS file dynamically into the page
   * @param {string} href - The URL of the CSS file
   * @param {string} [id] - Optional ID for the link element
   */
  const loadCSS = (href, id) => {
    if (id && document.getElementById(id)) {
      console.log(`CSS with id "${id}" is already loaded.`)
      return;
    }

    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.type = 'text/css'
    link.href = href

    if (id) {
      link.id = id
    }

    link.onload = () => {
      console.log(`CSS file "${href}" has been loaded successfully.`)
    }

    link.onerror = () => {
      console.error(`Failed to load CSS file "${href}".`)
    }

    document.head.appendChild(link)
  }

  // Usage example
  loadCSS('/src/shared/assets/scss/_variables.css', 'exampleStyles')
})()

    </script>
  </body>
</html>
