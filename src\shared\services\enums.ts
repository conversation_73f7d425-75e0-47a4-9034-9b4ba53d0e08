export enum RequestMethod {
    get = 'GET',
    post = 'POST',
    put = 'PUT',
    patch = 'PATCH',
    delete = 'DELETE'
}

export enum ContentType {
    json = 'application/json',
    form_url_encoded = 'application/x-www-form-urlencoded'
}

export enum DayJsUnit {
    day = 'd',
    week = 'w',
    month = 'M',
    quarter = 'Q',
    year = 'y',
    hour = 'h',
    minute = 'm',
    second = 's',
    millisecond = 'ms'
}

export enum DateTimeFormat {
    shortDate = 'shortDate',
    longDate = 'longDate',
    shortTime = 'shortTime',
    longTime = 'longTime',
    longTimeMs = 'longTimeMs',
    shortDateTime = 'shortDateTime',
    longDateTime = 'longDateTime',
    longDateTimeMs = 'longDateTimeMs'
}

export enum DateTimeStyle {
    full = 'full',
    long = 'long',
    medium = 'medium',
    short = 'short'
}

export enum CurrencySymbolFormat {
    symbol = 'symbol',
    code = 'code'
}

export enum LogLevel {
    log,
    information,
    debug,
    warning,
    error,
    trace
}

export enum WebSocketMessageType {
    HandShake,
    Json
}

export enum WebSocketClientType {
    Server,
    Client
}

export enum WebSocketChannel {
    IrisCore,
    Mingle,
    Rewards,
    PopupCampaign
}
