<script setup lang="ts">
import { onMounted, ref, watch, onUnmounted, getCurrentInstance } from 'vue'
import { FormControlModeEnum, CheckboxOutputModeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { constants } from '@/shared/services/constants'
import { useDependentFieldStore } from '@/shared/stores/dependent-field-store'
import Common from '@/shared/services/common'
import IrisCheckboxHelper from '@/shared/components/helpers/IrisCheckboxHelper.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

// Props
const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    readOnly: Boolean,
    value: [<PERSON>olean, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    outputMode: {
        type: String,
        default: CheckboxOutputModeEnum.checkbox,
        validator: (value: string) => {
            return (<any>Object).values(CheckboxOutputModeEnum).includes(value)
        }
    },
    visible: {
        type: Boolean,
        default: true
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: boolean) => true,
    'update:value': (value: boolean) => true
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const depedentRegistry = useDependentFieldStore()
let subscriptionId = ''

// Refs
const $el = ref<HTMLElement>()
const _value = ref(base.getValue())
const _required = ref(props.required)
const _readOnly = ref(props.readOnly)
const _label = ref(props.label)
const _mode = ref(props.mode)

// Vars
const refs = { _value, _required, _readOnly, _mode }
const methods = { isDisabled, isValid: base.isValid }

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())

    const formId = base.getParentFormId($el.value!)
    store.add(base.uniqueId, formId, base.getErrors, base.addDatabaseErrors, validate)
    initialize(false)

    if (base.isBound()) {
        // register component for depedency checking
        subscriptionId = depedentRegistry.registerComponent({ 
            field: props.field!,
            parentField: base.fieldMetadata.value.ControllerName,
            entity: (props.value! as any).__Metadata().Name,
            formId: formId!,
            invalidate: () => { }
        })
    }
})

onUnmounted(() => {
    store.remove(base.uniqueId)
    depedentRegistry.removeComponent(subscriptionId)
})

// Watches
watch(_value, () => {
    if (validate()) {
        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value
    
        emits('onChange', _value.value)
        emits('update:value', _value.value)
        depedentRegistry.dispatch(subscriptionId, _value.value)
    }
})

watch(() => [
    props.value, 
    props.field, 
    props.required, 
    props.readOnly, 
    props.label, 
    props.mode
], () => initialize(true))

// Functions
function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation)
        validate()
}

function isDisabled(): boolean {
    let result = _mode.value == FormControlModeEnum.disabled
    result = result || (_readOnly.value && props.outputMode == CheckboxOutputModeEnum.toggle)
    return result
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _readOnly.value = props.readOnly
        _label.value = props.label
        _mode.value = props.mode
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(addErrors: boolean = true): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true
    
    var errors: ModelError[] = []

    if (_required.value && !_value.value) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value ?? props.id })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    if (addErrors)
        base.addErrors(errors)

    return result
}
</script>

<template>
    <div ref="$el" v-if="visible">
        <div v-if="!base.isBound() || base.fieldMetadata.value.IsReadable">
            <!-- Text mode -->
            <template v-if="_mode == FormControlModeEnum.text">{{ Common.toTitleCase(_value?.toString()) }}</template>
            <template v-else-if="_mode == FormControlModeEnum.html">
                <div class="flex items-center">
                    <IrisIcon name="square" :id="id" v-if="_value == false || _value == null" class="iris-icon"></IrisIcon>
                    <IrisIcon name="square-check" :id="id" v-if="_value == true" class="iris-icon"></IrisIcon>
                    <label v-if="_label && showLabel" class="iris-label mt-2 ms-2"> {{ _label }} </label>
                </div>
            </template>
            <!-- Not Text mode -->
            <template v-else>
                <!-- <label class="iris-label-inline me-2 hidden md:block">&nbsp;</label> -->
                <!-- Output mode = checkbox  -->
                <template v-if="(_readOnly && outputMode == CheckboxOutputModeEnum.checkbox)">
                    <span class="checkbox-read-only">
                        <IrisIcon name="square-check" v-if="_value" class="iris-icon"></IrisIcon>
                        <IrisIcon name="square" v-if="!_value" class="iris-icon"></IrisIcon>
                    </span>
                    <span v-if="showLabel">{{ _label }}</span>
                </template>
    
                <!-- Any other mode -->
                <template v-else>
                    <template v-if="outputMode == CheckboxOutputModeEnum.toggle">
                        <label :for="id" class="inline-flex items-start iris-toggle cursor-pointer">
                            <IrisCheckboxHelper :fields="props" :refs="refs" :methods="methods"></IrisCheckboxHelper>
                            <div v-if="isDisabled()" class="relative min-w-[2.75rem] w-11 h-6 bg-bg-color-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-border-color after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-bg-color after:border-border-color after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:bg-bg-color-200-dark dark:peer-checked:after:border-border-color-dark dark:after:bg-bg-color-dark dark:after:border-border-color-dark flex-shrink-0"></div>
                            <div v-else class="relative min-w-[2.75rem] w-11 h-6 bg-bg-color-200 peer-focus:outline-none peer-focus:ring-1 peer-focus:ring-border-focus rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-border-color after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-bg-color after:border-border-color after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-bg-color-200-dark dark:peer-checked:after:border-border-color-dark dark:after:bg-bg-color-dark dark:after:border-border-color-dark flex-shrink-0"></div>
                            <template v-if="showLabel">
                                <div
                                    class="ms-3 text-sm font-medium text-text-color-200 dark:text-text-color-200-dark flex-grow"
                                    :class="{ 'cursor-pointer': _mode == FormControlModeEnum.edit }">

                                    <div class="break-words">{{ _label }}</div>
                                        <!-- Hint -->
                                    <template v-if="$slots.hint">
                                        <slot name="hint"></slot>
                                    </template>
                                    <template v-else-if="hint">
                                        <div class="iris-input-hint">{{ hint }}</div>
                                    </template>
                                </div>
                                <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
                            </template>
                        </label>
                    </template>
                    
                    <template v-else>
                        <IrisCheckboxHelper :fields="props" :refs="refs" :methods="methods"></IrisCheckboxHelper>
                        <template v-if="showLabel">
                            <label
                                class="iris-checkbox-label"
                                :for="id"
                                :class="{ 'cursor-pointer': _mode == FormControlModeEnum.edit }">
                                {{ _label }}
                                <span class="inline iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
                            </label>
                        </template>
                    </template>
                </template>
    
                <!-- Hint -->
                <div class="iris-input-hint" v-if="hint && outputMode !== CheckboxOutputModeEnum.toggle">{{ hint }}</div>
    
                <!-- Errors -->
                <div v-if="_mode == FormControlModeEnum.edit" class="text-red-600 text-xs mt-1" v-for="error in base.getErrors()">{{ error.message }}</div>
            </template>
        </div>
    </div>
</template>

<style scoped>
.checkbox-read-only {
    position: relative;
    top: .12rem;
    font-size: 1.2rem;
    margin-right: 0.25rem;
}

.iris-toggle {
    max-width: 100%;
    gap: 0.25rem;
}
</style>