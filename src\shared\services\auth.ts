import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import DataAccess from '@/shared/services/data-access'

export default class Auth {
    static sessionInfo: any

    static async createSession(refreshToken: string): Promise<any> {
        const dataAccess = new DataAccess()
        const url = `/api/${dataAccess.restVersion}/token`
        const body = { grant_type: 'refresh_token', refresh_token: refreshToken }

        this.sessionInfo = await dataAccess.postData(url, body)

        return this.sessionInfo
    }

    static isValidSession(): boolean {
        const systemInfo = useIrisSystemInfoStore().getData()
        
        if (systemInfo.env.production)
            return true

        if (!this.sessionInfo)
            return false

        const validUntil = Date.parse(this.sessionInfo.validUntil)
        const now = Date.parse((new Date()).toUTCString())
        const result = validUntil > now

        return result
    }

    static getAntiForgeryToken(): string {
        let token = '5C516A24-E7EE-45C9-A91E-3851D7F093DB'
        const systemInfo = useIrisSystemInfoStore().getData()

        if (systemInfo.env.production)
            token = document.getElementById('xsrf-token')?.innerText ?? ''
        
        return token
    }
}