const messages = {
  partnerProgram: "Programme Partenaire",
  partnerManagement: "Gestion des Partenaires",
  helponthispage: "Aide sur cette page",
  search: "Recherche",
  programs: "Programmes",
  account: "Compte",
  partnerPrograms: "Programmes Partenaires",
  serverError: "Une erreur est survenue !",
  loading: "Chargement...",
  yes: "Oui",
  no: "Non",
  benefits: "Avantages",
  programBenefits: "Avantages du Programme",
  newProgram: "Nouveau Programme",
  programTiers: "Niveaux du Programme",
  dragDropMessage: "Glissez et déposez les niveaux pour les réorganiser",
  tier: "Niveau",
  remove: "Supprimer",
  addTier: "Ajouter un Niveau",
  editTier: "Modifier le Niveau",
  tierBadge: "Badge du Niveau",
  tierDescription: "Description du Niveau",
  tierName: "Nom du Niveau",
  deleteTier: "Supprimer le Niveau",
  noRecords: "Aucune donnée à afficher.",
  newBenefit: "Nouvel Avantage",
  cancel: "Annuler",
  close: "Fermer",
  addBenefit: "Ajouter un Avantage",
  saving: "Enregistrement...",
  existingBenefitName: "Un avantage avec ce nom existe déjà. Veuillez en choisir un autre.",
  highestTierReached: "Vous êtes au niveau le plus élevé !",
  failFetchData: "Erreur : Impossible de récupérer les données. Contactez votre administrateur.",
  failInitialize: "Échec de l'initialisation des composants",
  failSave: "Échec de l'enregistrement. Veuillez réessayer ou contacter votre administrateur.",
  benefitDuplicateNameError: "Un avantage avec ce nom existe déjà. Veuillez en choisir un autre.",
  save: "Enregistrer",
  deleteBenefit: "Supprimer l'Avantage",
  deleteBenefitMessage: "Voulez-vous vraiment supprimer cet avantage et toutes ses affectations ?",
  delete: "Supprimer",
  failDelete: "Échec de la suppression. Veuillez réessayer ou contacter votre administrateur.",
  edit: "Modifier",
  first: "Premier",
  last: "Dernier",
  next: "Suivant",
  previous: "Précédent",
  editBenefit: "Modifier l'Avantage",
  noProgramsAvailable: "Aucun programme disponible.",
  enrollments: "Inscriptions",
  partnerEnrollments: "Inscriptions des Partenaires",
  newEnrollment: "Nouvelle Inscription",
  allPrograms: "Tous les Programmes",
  noTiers: "Aucun niveau disponible pour le programme sélectionné. Veuillez sélectionner un autre programme ou contacter votre administrateur.",
  editEnrollment: "Modifier l'Inscription",
  addEnrollment: "Ajouter une Inscription",
  deleteEnrollmentMessage: "Voulez-vous vraiment supprimer cette inscription et toutes les informations associées ?",
  deleteEnrollment: "Supprimer l'Inscription",
  duplicateEnrollment: "Ce partenaire est déjà inscrit activement à ce programme.",
  noAccountsAvailable: "Aucun compte disponible.",
  deleteProgramMessage: "Vous êtes sur le point de supprimer ce programme et toutes ses données, y compris les niveaux, les inscriptions et les critères. Cette action est irréversible.",
  deleteProgram: "Supprimer le Programme Partenaire",
  setting: "Paramètre",
  settings: "Paramètres",
  partnerProgramStatus: "Statut du Programme Partenaire",
  enablePartnerProgram: "Activer le Programme Partenaire",
  partnerProgramIsDisabled: "Le Programme Partenaire est désactivé",
  partnerProgramIsActive: "Le Programme Partenaire est actif",
  confirmationDisablingPartnerProgram: "Voulez-vous vraiment désactiver le Programme Partenaire ?",
  confirmationDisablingPartnerProgramMessage: "La visibilité des niveaux et des avantages sera masquée.",
  ok: "OK",
  settingsSaved: "Paramètres enregistrés avec succès !",
  back: "Retour",
  benefit: "Avantage du Programme Partenaire",
  benefitTypeError: "Le type d’avantage est requis.",
  benefitNameRequired: "Le nom de l’avantage est requis.",
  enrollmentDateMustBeBeforeExpiration: "La date d’inscription doit être antérieure à la date d’expiration.",
  editProgram: "Modifier le Programme",
  addProgram: "Ajouter un Programme",
  programNameRequired: "Le nom du programme est requis.",
  tierNameRequired: "Le nom du niveau est requis.",
  tierInexNameRequired: "Niveau {idx} : le nom est requis.",
  duplicateTierName: "Nom de niveau en double",
  unsavedChangesConfimation: "Vous avez des modifications non enregistrées. Voulez-vous vraiment fermer ?",
  minTierError: "Vous devez configurer au moins un niveau.",
  maxTierError: "Vous pouvez configurer au maximum six niveaux.",
  duplicateIndexTierName: "Niveau {idx} : nom en double {tierName}.",
  missingProgramId: "Programme non valide",
  program: "Programme",
  newTier: "Nouveau Niveau",
  clone: "Cloner",
  configure: "Configurer",
  noTiersDefined: "Aucun niveau défini.",
  criteria: "Critères du Programme",
  noCriteriaDefined: "Aucun critère défini.",
  addCriterion: "Ajouter un Critère",
  noBenefitDefined: "Aucun avantage défini.",
  atLeastOneTierRequired: "Saisissez au moins un niveau pour le programme",
  confirm: "Confirmer",
  noMoreAvailableBenefits: "Aucun autre avantage disponible à ajouter.",
  missingProgramIdOrPartnerAccountId: "Informations manquantes. Contactez l’administration.",
  noProgramEnrollments: "Vous n’êtes inscrit à aucun programme partenaire. Veuillez contacter votre administrateur.",
  noPartnerAccount: "Vous devez être partenaire pour voir cette page.",
  certifications: "Certifications",
  numberofOpportunities: "Nombre d’opportunités",
  opportunityRevenue: "Revenus des opportunités",
  numberofDealRegistrations: "Nombre d'enregistrements d'affaires",
  custom: "Personnalisé",
  configureCriterion: "Configurer le Critère",
  createCriterion: "Créer un Critère",
  create: "Créer",
  criteriaDuplicateNameError: "Un critère avec ce nom existe déjà. Veuillez en choisir un autre.",
  quantity: "Quantité",
  revenue: "Revenu",
  criterionNameRequiredError: "Le nom du critère est requis.",
  criterionTypeRequiredError: "Le type de critère est requis.",
  trainingCourseRequiredError: "Un cours de formation est requis pour les certifications.",
  rollupFieldRequiredError: "Le champ de cumul est requis pour les revenus.",
  attainmentDateFieldRequired: "Le champ de date d’obtention est requis.",
  unitofMeasureRequired: "L’unité de mesure est requise.",
  editCriterion: "Modifier le Critère",
  missingFields: "Il manque des informations",
  trainingCourse: "Cours de Formation",
  name: "Nom",
  type: "Type",
  filterLogic: "Logique de Filtrage (optionnelle)",
  filterLogicPlaceHolder: "ex : (1 et 2) ou 3",
  reset: "Réinitialiser",
  addFilter: "Ajouter un Filtre",
  equals: "égal à",
  notEquals: "différent de",
  includes: "comprend",
  excludes: "exclut",
  greaterThan: "supérieur à",
  lessThan: "inférieur à",
  lessOrEqual: "inférieur ou égal à",
  greaterOrEqual: "supérieur ou égal à",
  contains: "contient",
  notContain: "ne contient pas",
  startWith: "commence par",
  notStartWith: "ne commence pas par",
  partnerAccount: "Compte Partenaire",
  enrollmentDate: "Date d’Inscription",
  expirationDate: "Date d’Expiration"
}

export default messages