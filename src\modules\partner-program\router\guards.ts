import { RouteLocationNormalizedGeneric, type Router } from "vue-router"
import PartnerProgramRoutes from '@/modules/partner-program/router/index'
import DataAccess from '@/shared/services/data-access'

const partnerProgramGuards = async (to: RouteLocationNormalizedGeneric, systemInfo: any, router: Router): Promise<boolean> => {
  async function loadProgramSetting() {
    try {
      const dataAccess = new DataAccess()
      const endpoint = `/partnerprogram/loadsetting`
      const response = await dataAccess.execute(endpoint)

      return response
    } catch {
      return null
    }
  }

  // Partner Program Guards
  const partnerProgramRouteNames = PartnerProgramRoutes.map((x: any) => x.name)
  const destUrl = to.name?.toString() as string

  if (partnerProgramRouteNames.includes(destUrl)) {
    // Check license
    const hasLicense = systemInfo.company.system.licenses.includes('Partner Program')

    if (!hasLicense)
      return false

    if (to.meta.layout == 'Setup') {
      // Check enablement and manage permission
      var result = await loadProgramSetting()

      if (!result)
        return false

      const partnerProgramEnabled = result.setting?.IsEnabled
      const canManagePartnerPrograms = result.canManagePartnerPrograms

      if (!partnerProgramEnabled)
        router.push('/setup/partner-program/settings?disabled=1')

      // Check access to setup views
      //investigate this employee without the flag can see the setup
      const validRoles = systemInfo.userInfo.user.roleType == 'admin' || (systemInfo.userInfo.user.roleType == 'employee' && canManagePartnerPrograms)
  
      if (!validRoles)
        return false
    }
    else {
      // Check access to end-user views
      const invalidRoles = systemInfo.userInfo.user.roleType == 'guest' || systemInfo.userInfo.user.roleType == 'customer'

      if (invalidRoles)
        return false
    }
  }

  return true
}

export default partnerProgramGuards