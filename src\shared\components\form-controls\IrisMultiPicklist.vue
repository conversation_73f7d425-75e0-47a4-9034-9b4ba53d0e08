<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, computed, onUpdated, getCurrentInstance } from 'vue'
import { initTooltips } from 'flowbite'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { useDependentFieldStore } from '@/shared/stores/dependent-field-store'
import { ModelError } from '@/shared/services/model-error'
import { constants } from '@/shared/services/constants'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import { DropDownItem } from '@/shared/services/dropdown-item'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import common from '@/shared/services/common'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    searchPlaceholder: String,
    value: [String, Object],
    items: Array<DropDownItem>,
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    buttonLabel: {
        type: String
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    appendWhenNotFound: {
        type: Boolean,
        default: true
    },
    size: {
        type: String,
        default: 'default'
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => true
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const notiStore = useDependentFieldStore()
const isVisible = ref(true)
const $el = ref<HTMLElement>()
const _value = ref(base.getValue() as string)
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _items = ref(props.items)
const _searchTerm = ref("")
const _buttonLabel = ref("--SELECT--")
const valuesHaveColor = ref(false)
let subscriptionId = ''

let uniqueId: string = "gs" + common.newGuid().replace(/-/g, '')

// computed
const filteredItems = computed(() => {
    const filteredValues = getFilteredValues()

    if (!_searchTerm.value)
        return filteredValues
    
    const escapedSearchTerm = _searchTerm.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const regex = new RegExp(escapedSearchTerm.trim(), 'i')

    return filteredValues.filter(item => regex.test(item.label))
})

const selectedItems = computed(() => {
    return _items.value?.filter(item => item.selected === true);
})

const nonRemovableItems = computed(() => {
    return _items.value?.filter(item => !item.removable)
})

// Computed property to update button padding based on 'size'
const buttonPadding = computed(() => {
    return props.size === 'default' ? 'p-2' : 'p-4'
})

// Hooks
onMounted(() => {
    base.addInputFieldSymbol(getCurrentInstance())

    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)!
        store.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)

        if (base.isBound()) {
            // register picklist
            subscriptionId = notiStore.registerComponent({ 
                field: props.field!,
                parentField: base.fieldMetadata.value.ControllerName,
                entity: (props.value! as any).__Metadata().Name,
                formId,
                invalidate: invalidateState
            })
        }
    }
})

onUnmounted(() => {
    if (isVisible.value) { 
        store.remove(base.uniqueId)
        notiStore.removeComponent(subscriptionId)
    }
})

onUpdated(() => {
    initTooltips()
})

watch(_value, () => {
    
    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)
    }
    
    //call dependency component registry
    notiStore.dispatch(subscriptionId, _value.value)

    if (_mode.value == FormControlModeEnum.edit)
        initTooltips()
})

watch(() => [
    props.value, 
    props.field,
    props.required,
    props.label, 
    props.mode,
    props.hint,
    props.items
], () => initialize(true))

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (_mode.value == FormControlModeEnum.edit) {

        if (performValidation)
            validate()

        initTooltips()
    }
}

function setPropValues() : void {
    _items.value = []
    _value.value = base.getValue()
    valuesHaveColor.value = false

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label
        _buttonLabel.value = props.buttonLabel || `--${base.languageHelper.getMessage('select').toUpperCase()}--`
        
        if (_items.value == null || _items.value.length == 0 && props.items) 
            _items.value = props.items
     
        base.fieldMetadata
            .value
            .PicklistEntries
            .forEach((element: { Label: string; Value: string, Color: string }) => {

                if (element.Color)
                    valuesHaveColor.value = true

                _items.value?.push(new DropDownItem(element.Label, element.Value, element.Color))
            })
        

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
        _items.value = props.items 

        _items.value?.forEach(item => {
            
            if (item.color)
                valuesHaveColor.value = true
        })
    }

    if (_value.value) {
        const selectedValues = _value.value.split(';')
        selectedValues.forEach(selectedValue => {
            const selectedItem = _items.value?.find(f => f.value == selectedValue)

            if (selectedItem)
                selectedItem.selected = true
            else if (props.appendWhenNotFound) {
                const missingItem = new DropDownItem(_value.value, _value.value, '')
                missingItem.selected = true
                _items.value?.push(missingItem)
            }
        })
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))
}

function validate(): boolean {
    if (_mode.value != FormControlModeEnum.edit)
        return true
    
    var errors: ModelError[] = []

    if (_required.value && !_value.value) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    base.addErrors(errors)

    return result
}

function updateValue() : void {
    _value.value = _items.value?.filter(item => item.selected)
        .map(item => item.value)
        .join(';') ?? "";
}

function removeValue(pkValue : string) : void {
    const itemToRemove = _items.value?.find(f => f.value == pkValue)

    if (itemToRemove != null && itemToRemove.removable) {
        itemToRemove.selected = false
        updateValue()
    }
}

function getFilteredValues() : Array<DropDownItem> {
    const model = (props.value! as any)
    const rtFieldName = 'RecordTypeId'
    let finalItems = _items.value

    if (base.isBound()) {
        if (rtFieldName in model) {
            const recordTypeId = model[rtFieldName]

            if (recordTypeId
                && model.__Metadata != null 
                && model.__Metadata.RecordTypes != null 
                && model.__Metadata.RecordTypes.length > 0) {

                const rt = model.__Metadata.RecordTypes.find((f: { Id: any, Name: String, IsActive: Boolean }) => f.Id == recordTypeId)

                if (rt.RecordTypePicklistSettings != null && rt.RecordTypePicklistSettings.length > 0) {
                    const picklistSetting = rt.RecordTypePicklistSettings.find((f: { EntityField: string | undefined }) => f.EntityField == props.field)

                    if (picklistSetting != null && picklistSetting.Values) {
                        const values = removeFirstAndLastLetters(picklistSetting.Values).split(',')
                        finalItems = finalItems?.filter(item => values.includes(item.value))
                    }
                }
            }
        }

        if (base.fieldMetadata.value.ControllerName 
            && base.fieldMetadata.value.DependentPicklists != null) {
                
            const controllerValue = model[base.fieldMetadata.value.ControllerName]

            if (controllerValue) {
                const values = base.fieldMetadata.value.DependentPicklists[controllerValue]

                if (values) {
                    const allowedValues = values.map((f: { Value: string }) => f.Value)
                    finalItems = finalItems?.filter(item => allowedValues.includes(item.value))
                }
            }
            else if (_mode.value != FormControlModeEnum.html && _mode.value != FormControlModeEnum.text)
                _mode.value = FormControlModeEnum.disabled
        }
    }

    return finalItems!
}

function removeFirstAndLastLetters(input: string): string {
    if (input.length <= 2) {
        // If the input has 0 or 1 characters, return an empty string
        return ''; 
    } else {
        // Slice the string to remove the first and last characters
        return input.slice(1, -1); 
    }
}

function invalidateState(value: string) : void {
    if (_mode.value == FormControlModeEnum.html || _mode.value == FormControlModeEnum.text)
        return;

    //parent controling field has requested the state to be reset
    _items.value?.forEach(item => item.selected = false)
    getFilteredValues()
    
    if (value)
        _mode.value = FormControlModeEnum.edit
    else
        _mode.value = FormControlModeEnum.disabled
}

function getLabel() {
    let labels : String[] = []

    selectedItems.value?.forEach(f => { 
       labels.push(f.label)
    })

    return labels.join()
}

function toggleSelection(item: any) {
    if (item.removable)
        item.selected = !item.selected;
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="mode == FormControlModeEnum.text">
            {{ getLabel() }}
        </template>
        <template v-else>
            <label v-if="_label && showLabel" :for="id" class="iris-label inline-flex"> 
                <span>{{ _label }}</span>
                <template v-if="$slots.help">
                    <button :data-popover-target="id+'_helper'" data-popover-placement="right" type="button" class="ml-2 inline-flex">
                        <IrisIcon name="circle-info-solid" width="0.75rem" height="0.75rem"></IrisIcon>
                        <span class="sr-only">Show information</span>
                    </button>
                    <div data-popover :id="id+'_helper'" role="tooltip" class="absolute z-10 invisible inline-block text-sm transition-opacity rounded duration-300 bg-bg-color border border-border-color shadow opacity-0 w-72">
                        <div class="p-3 space-y-2">
                            <slot name="help"></slot>
                        </div>
                        <div data-popper-arrow></div>
                    </div>
                </template>
                <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
            </label>
            <div v-if="mode != FormControlModeEnum.html" class="relative mb-2">
                <button
                    :id="id" 
                    :disabled="_mode == FormControlModeEnum.disabled" 
                    :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined" 
                    :data-dropdown-toggle="id+'dropdown'" 
                    data-dropdown-placement="top" 
                    data-dropdown-offset-distance="5"
                    class="text-text-color-400 bg-bg-color-200 dark:bg-page-200-dark font-medium rounded-lg text-sm w-full text-center inline-flex justify-between items-center border border-border-color"
                    :class="[buttonPadding, {
                        'hover:border-border-focus': _mode != FormControlModeEnum.disabled,
                        'cursor-not-allowed' : _mode == FormControlModeEnum.disabled
                    }]"
                    type="button">
                    {{ _buttonLabel }}
                    <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                    </svg>
                </button>

                <!-- Dropdown menu -->
                <div :id="id+'dropdown'" 
                    data-popper-placement="top" 
                    class="z-10 hidden rounded shadow overflow-hidden border-border-color border w-full bg-bg-color dark:border-border-color-dark dark:bg-bg-color-dark">
                    <div class="p-3">
                        <label :for="uniqueId" class="sr-only">{{ base.languageHelper.getMessage('search') }}</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                <svg class="w-4 h-4 text-text-color-400 dark:text-text-color-400-dark" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input 
                                type="text" 
                                v-model="_searchTerm" 
                                :id="uniqueId" class="block w-full p-2 ps-10 text-sm
                                text-text-color-200 border border-border-color rounded-lg focus:border-border-focus focus:ring-border-focus
                                dark:text-text-color-200-dark dark:border-border-color-dark
                                bg-bg-color-200 dark:bg-bg-color-200" 
                                :placeholder="searchPlaceholder"/>
                        </div>
                    </div>
                    <ul class="h-48 px-3 pb-3 overflow-y-auto text-sm text-text-color-200 dark:text-text-color-200-dark" :aria-labelledby="id">
                        <li v-for="(item, index) in filteredItems">
                            <div class="flex items-center ps-2 rounded hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">
                                <input 
                                    :id="id + '-' + index" 
                                    :checked="item.selected || !item.removable"
                                    @input="toggleSelection(item)"
                                    @change="updateValue" 
                                    type="checkbox" 
                                    :value="item.value"
                                    :disabled="!item.removable"
                                    class="w-4 h-4 text-primary bg-bg-color border-text-color-200 rounded focus:ring-border-focus dark:bbg-bg-color-dark dark:border-text-color-200-dark" 
                                    :class="{ 'cursor-not-allowed opacity-50': !item.removable }"
                                />
                                <label 
                                    :for="id + '-' + index" 
                                    class="w-full py-2 ms-2 text-sm font-medium text-text-color-200 rounded dark:text-text-color-200-dark"
                                    :class="{ 'cursor-not-allowed': !item.removable }"
                                >
                                    {{ item.label }}
                                </label>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
                <div v-if="_mode == FormControlModeEnum.edit" class="text-danger small" v-for="error in base.getErrors()">{{ error.message }}</div>
            </div>
            <div>
                <span :id="id + 'read-only-badge' + index" v-for="(item, index) in nonRemovableItems" :key="index" class="inline-flex items-center px-2 py-1 me-2 mb-2 text-sm font-medium rounded border border-border-color">
                    <span v-if="valuesHaveColor" class="flex w-2.5 h-2.5 rounded-full me-1.5 flex-shrink-0" :style='{ backgroundColor: item.color }'></span>
                    {{ item.label }}
                </span>
                <span :id="id + 'badge' + index" v-for="(item, index) in selectedItems" :key="item.value" class="inline-flex items-center px-2 py-1 me-2 mb-2 text-sm font-medium rounded border border-border-color">
                    <span v-if="valuesHaveColor" class="flex w-2.5 h-2.5 rounded-full me-1.5 flex-shrink-0" :style='{ backgroundColor: item.color }'></span>
                    {{ item.label }}
                    <button v-if="mode == FormControlModeEnum.edit" 
                        type="button" 
                        :data-tooltip-target="`${id}-tooltip-${index}`" 
                        class="inline-flex items-center p-1 ms-2 text-sm text-text-color-400 bg-transparent rounded-sm hover:text-primary dark:text-text-color-400-dark" 
                        :data-dismiss-target="`#${id}badge${index}`" 
                        :aria-label="base.languageHelper.getMessage('remove')"
                        @click="removeValue(item.value)">

                        <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">{{ base.languageHelper.getMessage('remove') }}</span>
                    </button>
                    <div :id="id+'-tooltip-'+index" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                        {{ base.languageHelper.getMessage('remove') }} <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </span>
            </div>
        </template>
    </div>
</template>