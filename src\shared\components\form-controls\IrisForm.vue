<script setup lang="ts">
import { computed } from 'vue';
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    onSubmit: Function
})

defineExpose({ isValidForm })

const emit = defineEmits(['change', 'dirty'])
const store = useIrisFormControlStore()
const errors = computed(() => store.getErrors(props.id))
var dirty = false

function onSubmit() {
    if (props.onSubmit)
        props.onSubmit()
}

function onChange(e: Event) {
    if (!dirty) {
        dirty = true
        emit('dirty')
    }

    emit('change', e, isValidForm())
}

function isValidForm() {
    var result = false
    const form = document.getElementById(props.id) as HTMLFormElement
    
    if (form)
        result = form.checkValidity() && errors.value.length == 0

    return result
}
</script>

<template>
    <form :id="id" :class="{ 'was-validated': errors.length == 0 }" @submit.prevent="onSubmit" @input="onChange($event)">
        <slot></slot>
    </form>
</template>