<script setup lang="ts">
import { ref, computed, provide, onMounted, watch, readonly} from 'vue'

// 1. Props
const props = defineProps({
  defaultOpenId: {
    type: [String, Number, Array] as import('vue').PropType<string | number | (string | number)[]>,
    default: null,
  },
  alwaysOpen: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true
  }
});

// 2. Emits (No emits defined for this component)

// 3. Const, Refs
const openItems = ref<Set<string | number> | string | number | null>(null);
const childSectionIds = ref<Set<string | number>>(new Set()); // To keep track of registered sections

// 4. Lifecycle Hooks
onMounted(function() {
  // Initialize after mount when children have had a chance to register
  initializeOpenItems(); // initializeOpenItems is defined in the Functions section below
});

// 5. Computed
// Generate a unique ID for the accordion if not provided (props.id is preferred)
const uniqueAccordionId = computed(function() {
  return props.id || `iris-accordion-${Math.random().toString(36).slice(2, 9)}`;
});

// 6. Watches
// Re-initialize if props that define default state change
watch(
  function() { return [props.defaultOpenId, props.alwaysOpen]; },
  function() {
    initializeOpenItems(); // initializeOpenItems is defined in the Functions section below
  },
  { deep: true }
);

// 7. Functions

// Initialize or update openItems based on props and available children
function initializeOpenItems() {
  const validDefaultIds = new Set<string | number>();

  if (props.defaultOpenId) {
    const idsToCheck = Array.isArray(props.defaultOpenId) ? props.defaultOpenId : [props.defaultOpenId];
    idsToCheck.forEach(function(id) { // Converted arrow function
      if (childSectionIds.value.has(id)) {
        validDefaultIds.add(id);
      } else {
        console.warn(`IrisAccordion: defaultOpenId "${id}" does not match any registered IrisAccordionSection.`);
      }
    });
  }

  if (props.alwaysOpen) {
    openItems.value = new Set(validDefaultIds);
  } else {
    if (validDefaultIds.size > 0) {
      const firstValidId = Array.from(validDefaultIds)[0];
      if (validDefaultIds.size > 1 && !props.alwaysOpen) {
        console.warn("IrisAccordion: Multiple 'defaultOpenId' values provided in single-open mode. Using the first valid one:", firstValidId);
      }
      openItems.value = firstValidId;
    } else {
      openItems.value = null;
    }
  }
}

// --- Child Registration ---
function registerSection(id: string | number) {
  childSectionIds.value.add(id);
  // Re-initialize if a new child appears that might match defaultOpenId
  // This is more robust if sections are added dynamically.
  initializeOpenItems();
}

function unregisterSection(id: string | number) {
  childSectionIds.value.delete(id);
  // If an open item is removed, handle its state
  if (props.alwaysOpen && openItems.value instanceof Set && openItems.value.has(id)) {
    const newSet = new Set(openItems.value);
    newSet.delete(id);
    openItems.value = newSet;
  } else if (!props.alwaysOpen && openItems.value === id) {
    openItems.value = null;
  }
}

function isSectionOpen(sectionId: string | number): boolean {
  if (!childSectionIds.value.has(sectionId)) return false; // Don't consider non-children

  if (props.alwaysOpen && openItems.value instanceof Set) {
    return openItems.value.has(sectionId);
  }
  return openItems.value === sectionId;
}

function toggleSection(sectionId: string | number) {
  if (!childSectionIds.value.has(sectionId)) {
    return; // Ignore toggles for unregistered sections
  }

  if (props.alwaysOpen) {
    const newOpenItemsSet = new Set(openItems.value as Set<string | number> || []); // Ensure it's a Set

    if (newOpenItemsSet.has(sectionId)) {
      newOpenItemsSet.delete(sectionId);
    } else {
      newOpenItemsSet.add(sectionId);
    }
    openItems.value = newOpenItemsSet;
  } else {
    openItems.value = openItems.value === sectionId ? null : sectionId;
  }
}

// 8. Provide to children
provide('parentAccordionId', readonly(uniqueAccordionId)); // Provide the computed value for ID
provide('isSectionOpen', isSectionOpen);
provide('toggleSection', toggleSection);
provide('registerSection', registerSection);
provide('unregisterSection', unregisterSection);

</script>

<template>
  <div :id="uniqueAccordionId" :data-accordion="props.alwaysOpen ? 'open' : 'collapse'">
    <slot></slot> </div>
</template>