<script setup lang="ts">
import { onMounted, ref, watch, type PropType } from 'vue'
import type { TabData } from '@/shared/services/tab-data'
import Common from '@/shared/services/common'

const props = defineProps({
    mode : {
        type: String as PropType<'Default' | 'Underlined' | 'Pills'>,
        default: "Underlined",
        validator: (value: string) => {
            // Ensure the value is one of the specified options
            return ['Default', 'Underlined', 'Pills'].includes(value);
        }
    },
    items : {
        type: Array<TabData>,
        required: true
    },
    visible : {
        type: Boolean,
        default: true
    }
})

const id = Common.newGuid()
const _items = ref<TabData[]>()

watch(() => [
    props.items, 
    props.mode,
    props.visible
], () => initialize())

onMounted(() => {
    initialize()
})

function initialize() {
    _items.value = props.items
    const selectedItems = _items.value.find(f => f.selected == true)

    if (selectedItems == null && _items.value.length > 0)
        _items.value[0].selected = true
}

function selectTab(tab: TabData) {
    _items.value?.forEach(f => f.selected = false)
    tab.selected = true
}
</script>

<template>
<template v-if="visible">
    <div :class="{ 'mb-4 border-b border-border-color': mode == 'Underlined' }">
        <ul :class="{ 
            '-mb-px text-sm font-medium text-center' : mode == 'Underlined',
            'text-sm font-medium text-center text-text-color-400 dark:text-text-color-400-dark' : mode == 'Pills',
            'text-sm font-medium text-center text-text-color-400 border-b border-border-color dark:text-text-color-400-dark' : mode == 'Default'
            }"
            class="flex flex-wrap"
            :id="id"
            :data-tabs-toggle="`#${id}content`" 
            role="tablist"
            data-tabs-active-classes="text-primary border-primary"
            data-tabs-inactive-classes="text-text-color-400 hover:text-text-color-200 border-transparent hover:border-border-color dark:text-text-color-400-dark dark:hover:text-text-color-200-dark">

            <li v-for="(tab, index) in items" class="me-2" role="presentation">
                <button
                    v-if="mode == 'Underlined'"
                    @click="selectTab(tab)" 
                    class="inline-block p-4 border-b-2 rounded-t-lg" 
                    :id="`t${tab.id}tab`"
                    :data-tabs-target="`#${tab.id}content`" 
                    type="button" 
                    role="tab"
                    :aria-controls="`${tab.id}content`" 
                    aria-selected="false">{{ tab.label }}</button>
                <a href="javascript:void(0)"
                    v-if="mode == 'Pills' || mode == 'Default'"
                    @click="selectTab(tab)" 
                    :class="{ 
                        'inline-block px-4 py-3 rounded-lg hover:text-text-color-200 hover:bg-bg-color-300 dark:hover:text-text-color-200-dark dark:hover:bg-bg-color-300-dark' : (mode == 'Pills' && !tab.selected),
                        'inline-block px-4 py-3 text-primary-text bg-primary rounded-lg' : (mode == 'Pills' && tab.selected),
                        'inline-block p-4 text-primary bg-bg-color-200 hover:bg-bg-color-300 rounded-t-lg active dark:bg-bg-color-200-dark dark:hover:bg-bg-color-300-dark' : (mode == 'Default' && tab.selected),
                        'inline-block p-4 rounded-t-lg hover:text-text-color-200 hover:bg-bg-color-300 dark:hover:text-text-color-200-dark dark:hover:bg-bg-color-300-dark' :  (mode == 'Default' && !tab.selected)
                    }"
                    role="tab"
                    :id="`t${tab.id}tab`"
                    :data-tabs-target="`#${tab.id}content`"  
                    :aria-controls="`${tab.id}content`" 
                    aria-current="page"
                    >
                    {{ tab.label }}
                </a>
            </li>

        </ul>
    </div>
    <div :id="`${id}content`">
        <div v-for="tab in items" 
            :id="`${tab.id}content`"
            role="tabpanel"
            :aria-labelledby="`t${tab.id}tab`"
            class="hidden p-4 rounded-lg">
            <slot :name="tab.id"></slot>
        </div>
    </div>
</template>
</template>