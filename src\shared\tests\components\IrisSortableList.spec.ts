import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import SortableList from '@/shared/components/general-controls/IrisSortableList.vue'

// Mock IrisSortable service
vi.mock('@/shared/services/sortable', () => ({
  default: vi.fn().mockImplementation(() => ({
    create: vi.fn(),
    destroy: vi.fn(),
    addEventListener: vi.fn()
  }))
}));

describe.skip('SortableList', () => {
  let wrapper: any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe.skip('SortableList rendering', () => {
    it('renders simple array items correctly', () => {
      const simpleItems = ['item1', 'item2'];
      wrapper = mount(SortableList, {
        props: {
          modelValue: simpleItems
        },
        slots: {
          default: '<template #default="{ item }"><div class="custom-item">{{ item }}</div></template>'
        }
      });
      
      const renderedItems = wrapper.findAll('.custom-item');
      expect(renderedItems).toHaveLength(2);
      expect(renderedItems[0].text()).toBe('item1');
      expect(renderedItems[1].text()).toBe('item2');
    });

    it('renders complex array items correctly', () => {
      const complexItems = [
        { customId: 1, text: 'first' },
        { customId: 2, text: 'second' }
      ];

      wrapper = mount(SortableList, {
        props: {
          modelValue: complexItems,
          itemKey: (item: any) => `key-${item.customId}`
        },
        slots: {
          default: '<template #default="{ item }"><div class="custom-item">{{ item.text }}</div></template>'
        }
      });

      const renderedItems = wrapper.findAll('.custom-item');
      expect(renderedItems).toHaveLength(2);
      expect(renderedItems[0].text()).toBe('first');
      expect(renderedItems[1].text()).toBe('second');
    });

    it('updates internal list when modelValue changes', async () => {
      const items = ['item1', 'item2'];
      wrapper = mount(SortableList, {
        props: {
          modelValue: items
        },
        slots: {
          default: '<template #default="{ item }"><div class="custom-item">{{ item }}</div></template>'
        }
      });

      let renderedItems = wrapper.findAll('.custom-item');
      expect(renderedItems).toHaveLength(2);
      expect(renderedItems[0].text()).toBe('item1');
      expect(renderedItems[1].text()).toBe('item2');

      await wrapper.setProps({
        modelValue: ['item3', 'item4']
      });

      renderedItems = wrapper.findAll('.custom-item');
      expect(renderedItems).toHaveLength(2);
      expect(renderedItems[0].text()).toBe('item3');
      expect(renderedItems[1].text()).toBe('item4');
    });
  });

  it('emits update event on reorder', async () => {
    const items = ['item1', 'item2', 'item3'];
    wrapper = mount(SortableList, {
      props: {
        modelValue: items
      }
    });

    const sortableInstance = wrapper.vm.irisSortable;
    const endCallback = sortableInstance.addEventListener.mock.calls[0][1];
    
    // Simulate drag end event
    endCallback({
      detail: {
        evt: { oldIndex: 0, newIndex: 2 }
      }
    });

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')[0][0]).toEqual(['item2', 'item3', 'item1']);
  });

  it('initializes sortable with options', () => {
    const options = { animation: 150 };
    wrapper = mount(SortableList, {
      props: {
        modelValue: ['item1'],
        options
      }
    });

    expect(wrapper.vm.irisSortable.create).toHaveBeenCalledWith(
      expect.any(HTMLElement)
    );
  });

  it('cleans up on unmount', () => {
    wrapper = mount(SortableList, {
      props: {
        modelValue: ['item1']
      }
    });

    const sortableInstance = wrapper.vm.irisSortable;
    wrapper.unmount();
    
    expect(sortableInstance.destroy).toHaveBeenCalled();
  });
});