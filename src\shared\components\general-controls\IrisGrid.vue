<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { GridColumn } from '@/shared/services/grid-column'
import { GridAction, GridActionMode } from '@/shared/services/grid-action'
import { FormulaParser } from '@/shared/services/formula-parser'
import <PERSON><PERSON><PERSON> from '@/shared/components/form-controls/IrisField.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
import LanguageHelper from '@/shared/services/language-helper'

const props = defineProps({
    id: {
        type:String,
        required: true
    },
    hoverable: {
        type: Boolean,
        default: true
    },
    value: {
        type: Array<Object>,
        default: [],
        required: true
    },
    sortable: {
        type: Boolean,
        default: true
    },
    sortExpression: {
        type: String,
        default: ""
    },
    sortDirection: {
        type: String,
        default: "A"
    },
    cols: {
        type: Array<GridColumn>,
        required: true
    },
    actions: {
        type: Array<GridAction>,
        required: false
    },
    showBorder: {
        type: Boolean,
        default: true
    },
    showRounding: {
        type: Boolean,
        default: true
    },
    showHeaderBackground: {
        type: Boolean,
        default: true
    },
    selectable: {
        type: Boolean,
        default: false
    }, 
    multiSelect: {
        type: Boolean,
        default: true
    },
    noDataMessage: {
        type: String
    }
})

const base = useIrisFormControl(props)
const _cols = ref(props.cols)
const _isSelectAllChecked = ref(false) 
const _rows = ref<{ item: Object, selected: boolean }[]>()
const _actions = ref(props.actions)
const languageHelper = new LanguageHelper()
let _metadata : any | null = null

// Emits
const emits = defineEmits({
    onSelect: (selectedItems: Object[]) => true,
    onSort: (column: GridColumn) => true,
    actionClicked: (actionName: string, data: object) => true
})

const gridCols = computed(() => {
    return _cols.value?.filter(item => item.visible === true);
})

onMounted(() => {
    initialize()
})

watch(() => [props.value,props.cols], () => initialize())

function initialize() : void {
    _metadata = getMetadata() 
    validateProps()
    initCols()
    initActions()
    initRows()
}

function validateProps() : void {
    // Validate columns there should be only one selected or none.
    if (props.cols && props.cols.length > 0) {

        let selectedCount = 0
        props.cols.forEach(col => {
            if (col.selected)
                selectedCount++
        })

        if (selectedCount > 1)
            throw new Error(base.languageHelper.getMessage('singleSelectedColumnOnly'))
    }
}

function initCols() : void {
    _cols.value = []

    if (!props.cols || props.cols.length == 0)
        return

    // Read the labels from metadata
    props.cols.forEach(col => {
        let gcol = GridColumn.create(col)
        
        if (_metadata != null) {
            const fieldMeta = _metadata.Fields.find((f: { Id: string; Name: string; }) => f.Id == gcol.name || f.Name == gcol.name)

            if (fieldMeta != null) {

                if (!gcol.label)
                    gcol.label = fieldMeta.Label
            }
        }

        _cols.value.push(gcol)
    })
}

function initRows(): void {
    const data : any[] = []

    if (props.value) {
        props.value.forEach(item => {
            const row = { 
                item: item as Object,
                selected: false
            }

            data.push(row)
        })
    }

    _rows.value = data
}

function initActions(): void {
    _actions.value = []

    if (!props.actions || props.actions.length == 0)
        return
    
    props.actions.forEach(action => {
        let act = GridAction.create(action)

        _actions.value?.push(act)
    })
}

function getMetadata() : any | null {
    if (props.value?.length > 0)
        return (props.value[0] as any).__Metadata()
    else
        return null
}

function toggleSelectAll() : void {
    _rows.value?.forEach(item => {
        item.selected = _isSelectAllChecked.value
    })

    emits('onSelect', _rows.value?.filter(f => f.selected  == true).map(f => f.item)!)
}

function toggleRowSelection() : void {
    _isSelectAllChecked.value = false
    
    emits('onSelect', _rows.value?.filter(f => f.selected  == true).map(f => f.item)!)
}

function selectRow(row: { item: Object, selected: boolean }) : void {
    if (props.multiSelect)
        return

    var isItemAlreadySelected = _rows.value?.find(o => o.selected == true && o.item == row.item)

    if (isItemAlreadySelected)
        return

    _rows.value?.forEach(item => {
        item.selected = false
    })

    row.selected = true
    emits('onSelect', _rows.value?.filter(f => f.selected  == true).map(f => f.item)!)
}

function sort(column : GridColumn) : void {
    if (column.selected)
        column.reverseSortDirection()

    _cols.value.forEach(col => col.selected = false)
    
    column.selected = true
    emits('onSort', column)
}

function getActionPath(path: string, data : object) {
    if (path) {
        const parser = new FormulaParser(data)
        return parser.replaceTokens(path)
    }

    return ''
}

function actionClicked(actionName: string, data: object) {
    if (!actionName)
        return

    // If action is a link, we don't need to emit the event.
    if (_actions.value?.find(a => a.path == actionName && a.type == 'link'))
        return

    // Close dropdown by simulating a click outside
    setTimeout(() => {
        document.body.click()
    }, 0)

    // Emit the action clicked event 
    emits('actionClicked', actionName, data)
}
</script>

<template>
    <div class="relative overflow-x-auto" :class="{
        'border border-border-color' : showBorder,
        'rounded-lg' : showRounding,
    }">
        <caption v-if="$slots.caption" class="p-5 border-b block text-lg font-semibold text-left rtl:text-right text-text-color-100 bg-bg-color dark:text-text-color-100-dark dark:bg-bg-color-dark">
            <slot name="caption"></slot>
        </caption>
        <table class="w-full text-sm text-left rtl:text-right">
            <thead class="text-xs uppercase" :class="{ 'bg-bg-color-200 dark:bg-text-color-200-dark' : showHeaderBackground }">
                <tr class="border-b-2">
                    <th v-if="selectable" scope="col" class="p-4">
                        <div v-if="multiSelect" class="flex items-center">
                            <input 
                                :id="`${id}-select-all`" 
                                type="checkbox" 
                                @change="toggleSelectAll"
                                v-model="_isSelectAllChecked"
                                class="w-4 h-4 text-primary bg-bg-color border-text-color-400 rounded focus:ring-primary focus:ring-2 dark:bg-bg-color-dark dark:border-text-color-400-dark" />
                            <label 
                                :for="`${id}-select-all`" 
                                class="sr-only">{{base.languageHelper.getMessage('selectAll')}}</label>
                        </div>
                    </th>
                    <th v-if="actions && actions.length > 0" scope="col" class="p-4">Actions</th>
                    <th v-for="(col, index) in gridCols" scope="col" class="p-3 whitespace-nowrap">
                        <a href="javascript:void(0)" class="whitespace-nowrap" v-if="col.sortExpression" @click="sort(col)">
                            {{ col.label }}
                            <span class="iris-sort-asc opacity-50" v-if="!col.selected"><IrisIcon name="sort-disabled" class="iris-icon"></IrisIcon></span>
                            <span class="inline" v-if="col.selected">
                                <span class="iris-sort-asc" v-if="col.sortDirection == 'A'"><IrisIcon name="sort-a" class="iris-icon"></IrisIcon></span>
                                <span class="iris-sort-asc" v-if="col.sortDirection == 'D'"><IrisIcon name="sort-d" class="iris-icon"></IrisIcon></span>
                            </span>
                        </a>
                        <span v-else>{{ col.label }}</span>
                    </th>
                </tr>
            </thead>
            <tbody v-if="value && value.length > 0">
                <tr v-for="(row, index) in _rows" 
                    :class="{
                        'hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark' : hoverable,
                        'bg-bg-color-200 dark:bg-bg-color-200-dark' : row.selected,
                        //'bg-bg-color dark:bg-bg-color-dark' : !row.selected,
                        'border-b' : index !== _rows!.length - 1
                    }"
                    @click="selectRow(row)">
                    <td v-if="selectable" class="w-4 p-4">
                        <div v-if="multiSelect" class="flex items-center">
                            <input
                                v-model="row.selected"
                                @change="toggleRowSelection"
                                :id="`${id}-row-${index}`"
                                type="checkbox"
                                class="w-4 h-4 text-primary bg-bg-color border-text-color-400 rounded focus:ring-primary focus:ring-2 dark:bg-bg-color-dark dark:border-text-color-400-dark" />
                            <label :for="`${id}-row-${index}`" class="sr-only">{{base.languageHelper.getMessage('select')}}</label>
                        </div>
                        <div v-if="!multiSelect">
                            <input 
                                :id="`${id}-row-${index}`"
                                :name="`${id}-row`"
                                :checked="row.selected"
                                type="radio"
                                @change="selectRow(row)"
                                class="w-4 h-4 text-primary bg-bg-color border-text-color-400 focus:ring-primary focus:ring-2 dark:bg-bg-color-dark dark:border-text-color-400-dark" />
                            <label :for="`${id}-row-${index}`" class="sr-only">{{base.languageHelper.getMessage('select')}}</label>
                        </div>
                    </td>
                    <td v-if="actions && actions.length > 0" class="px-4">
                        <button :id="`${id}_btnGrid_${index}`" :data-dropdown-toggle="`${id}_btnGridToggle_${index}`"
                        data-dropdown-placement="bottom-start"
                          class="inline-flex items-center p-2 text-sm font-medium text-center btn-light" type="button"> 
                            <IrisIcon name="chevron-down" class="w-3 h-3"/>
                        </button>

                        <!-- Dropdown menu -->
                        <div :id="`${id}_btnGridToggle_${index}`" class="z-10 hidden divide-y rounded-lg shadow overflow-hidden bg-bg-color divide-border-muted border border-border-color dark:bg-bg-color-dark dark:border-border-color-dark dark:divide-border-muted-dark">
                            <ul class="py-2 text-sm text-text-color dark:text-text-color-dark" :aria-labelledby="`${id}_btnGrid_${index}`">
                                <li v-for="(action, index) in _actions">
                                    <IrisLink v-if="action.type === GridActionMode.Link" :path="getActionPath(action.path, row.item)" class="block px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">
                                        <IrisIcon :name="action.icon"/> {{ action.label }}
                                    </IrisLink>
                                    <a v-else @click="actionClicked(action.path, row.item)" class="block px-4 py-2 hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark cursor-pointer">
                                        <IrisIcon :name="action.icon"/> {{ action.label }}
                                    </a>
                                </li> 
                            </ul> 
                        </div>
                    </td>
                    <td class="p-3" v-for="(col,colIndex) in gridCols">
                        <IrisField 
                            :id="`${base.uniqueId}-${index}-${colIndex}`"
                            :value="row.item"
                            :field="col.name" 
                            :mode="col.mode" 
                            :showLabel="false"
                            :component="col.component"
                            :data="col.data"/>
                    </td>
                </tr>
            </tbody>

            <tbody class="p-3 text-text-color-400 dark:text-text-color-400-dark rounded-lg" v-else>
                <tr>
                    <td class="p-3" :colspan="(gridCols && gridCols.length)? gridCols.length : 1">
                        <span>{{ noDataMessage || languageHelper.getMessage('noDataToDisplay') }}</span>
                    </td>
                </tr>
            </tbody>
        </table>
        
    </div>
</template>