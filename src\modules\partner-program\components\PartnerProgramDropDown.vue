<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import { DropDownItem } from '@/shared/services/dropdown-item'
import Common from '@/shared/services/common'
import DataAccess from '@/shared/services/data-access'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'

const props = defineProps({
  partnerAccountId: {
    type: String,
    default: null
  }, 
  selectedProgramId: {
    type: String
  }, 
  id: {
    type: String,
    required: true
  } 
})

// Emits
const emit = defineEmits(["onChange", "onLoaded", "onEmpty", "onNoPartner"])

const _selectedProgramId = ref(props.selectedProgramId)
const _partnerAccountId = ref(props.partnerAccountId)
const _programItems = ref<DropDownItem[]>([])

onMounted(() => {
  fetchPartnerPrograms()
})

watch(() => props.selectedProgramId, (newValue) => {
    _selectedProgramId.value = newValue
})

watch(() => props.partnerAccountId, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    fetchPartnerPrograms()
  }
})

// Fetch Partner Programs
async function fetchPartnerPrograms() {
  if (!props.partnerAccountId) {
    emit("onNoPartner")
    return
  }
  
  try {
    const endpoint = `/partnerprogram/getpartnerprograms`
    const data = {
      partnerAccountId: props.partnerAccountId
    }

    const url = Common.constructUrl(endpoint, data)
    const dataAccess = new DataAccess()
    const response = await dataAccess.execute(url, RequestMethod.get)

    if (!response || !response.programs || response.programs.length === 0) {
      emit("onEmpty")
      return
    }

    _programItems.value = response.programs?.map((program: any) => ({
      value: program.ProgramId, 
      label: program.ProgramName
    })) || []

    if (!_selectedProgramId.value && response?.selectedProgramId) 
      _selectedProgramId.value = response.selectedProgramId

    if (!_partnerAccountId.value) 
      _partnerAccountId.value = response.partnerAccountId

    emit("onLoaded", {
      partnerAccountId: _partnerAccountId.value,
      selectedProgramId: _selectedProgramId.value
    })

  } catch {
    emit("onEmpty")
  }
}

function handleSelectionChange(newValue: any) {
  _selectedProgramId.value = newValue

  // Emit both selectedProgramId and full selectedProgram object
  emit("onChange", {
    partnerAccountId: _partnerAccountId.value,
    selectedProgramId: _selectedProgramId.value
  })
}

</script>

<template>
  <iris-picklist 
    v-if="_programItems.length"
    :id="id" 
    :value="_selectedProgramId"
    :items="_programItems"
    placeholder="Select Partner Program"
    :isNullable="true"
    @onChange="handleSelectionChange"
  />
</template>