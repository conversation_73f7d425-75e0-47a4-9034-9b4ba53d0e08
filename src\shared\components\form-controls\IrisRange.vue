<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, computed, getCurrentInstance } from 'vue'
import { ComponentSizeEnum, FormControlModeEnum } from '@/shared/services/form-control-enums'
import { useIrisFormControl } from '@/shared/composables/iris-form-control'
import { useIrisFormControlStore } from '@/shared/stores/form-control-store'
import { ModelError } from '@/shared/services/model-error'
import { constants } from '@/shared/services/constants'
import { FieldValidation } from '@/shared/services/field-validation'
import NumberHelper from '@/shared/services/number-helper'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    field: String,
    tabIndex: Number,
    required: Boolean,
    value: [Number, Object],
    id: {
        type: String,
        required: true
    },
    label: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxLabelLength
        }
    },
    hint: {
        type: String,
        validator: (value: string) => {
            return value.length <= constants.formControls.maxHintLength
        }
    },
    mode: {
        type: String,
        default: FormControlModeEnum.edit,
        validator: (value: string) => {
            return (<any>Object).values(FormControlModeEnum).includes(value)
        }
    },
    min: { 
        type: Number,
        default: 0
    },
    max: {
        type: Number,
        default: 100
    },
    step: { 
        type: Number,
        validator: (value: number) => {
            return value > 0;
        }
    },
    size: {
        type: String,
        default: ComponentSizeEnum.default,
        validator: (value: string) => {
            return (<any>Object).values(ComponentSizeEnum).includes(value)
        }
    },
    displayLabels: {
        type: Boolean,
        default: false
    },
    showLabel: {
        type: Boolean,
        default: true
    }
})

// Emits
const emits = defineEmits({
    onChange: (value: string) => value
})

// Composables
const base = useIrisFormControl(props)
const store = useIrisFormControlStore()
const numberHelper = new NumberHelper()

// Refs
const $el = ref<HTMLElement>()
const _value = ref(base.getValue())
const _required = ref(props.required)
const _label = ref(props.label)
const _mode = ref(props.mode)
const _min = ref(props.min)
const _max = ref(props.max)
const isVisible = ref(true)
const _decimalPoints = ref(0)

// Hooks
onMounted(() => {
    // Add symbol for section detection
    base.addInputFieldSymbol(getCurrentInstance())
    isVisible.value = !base.isBound() || base.fieldMetadata.value.IsReadable

    if (isVisible.value) {
        const formId = base.getParentFormId($el.value!)
        store.add(base.uniqueId, formId, base.getErrors, validate)
        initialize(false)
    }
})

onUnmounted(() => {
    if (isVisible.value) 
        store.remove(base.uniqueId)
})

// computed
const formattedValue = computed(() => numberHelper.formatNumber(_value.value, { maximumFractionDigits:_decimalPoints.value }))

// watches
watch(_value, () => {
    if (validate()) {

        if (base.isBound())
            (<any>props.value)[props.field!] = _value.value

        emits('onChange', _value.value)
    }
})

watch(() => [
    props.value, 
    props.required,
    props.label, 
    props.mode,
    props.size,
    props.max,
    props.min, 
    props.hint
], () => initialize(true))

function initialize(performValidation: boolean) {
    setPropValues()
    validateProps()

    if (performValidation) 
        validate()
}

function setPropValues() {
    _value.value = base.getValue()

    if (base.isBound()) {
        _required.value = props.required || base.fieldMetadata.value.IsRequired
        _label.value = props.label || base.fieldMetadata.value.Label
        _decimalPoints.value = base.fieldMetadata.value.Precision
        
        // read min and max from metadata
        const validations = base.fieldMetadata.value.Validations as Array<FieldValidation>

        if (validations && validations.length > 0) {
            const rangeVal = validations.find(f => f.Type == "Range")

            if (rangeVal != null) {
                var rangeData = JSON.parse(rangeVal.Data)
                _min.value = rangeData.min
                _max.value = rangeData.max
            }
        }

        if (base.fieldMetadata.value.IsReadOnly && _mode.value != FormControlModeEnum.text)
            _mode.value = FormControlModeEnum.html
    }
    else {
        _required.value = props.required
        _label.value = props.label
        _mode.value = props.mode
        _min.value = props.min
        _max.value = props.max
    }
}

function validateProps(): void {
    if (!(<any>Object).values(FormControlModeEnum).includes(_mode.value))
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'mode' }))

    if (base.isBound() && !props.value && props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'value' }))

    if (base.isBound() && props.value && !props.field)
        throw new Error(base.languageHelper.getMessage('invalidProp', { prop: 'field' }))

    if (props.hint && props.hint.length > constants.formControls.maxHintLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'hint', length: constants.formControls.maxHintLength }))

    if (props.label && props.label.length > constants.formControls.maxLabelLength)
        throw new Error(base.languageHelper.getMessage('invalidMaxLength', { prop: 'label', length: constants.formControls.maxLabelLength }))

    if (props.max <= props.min)
        throw new Error(base.languageHelper.getMessage('invalidMaxRange'))
}

function validate(): boolean {
    if (_mode.value == FormControlModeEnum.edit) 
        return true

    var errors: ModelError[] = []

    if (_required.value && !_value.value && base.isTouched) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('isRequired', { label: _label.value })

        errors.push(new ModelError(field, message))
    }

    if (_value.value && (_value < _min || _value > _max)) {
        const field = props.field ?? ''
        const message = base.languageHelper.getMessage('invalidRange', { min: _min.value, max: _max.value })

        errors.push(new ModelError(field, message))
    }

    const result = errors.length == 0

    base.addErrors(errors)

    return result
}
</script>

<template>
    <div ref="$el" v-if="isVisible">
        <template v-if="mode == FormControlModeEnum.html">
            <label v-if="_label && mode == FormControlModeEnum.html && showLabel" class="iris-label"> {{ _label }} </label>
            <span>{{ formattedValue }}</span>
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
        </template>
        <template v-else-if="mode == FormControlModeEnum.text">
            {{ formattedValue }}
        </template>
        <template v-else>
            <div :class="[ displayLabels? 'mb-6':'', 'relative' ]">
                <label v-if="_label && showLabel" :for="id" class="iris-label"> 
                    {{ _label }} 
                    <span class="iris-required" v-if="_required"><IrisIcon name="asterisk" class="iris-icon" width="0.5rem" height="0.5rem"></IrisIcon></span>
                </label>
                <span v-if="displayLabels" class="text-sm font-medium text-text-color-400 absolute end-0 dark:text-text-color-400-dark" style="margin-top:-1.6rem">
                    {{ formattedValue }}
                </span>
                <input :id="id"
                    type="range"
                    v-model.number="_value"
                    class="w-full bg-bg-color-300 rounded-lg appearance-none cursor-pointer iris-min-max-range dark:bg-bg-color-300-dark"
                    :class="{
                        'range-lg h-3': size == 'large',
                        'h-2': size == 'default',
                        'range-sm h-1': size == 'small',
                    }"
                    :tabindex="_mode == FormControlModeEnum.edit ? tabIndex : undefined" 
                    :disabled="_mode == FormControlModeEnum.disabled"
                    :required="_required"
                    :min="_min" 
                    :max="_max"
                    :step="step"
                    />
                <span v-if="displayLabels" class="text-sm text-text-color-400 absolute start-0 -bottom-6 dark:text-text-color-400-dark">{{ numberHelper.formatNumber(min) }}</span>
                <span v-if="displayLabels" class="text-sm text-text-color-400 absolute end-0 -bottom-6 dark:text-text-color-400-dark">{{ numberHelper.formatNumber(max) }}</span>
            </div>
            <div class="iris-input-hint" v-if="hint">{{ hint }}</div>
            <div v-if="_mode == FormControlModeEnum.edit" class="text-danger small" v-for="error in base.getErrors()">{{ error.message }}</div>
        </template>
    </div>
</template>

<style scoped>
.iris-min-max-range {
  &::-webkit-slider-thumb {
    background-color: var(--mag-primary-color); 
  }
  &::-moz-range-thumb {
    background-color: var(--mag-primary-color); 
  }
  &::-ms-thumb {
    background-color: var(--mag-primary-color); 
  }
}

.iris-min-max-range:disabled::-webkit-slider-thumb {
  background-color: var(--mag-base-400);
}
.iris-min-max-range:disabled::-moz-range-thumb {
  background-color: var(--mag-base-400);
}
.iris-min-max-range:disabled::-ms-thumb {
  background-color: var(--mag-base-400);
}
</style>