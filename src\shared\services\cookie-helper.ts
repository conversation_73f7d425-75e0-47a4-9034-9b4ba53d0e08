export default class CookieHelper {
    // Set a cookie with expiration date
    static setCookie(name: string, value: string, days: number = 7, secure: boolean = true, httpOnly: boolean = false): void {
        const expires = new Date(Date.now() + days * 864e5).toUTCString();
        let cookie = `${name}=${encodeURIComponent(value)}; expires=${expires}; path=/`;
        
        if (secure) 
            cookie += '; Secure';

        if (httpOnly)
            cookie += '; HttpOnly';

        document.cookie = cookie;
    }

    // Set a session cookie (no expiration date)
    static setSessionCookie(name: string, value: string, secure: boolean = true, httpOnly: boolean = false): void {
        let cookie = `${name}=${encodeURIComponent(value)}; path=/`;

        if (secure) 
            cookie += '; Secure';

        if (httpOnly) 
            cookie += '; HttpOnly';

        document.cookie = cookie;
    }

    // Get a cookie by name
    static getCookie(name: string): string | null {
        const cookies = document.cookie.split('; ');
        const cookie = cookies.find(cookie => cookie.startsWith(`${name}=`));
        return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
    }

    // Delete a cookie by name
    static deleteCookie(name: string): void {
        this.setCookie(name, '', -1);
    }

    // Check if a cookie exists
    static hasCookie(name: string): boolean {
        return this.getCookie(name) !== null;
    }
}

  