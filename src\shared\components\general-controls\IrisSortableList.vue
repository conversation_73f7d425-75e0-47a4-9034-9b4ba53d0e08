<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import IrisSortable from '@/shared/services/sortable'

// Props
const props = defineProps({
  modelValue: {
    type: [Array, String],
    required: true,
  },
  options: {
    type: Object,
    default: () => ({}),
  },
  itemKey: {
    type: [String, Function],
    default: 'id',
  },
  delimiter: {
    type: String,
    default: ';',
  },
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Refs & Vars
const internalList = ref(parseModelValue(props.modelValue));
const sortableContainer = ref<HTMLElement | null>(null)
const irisSortable = new IrisSortable()

// Computed
const isStringMode = computed(() => typeof props.modelValue === 'string');

// Generate a unique key for the entire component based on the list
const componentKey = computed(() => {
  if (isStringMode.value)
    return internalList.value.join(props.delimiter);
  
  return internalList.value.map(item => getKey(item)).join('|');
});

// Hooks
onMounted(initSortable)
onBeforeUnmount(() => {
  if (irisSortable)
    irisSortable.destroy()
})

// Watches
watch(
  () => props.modelValue,
  (newVal) => {
    internalList.value = parseModelValue(newVal);

    // Small delay to ensure DOM is updated
    setTimeout(() => {
      initSortable();
    }, 0);
  },
  { deep: true }
)

watch(() => props.options, initSortable, { deep: true });

// Functions
function initSortable() {
  if (sortableContainer.value) {
    irisSortable.addEventListener('onEnd', (evt: any) => {
      const updatedList = [...internalList.value];
      const [movedItem] = updatedList.splice(evt.detail.evt.oldIndex!, 1);
      updatedList.splice(evt.detail.evt.newIndex!, 0, movedItem);
      internalList.value = updatedList;
      const newValue = isStringMode.value
        ? updatedList.join(props.delimiter)
        : updatedList;
      emit('update:modelValue', newValue);
    })
    irisSortable.create(sortableContainer.value) 
  }
}

function getKey(item: any) {
  return typeof props.itemKey === 'function'
    ? props.itemKey(item)
    : typeof item === 'object' && item !== null
    ? item[props.itemKey as string]
    : item;
}

function parseModelValue(val: any) {
  return typeof val === 'string'
    ? val.split(props.delimiter).map((s) => s.trim())
    : Array.isArray(val)
    ? [...val]
    : [];
}
</script>

<template>
  <div ref="sortableContainer" :key="componentKey">
    <slot
      v-for="(item, index) in internalList"
      :item="item"
      :index="index"
      :key="getKey(item)"
    >
      <div>{{ item }}</div>
    </slot>
  </div>
</template>