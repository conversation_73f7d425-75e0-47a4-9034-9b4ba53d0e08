import { describe, it, expect, beforeAll } from "vitest"
import { createPinia, setActiveP<PERSON> } from "pinia"
import I18n<PERSON>elper from "@/shared/services/i18n-helper"

describe("I18nHelper", () => {
    let i18nHelper: I18nHelper

    beforeAll(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
        i18nHelper = new I18nHelper()
    })

    it("Get locales by currency ISO code", () => {
        const locales = i18nHelper.getLocalesByCurrencyIsoCode('CAD')
        const result = locales.includes('en-CA') && locales.includes('fr-CA')

        expect(result).toEqual(true)
    })
})
