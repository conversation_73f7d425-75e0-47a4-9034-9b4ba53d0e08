<script setup lang="ts">
import { onMounted, ref, watch, type PropType } from 'vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

interface SocialLink {
    Id: string
    Name: string
    Link: string
    Icon: string
}

const props = defineProps({
    value: {
        type: Array as PropType<SocialLink[]>,
        required: true
    }
})

defineOptions({
  inheritAttrs: false
})

const hasValues = ref(false);

onMounted(() => {
    initialize()
});

watch(() => props.value, () => initialize(), { deep: true });

function initialize() {
    hasValues.value = props.value.length > 0
}

function getHref(item: SocialLink) {
    if (item.Name.toLowerCase() === 'phone') {
        return `tel:${item.Link}`
    } else if (item.Name.toLowerCase() === 'email') {
        return `mailto:${item.Link}`
    }
    return item.Link
}

</script>
<template>
    <div v-if="hasValues">
        <ul v-if="hasValues" class="list-none social-media-links">
            <li v-for="(item, index) in value" :key="item.Id" class="inline-block rounded-full social-media-links-list-item mr-2 mb-2">
                <a :href="getHref(item)" target="_blank" :aria-label="item.Name" :class="$attrs.class">
                    <IrisIcon :name="item.Icon.toLowerCase()" width="1.5rem" height="1.5rem" />
                </a>
            </li>
        </ul>
    </div>  
</template>