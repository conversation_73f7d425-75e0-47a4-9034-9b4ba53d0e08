@mixin calc-primary-hover-color($base-color, $color-name) {
  $adjusted-color: lighten($base-color, 5%);
  --#{$color-name}: #{$adjusted-color};
}

// Custom absolute value function for SCSS
@function abs($number) {
    @if $number < 0 {
        @return -$number;
    }
    @else {
        @return $number;
    }
}

// Custom square root function for SCSS
@function sqrt($number) {
    $approx: calc($number / 2);
    $epsilon: 0.0001;

    @while abs($approx * $approx - $number) > $epsilon {
        $approx: calc(($approx + $number / $approx) / 2);
    }

    @return $approx;
}

@mixin lighten-darken-color($color, $color-name, $percent) {
  $blackness-value: color.blackness($color);
  
  @if $blackness-value < 50% {
    $percent: -$percent;
  }

  $r: red($color);
  $g: green($color);
  $b: blue($color);

  $amt: round($percent * 2.55);

  $r: min(max(0, $r + $amt), 255);
  $g: min(max(0, $g + $amt), 255);
  $b: min(max(0, $b + $amt), 255);
  
  $color: 'rgb(' + $r + ',' + $g + ',' + $b + ')';

  --#{$color-name}: #{$color};
}

@mixin calculate-color-shade($baseColor,$color-name, $percent, $mode: "auto") {

  $r: red($baseColor);
  $g: green($baseColor);
  $b: blue($baseColor);

  @if $mode == "auto" {
      // Determine light or dark adjustment based on brightness
      $hsp: sqrt(0.299 * ($r * $r) + 0.587 * ($g * $g) + 0.114 * ($b * $b));

      @if $hsp <= 127.5 {
          $percent: abs($percent);
      }
      @else {
          $percent: -$percent;
      }
  }
  @else if $mode == "lighten" {
      $percent: abs($percent);
  }

  $adjustment: round($percent * 2.55);

  // Adjust color components
  $r: min(max(0, $r + $adjustment), 255);
  $g: min(max(0, $g + $adjustment), 255);
  $b: min(max(0, $b + $adjustment), 255);
  
  $color: 'rgb(' + $r + ',' + $g + ',' + $b + ')';

  --#{$color-name}: #{$color};
}