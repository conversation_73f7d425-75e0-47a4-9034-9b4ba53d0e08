<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { RequestMethod } from '@/shared/services/enums'
import Common from '@/shared/services/common'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import LanguageHelper from '@/shared/services/language-helper'
import Debounce from '@/shared/services/debounce'
import DataAccess from '@/shared/services/data-access'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'

interface AutoCompleteItem {
    Id: string
    Name: string
    EntityLabel: string
    Url: string
}

const props = defineProps({
    hoverEffect: {
        type: Boolean,
        default: true
    }
})

const isModalVisible = ref(false)
const languageHelper = new LanguageHelper()
const clientID = 'gs' + Common.newGuid()
const searchQuery = ref<string>('')
const isFocused = ref<boolean>(false)
const isHovered = ref<boolean>(false)
const searchInput = ref<HTMLInputElement | null>(null)
const modalSearchInput = ref<HTMLInputElement | null>(null)
const searchResults = ref<AutoCompleteItem[]>([])
const selectedIndex = ref(-1)
const dropdownContainer = ref<HTMLElement | null>(null)
const modalDropdownContainer = ref<HTMLElement | null>(null)
const showLengthLimitErrorMessage = ref(false)
let screenWidth = 0
const focusInput = () => {
  isFocused.value = true
  nextTick(() => {
    setInputFocus()
    
    handleEnterPress()
  })
}

// This method gets called on Enter key press
const handleEnterPress = () => {

    if (selectedIndex.value > -1 && searchResults.value.length >= selectedIndex.value) {
        const id = searchResults.value[selectedIndex.value].Id
        selectItem(id)
        return
    }

    if (searchQuery.value && searchQuery.value.trim() && searchQuery.value.length >= 2) {
        showLengthLimitErrorMessage.value = false
        loadGlobalSearchResults() 
        searchQuery.value = ''
        onModalHide()
    } else if (searchQuery.value && searchQuery.value.trim()) {
        showLengthLimitErrorMessage.value = true
    }
}

const loadGlobalSearchResults = () => {
    router.push({ 
        path: '/global-search', 
        query: { kw: searchQuery.value }
    })
}

let searchError = ref('')
let previousSearchText = ""
const router = useRouter()

// Debounce prevents too many queries to be sent out to the backend, leading flag means that system should
// sent the request right away and trailing flag means that system should first wait until time expires and then run.
const debounceInstance = new Debounce(searchByKeywords, 500, { leading: false, trailing: true })

const divClass = computed(() => {
  return props.hoverEffect && !searchQuery.value && !isFocused.value && !isHovered.value
    ? 'w-0'
    : 'w-80';
});

onMounted(() => { 
    initialize()
})

function initialize() {
    window.addEventListener('resize', onResize)
}

function doSearch(e: Event) {

    if (searchQuery.value.length >= 2) {

        if (searchQuery.value !== previousSearchText && searchQuery.value.trim() != '') {
            previousSearchText = searchQuery.value
            debounceInstance.debounced()
        }
    }
}

async function searchByKeywords() {
    if (!searchQuery.value || searchQuery.value.length < 2)
        return

    var endpoint = `sys/globalsearch/autocompletesearch`
    const data: any = {
        term: searchQuery.value,
    }

    const url = Common.constructUrl(endpoint, data)
    
    const dataAccess = new DataAccess()

    try {
        searchResults.value = await dataAccess.execute(url, null, RequestMethod.get)
    }
    catch (err: any) {
        searchError.value = err.message
        return
    }
}

function selectItem(id: string) {
    // find the record selected based on ID
    const item = searchResults.value.find(o => o.Id == id)
    let url = item?.Url ?? `/${item?.Id}`

    // check to see if the item's URL is part of Iris routes
    // if yes, use Iris Route to load the proper component, otherwise use window.location to navigate.

    if (!Common.IsIrisPath(url, router))
        window.location.href = url
    else
        router.push({ path: url })
}

function highlightKeywordInSearchText(name: string, search: string) : string {
    // Escape special characters in the keyword for regex
    const escapedKeyword = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    const safeName = Common.htmlEncode(name)

    // Create a regular expression to match the keyword
    const regex = new RegExp(`(${escapedKeyword})`, 'gi')

    // Replace occurrences of the keyword with bolded version
    const boldedText = safeName.replace(regex, '<strong>$1</strong>')
    return boldedText;
}

function selectNextItem() {
    let maxLength = searchResults.value.length

    if (selectedIndex.value < (maxLength-1)) {
        selectedIndex.value++;
        nextTick(scrollToSelectedItem)
    }
}

function selectPreviousItem() {
    if (selectedIndex.value > 0) {
        selectedIndex.value--
        nextTick(() => scrollToSelectedItem(false))
    } else if (selectedIndex.value == 0) {
        selectedIndex.value--
        setInputFocus()
    }
}

function scrollToSelectedItem(goingDown: boolean = true) {
    
    const dropdown = (isModalVisible.value)? modalDropdownContainer.value : dropdownContainer.value;
    const modifier = (goingDown)? 1 : -1
    const selectedItem = dropdown?.querySelector(`li:nth-child(${selectedIndex.value + modifier})`);
    
    if (selectedItem) {
        selectedItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
        (selectedItem as HTMLElement).focus();
    }
}

function showModal() {
    isModalVisible.value = true
    setTimeout(() => {
        setInputFocus()
    }, 400)
    
}

function onModalHide() {
    isModalVisible.value = false
}

function onResize() {
    nextTick(() => {
        if (screenWidth != window.innerWidth) {
            screenWidth = window.innerWidth;

            if (searchQuery.value && window.innerWidth < 768) {
                // search box is open but the screen now cannot show it, open the modal:
                showModal()
            } else if (isModalVisible.value && window.innerWidth >= 768) {
                if (searchQuery.value) {
                    isFocused.value = true
                    setInputFocus()
                }
                
                isModalVisible.value = false
            }

        }
    });
}

function inputOnBlur() {
    setTimeout(() => {
        isFocused.value = false
    }, 300);
}

function setInputFocus() {
    if (isModalVisible.value)
        modalSearchInput.value?.focus()
    else
        searchInput.value?.focus()
}

</script>
<template>
    <div class="relative hidden md:block" :class="$attrs.class">
        <div
            :class="['iris-search-container relative transition-all duration-1000 h-10 group',
                        { 
                            'w-0': hoverEffect && !searchQuery && !isFocused && !isHovered, 
                            'w-80': !hoverEffect || searchQuery || isFocused || isHovered
                        }
                    ]"
        >
            <input 
                ref="searchInput"
                :id="clientID"
                :class="[
                    'iris-global-search p-2 absolute top-0 right-0 overflow-hidden transition-all duration-1000 text-header-text bg-header-bg-200 rounded-lg border-header-bg-100 focus:border-header-bg-100 focus:ring-1 focus:ring-header-bg-100 dark:text-header-text-dark dark:bg-bg-color-dark dark:border-header-bg-100-dark placeholder:italic placeholder:text-xs placeholder-header-text-color dark:placeholder-header-text-dark',
                    { 
                        'w-0 opacity-0': hoverEffect && !searchQuery && !isFocused && !isHovered, 
                        'w-80 opacity-100 pr-10 pl-2': !hoverEffect || searchQuery || isFocused || isHovered
                    }
                ]"
                type='text' 
                autocomplete='off' 
                :placeholder="languageHelper.getMessage('search') + '...'"
                maxlength='255' 
                v-model="searchQuery"
                @focus="isFocused = true"
                @blur="inputOnBlur"
                @mouseenter="isHovered = true"
                @mouseleave="isHovered = false"
                @keyup="doSearch"
                @keyup.arrow-down.prevent="selectNextItem"
                @keydown.arrow-up.prevent="selectPreviousItem"
                @keydown.enter.prevent="handleEnterPress"
            />
            <IrisIcon 
                name="magnifying-glass" 
                :class="'cursor-pointer mag-search-icon absolute top-2 transition-all duration-100 text-headet-text ' + (!searchQuery && !isFocused && !isHovered && hoverEffect ? 'right-0 ' : 'right-2 ') + (!searchQuery && !isFocused && !isHovered ? 'opacity-80' : 'opacity-100') " 
                width="1.5rem" height="1.5rem"
                @mouseenter="isHovered = true"
                @mouseleave="isHovered = false"
                @click="focusInput"
            />
        </div>
        <div v-show="searchResults && searchQuery && isFocused && searchQuery.length >= 2"
            class="search-result-list z-10 border-header-bg-100 border divide-y divide-border-muted rounded-lg shadow overflow-hidden w-full dark:border-header-bg-100-dark dark:divide-border-muted-dark"
            :class="{
                'absolute bg-header-bg-200 block w-full dark:bg-header-bg-200-dark' : searchResults.length > 0
            }"
            ref="dropdownContainer">
            <ul class="max-h-56 text-sm overflow-y-auto">
                <li v-for="(item, index) in searchResults" :class="{ 'bg-header-bg dark:bg-header-bg-dark': index === selectedIndex }">
                    <a @click.prevent="selectItem(item.Id)"
                        class="block px-4 py-2 hover:bg-header-bg cursor-pointer dark:hover:bg-header-bg-dark">
                        <span class="block" v-html="highlightKeywordInSearchText(item.Name,searchQuery)"></span>
                        <span class="text-xs">{{ item.EntityLabel }}</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block md:hidden" :class="$attrs.class">
        <IrisIcon 
            name="magnifying-glass" 
            width="1.5rem" height="1.5rem"
            @click="showModal"
            class="cursor-pointer mag-search-icon"
            />
    </div>

    <iris-modal id="globalSearchBox" placement="top-center" :show="isModalVisible" size="full" @onHide="onModalHide">
        <template #content>
            <button type="button"
                @click="onModalHide"
                class="absolute top-1 end-3.5 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center text-text-color-400  hover:bg-bg-color-300 hover:text-text-color dark:hover:bg-bg-color-300-dark dark:hover:text-text-color-dark">
                <IrisIcon name="xmark" class="w-5 h-5"/>
                <span class="sr-only">{{ languageHelper.getMessage('close') }}</span>
            </button>
            <div class="iris-search-container relative my-4 group w-full">
                <input 
                ref="modalSearchInput"
                :id="clientID"
                class='iris-global-search p-2 overflow-hidden text-text-color bg-bg-color rounded-lg border-border-color w-full
                focus:border-border-focus focus:ring-1 focus:ring-border-focus
                    dark:text-text-color-dark dark:bg-bg-color-dark dark:border-border-color-dark 
                    placeholder:italic placeholder:text-xs placeholder:text-text-color-400 dark:placeholder:text-text-color-400-dark'
                type='text' 
                autocomplete='off' 
                :placeholder="languageHelper.getMessage('search') + '...'"
                v-model="searchQuery"
                @keyup="doSearch"
                @keyup.arrow-down.prevent="selectNextItem"
                @keydown.arrow-up.prevent="selectPreviousItem"
                @keydown.enter.prevent="handleEnterPress"
                />
                <IrisIcon 
                    name="magnifying-glass"
                    class="cursor-pointer mag-search-icon absolute right-2 top-2"
                    width="1.5rem" height="1.5rem"
                    @click="focusInput"
                />

                <div v-show="searchResults && searchQuery && searchQuery.length >= 2"
                    class="z-10 border-border-color border divide-y divide-border-muted rounded-lg shadow overflow-hidden w-full dark:border-border-color-dark dark:divide-border-muted-dark"
                    :class="{
                        'absolute bg-bg-color block w-full dark:bg-bg-color-dark' : searchResults.length > 0
                    }"
                    ref="modalDropdownContainer">
                    <ul class="max-h-56 text-sm text-text-color-200 overflow-y-auto dark:text-text-color-200-dark">
                        <li v-for="(item, index) in searchResults" :class="{ 'bg-bg-color-200 dark:bg-bg-color-200-dark': index === selectedIndex }">
                            <a @click.prevent="selectItem(item.Id)"
                                class="block px-4 py-2 hover:bg-bg-color-300 cursor-pointer dark:hover:bg-bg-color-300-dark">
                                <span class="block" v-html="highlightKeywordInSearchText(item.Name,searchQuery)"></span>
                                <span class="text-xs">{{ item.EntityLabel }}</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        
        </template>
    </iris-modal>

    <IrisMessageBox 
        type="Message"
        :message="languageHelper.getMessage('kwLengthLimit')"
        :fullWidthButtons="false"
        :show="showLengthLimitErrorMessage"
        @onPrimaryClick="showLengthLimitErrorMessage=false; setInputFocus()"
        @onCancelClick="showLengthLimitErrorMessage=false; setInputFocus()" />
</template>