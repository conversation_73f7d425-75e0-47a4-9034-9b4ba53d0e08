import { describe, it, expect, vi, beforeAll } from "vitest"
import { VueWrapper, mount } from "@vue/test-utils"
import { createTestingPinia } from '@pinia/testing'
import { FormControlModeEnum, TextboxTypeEnum } from "@/shared/services/form-control-enums"
import IrisTextbox from "@/shared/components/form-controls/IrisTextbox.vue"
import model from '@/shared/assets/data/sample-iris-data.json'
import DataAccess from '@/shared/services/data-access'
import Common from '@/shared/services/common'
import router from '@/router'

describe("IrisTextbox", () => {
    const createWrapper = (props: any): VueWrapper => {
        const wrapper = mount(IrisTextbox, {
            props: { id: Common.newGuid(), value: undefined, field: undefined, ...props },
            global: {
                plugins: [
                    router,
                    createTestingPinia({ createSpy: vi.fn })
                ]
            }
        })

        return wrapper
    }

    beforeAll(() => {
        const dataAccess = new DataAccess()

        dataAccess.addModelExtendedProps(model)
    })

    it("Test component initialization", () => {
        expect(IrisTextbox).toBeTruthy()
    })

    it("Test simple text (no bound)", async () => {
        const props = { value: 'This is a textbox' }
        const wrapper = createWrapper(props)
        const result = (wrapper.find('.iris-textbox').element as HTMLInputElement).value

        expect(result).toEqual('This is a textbox')
    })

    it("Test simple text with label (no bound)", async () => {
        const props = { value: 'This is a textbox', label: 'This is a label' }
        const wrapper = createWrapper(props)
        const result = wrapper.find('.iris-label-inline').html()
        
        expect(result).toContain('This is a label')
    })

    it("Test simple Url (no bound) - check href", async () => {
        const props = { value: 'magentrix.com', type: TextboxTypeEnum.url, mode: FormControlModeEnum.html }
        const wrapper = createWrapper(props)

        await wrapper.vm.$nextTick().then(() => {
            const result = (wrapper.find('.text-primary').element as HTMLInputElement).getAttribute("href")

            expect(result).toEqual('https://magentrix.com')
        })
    })

    it("Test simple Url (no bound)", async () => {
        const props = { value: 'magentrix.com', type: TextboxTypeEnum.url, mode: FormControlModeEnum.html }
        const wrapper = createWrapper(props)
        const result = wrapper.text()
            
        expect(result).toEqual('magentrix.com')
    })

    it("Test with model (bound)", async () => {
        const props = { value: model.role, field: 'Name' }
        const wrapper = createWrapper(props)
        
        await wrapper.vm.$nextTick().then(() => {
            const result = (wrapper.find('.iris-textbox').element as HTMLInputElement).value

            expect(result).toEqual('Administrator')
        })
    })
})