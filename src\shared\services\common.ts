import type { Router } from 'vue-router'
import { LogLevel } from "@/shared/services/enums"
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'

export default class Common {
    static getBaseUrl(): string {
        const systemInfo = useIrisSystemInfoStore().getData()
        const baseUrl = systemInfo.env.production ? '' : systemInfo.env.baseUrl
        return baseUrl
    }

    static getFullUrl(endpoint: string): string {
        var baseUrl = this.getBaseUrl()

        if (!endpoint)
          return baseUrl
    
        const url = baseUrl + (endpoint.startsWith('/') ? '' : '/') + endpoint
    
        return url
    }

    static setPageTitle(title: string): void {
        const systemInfo = useIrisSystemInfoStore().getData()
        document.title = `${title} | ${systemInfo.portalName}`
    }

    static htmlEncode(str: string): string {
        const element = document.createElement('div')
        element.innerText = str
        return element.innerHTML
    }

    static constructUrl(baseURL: string, params: { [key: string]: any }): string {
        const queryString = Object.entries(params)
            .filter(([key, value]) => value !== undefined && value !== null && value !== '')
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    
        return `${baseURL}?${queryString}`;
    }

    static upsertUrlParameters(url: string, params: { key: string, value: string }[]): string {
        if (!url)
            throw new Error("Invalid URL")
        
        if (params == null || params.length < 1)
            throw new Error("No parameters are provided.")

        // Handle both relative and absolute URLs
        const dummyUrl = "http://localhost"
        const isAbsoluteUrl = /^(?:[a-z]+:)?\/\//i.test(url)
        const base = isAbsoluteUrl ? undefined : dummyUrl
        const urlObj = new URL(url, base);
        const queryString = urlObj.search

        // Use URLSearchParams to handle the query string parsing
        const url_params = new URLSearchParams(queryString)

        params.forEach(item => { 
            if (url_params.has(item.key)) {
                if (item.value == "{$remove}")
                    url_params.delete(item.key)
                else
                    url_params.set(item.key, item.value)
            } else if (item.value != "{$remove}")
                url_params.append(item.key, item.value)
        })

        let urlWithoutQueryString = `${urlObj.origin === dummyUrl ? "" : urlObj.origin}${urlObj.pathname}${urlObj.hash}`
        
        if (urlWithoutQueryString.endsWith('/'))
            urlWithoutQueryString = this.removeLastChars(urlWithoutQueryString, 1)
        
        return `${urlWithoutQueryString}${url_params.size > 0 ? '?' : ''}${url_params.toString()}`
    }
    
    static newGuid(): string {
        const result = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        })

        return result
    }

    static extendObject(fullObject: any, partialObject: any): any {
        const result: any = {}

        for (const [key, value] of Object.entries(fullObject))
            result[key] = partialObject[key] == undefined ? value : partialObject[key]

        return result
    }

    static formatFileSize(bytes: number): string {
        var size = 0;
        var unit = 'Bytes'

        if (bytes >= 1073741824)
        {
            size = bytes / 1073741824
            unit = 'GB'
        }
        else if (bytes >= 1048576)
        {
            size = bytes / 1048576
            unit = 'MB'
        }
        else if (bytes >= 1024)
        {
            size = bytes / 1024
            unit = 'KB'
        }
        else if (bytes > 0 && bytes < 1024)
        {
            size = bytes
            unit = 'Bytes'
        }
        
        const formattedSize = parseFloat(size.toFixed(2));
        const result = `${formattedSize} ${unit}`;

        return result
    }

    static numParts(n: number): any {
        // Returns an object consisting of integer and decimal parts of a number
        const s = String(n)
        const p = s.indexOf('.')
        var integer, decimal

        if (p > -1) {
            integer = s.substring(0, p)
            decimal = s.substring(p + 1)
        }
        else {
            integer = s
            decimal = null
        }

        const result = { integer, decimal }

        return result
    }

    static isNumber(value: any, checkType = false): boolean {
        var result = !isNaN(value)

        if (checkType)
            result = typeof value === 'number' && isFinite(value)

        return result
    }

    static isValidEmail(email: string): boolean {
        const pattern = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/
        const result = pattern.test(email)

        return result
    }

    static isValidUrl(url: string): boolean {
        const pattern = /^(http(s)?:\/\/.)[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)$/
        const result = pattern.test(url)

        return result
    }

    static isValidPhone(phone: string): boolean {
        const pattern = /^[0-9ext\s()\.\-\+]+$/
        const result = pattern.test(phone)

        return result
    }

    static toTitleCase(value: string): string {
        if (!value)
            return ''

        var result = value.charAt(0).toUpperCase() + value.slice(1)

        return result
    }

    static padNumber(value: number, size: number, char: string) {
        var result = value.toString()

        if (char.length != 1) {
            console.error('Please specify a single character for padding.')
            return result
        }

        const minSize = 1
        const maxSize = 20

        if (size < minSize || size >> maxSize) {
            console.error(`Please specify a size between ${minSize} and ${maxSize}.`)
            return result
        }

        const pads = size - result.length

        if (pads > 0)
            result = char.repeat(pads) + result

        return result
    }

    static formatAutoNumber(value: number, displayFormatString: string): string {
        if (Number.isNaN(value))
            return ''
        
        const maskStartIndex = displayFormatString.indexOf('{')
        const maskEndIndex = displayFormatString.indexOf('}')
        const mask = displayFormatString.substring(maskStartIndex, maskEndIndex).replace('{', '').replace('}', '')
        const maskColonIndex = mask.indexOf(':')
        const zeros = mask.substring(maskColonIndex + 1)
        const maskedValue = Common.padNumber(value, zeros.length, '0')
        const result = displayFormatString.replace('{', '').replace('}', '').replace(mask, maskedValue)
      
        return result
    }

    static IsIrisPath(path: string, router: Router, caseSensitive: Boolean = false): boolean {
        let _path = Common.removeUrlParameters(path)

        if (_path.endsWith('/') || _path.endsWith('\\'))
            _path = Common.removeLastChars(_path, 1)
        
        const routeInfo = caseSensitive
            ? router.getRoutes().find(x => x.path == _path || x.name == _path)
            : router.getRoutes().find(x => x.path.toLowerCase() == _path.toLowerCase() || x.name?.toString().toLowerCase() == _path.toLowerCase())
        const result = routeInfo ? true : false

        return result
    }

    static includeUrlProtocol(url: string, secure: boolean = true): string {
        let result = url

        if (!result || result.startsWith('//') || result.includes('://'))
            return result

        result = `${secure ? 'https://' : 'http://'}${this.trimSides(url, '/', '', 's')}`

        return result
    }

    static hasDomain(url: string): boolean {
        let parsedUrl = Common.removeUrlParameters(url)
        const result = parsedUrl.includes('.')

        return result
    }

    static removeUrlParameters(path: string): string {
        if (!path)
            return ''

        let parsedUrl = path
        const pos = parsedUrl.indexOf('?')

        if (pos > -1)
            parsedUrl = parsedUrl.substring(0, pos)

        return parsedUrl
    }

    static getRelativePath(url: string): string {
        try {
            const parsedUrl = new URL(url);
            return parsedUrl.pathname + parsedUrl.search;
        } catch (error) {
            throw new Error("Invalid URL provided");
        }
    }

    static isExternalPath(url: string): boolean {
        try {
            // Parse the given URL
            const givenUrl = new URL(url);
            
            // Compare the protocol and hostname of the given URL with the current page's URL
            return givenUrl.origin !== window.location.origin;
        } catch (e) {
            // If the URL constructor throws an error, the URL might be malformed
            return false;
        }
    }

    static trimSides(value: string, lookFor: string, replaceWith: string, mode: string): string {
        // mode: s=start, e=end, b=both
        if (lookFor == ']')
            lookFor = '\\]'
        else if (lookFor == '^')
            lookFor = '\\^'
        else if (lookFor == '\\')
            lookFor = '\\\\'

        let regExp = null
        const startExp = '^[' + lookFor + ']+'
        const endExp = '[' + lookFor + ']+$'
        const bothExp = `${startExp}|${endExp}`

        if (mode == 's')
            regExp = new RegExp(startExp, 'g')
        else if (mode == 'e')
            regExp = new RegExp(endExp, 'g')
        else if (mode == 'b')
            regExp = new RegExp(bothExp, 'g')

        const result = regExp ? value.replace(regExp, replaceWith) : value

        return result
    }

    static async importExternalScript(endpoint: string, defer: boolean = false): Promise<void> {
        return new Promise<void>((resolve) => {
            const script = document.createElement('script')
            const fullUrl = Common.getFullUrl(endpoint)
            const selector = `script[src='${fullUrl}']`
            const found = document.querySelectorAll(selector).length > 0
    
            if (!found) {
                script.setAttribute('src', fullUrl)
                script.setAttribute('language', 'javascript')
    
                if (defer)
                    script.setAttribute('defer', '')
                
                document.head.appendChild(script)
            }
            
            resolve()
        })
    }

    static isNullOrEmpty(value: string | null | undefined): boolean {
        return !(value !== null && value !== undefined && value.trim() !== '');
    }

    static countOccurenceInString(value: string, lookFor: string): number {
        const result = (value.match(new RegExp(lookFor, "g")) || []).length

        return result
    }

    static log(message: string, level: LogLevel = LogLevel.log, obj: any = null, onlyDevMode: boolean = true) {
        const systemInfo = useIrisSystemInfoStore().getData()

        if (onlyDevMode && systemInfo.env.production)
            return

        const extra = obj ? obj : ''

        if (level == LogLevel.log)
            console.log(message, extra)
        else if (level == LogLevel.information)
            console.info(message, extra)
        else if (level == LogLevel.debug)
            console.debug(message, extra)
        else if (level == LogLevel.warning)
            console.warn(message, extra)
        else if (level == LogLevel.error)
            console.error(message, extra)
        else if (level == LogLevel.trace)
            console.trace(message, extra)
    }

    static getBackendHostName(): string {
        const systemInfo = useIrisSystemInfoStore().getData()
        const result = systemInfo.env.production ? window.location.host : new URL(systemInfo.env.baseUrl).hostname
        return result
    }

    static isValidJson(json: string): boolean {
        let result = false

        try {
            const obj = JSON.parse(json)

            if (obj && typeof obj === 'object')
                result = true
        }
        catch {}

        return result
    }

    static tryParseJson(json: string): any {
        let result = null

        try {
            const obj = JSON.parse(json)

            if (obj && typeof obj === 'object')
                result = obj
        }
        catch {}

        return result
    }

    static removeLastChars(text: string, numberOfChars: number): string {
        if (numberOfChars <= 0)
            throw new Error('invalid argument: numberOfChars, should be greater than zero.')

        if (!text || numberOfChars > text.length)
            return ''

        return text.substring(0, text.length - numberOfChars)
    }

    static replaceAll(str: string, searchValue: string, replaceValue: string): string {
        const safeSearchValue = searchValue.replace(/{/g, '\\{').replace(/}/g, '\\}')
        const result = str.replace(new RegExp(safeSearchValue, 'g'), replaceValue)

        return result
    }
    
    static capitalizeFirstLetter(text?: string): string {
        // Return blank if input is undefined or empty
        if (!text)
            return ''
        
        return text.charAt(0).toLocaleUpperCase() + text.slice(1)
    }

    static sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms))
    }

    /**
    * Dynamically adds a CSS file to the <head> tag.
    * @param id - The unique ID for the link element.
    * @param path - The path to the CSS file.
    */
    static addCssFile(id: string, path: string): Promise<void> {
        return new Promise((resolve, reject) => {

            // Check if a link element with the given ID already exists
            if (document.getElementById(id)) {
                console.warn(`CSS file with ID "${id}" already exists.`)
                return
            }

            if (!path) {
                console.warn(`CSS file with ID "${id}" does not have a path identified.`)
                return
            }

            let buildVersion = document.documentElement.getAttribute('data-build')

            if (buildVersion)
                path = `${path}?v=${buildVersion}`

            // Create a new link element
            const linkElement = document.createElement("link")
            linkElement.id = id
            linkElement.rel = "stylesheet"
            linkElement.href = path

            // Handle successful loading of the CSS file
            linkElement.onload = () => {
                // Resolve the promise when the CSS file is loaded
                resolve()
            }

            // Handle errors if the CSS file fails to load
            linkElement.onerror = () => {
                reject(`Failed to load CSS file with ID "${id}"`)
            }

            // Append the link element to the head
            document.head.appendChild(linkElement)
        })
    }

    /**
     * Removes a CSS file from the <head> tag using its ID.
     * @param id - The unique ID of the link element to remove.
    */
    static removeCssFile(id: string): void {
        // Find the link element with the given ID
        const linkElement = document.getElementById(id)

        // If the element exists, remove it
        if (linkElement) 
            linkElement.remove()
        else 
            console.warn(`CSS file with ID "${id}" does not exist.`)
    }

    static openHelpWindow(url: string, options?: { 
        name?: string;
        width?: number;
        height?: number;
        left?: number;
        top?: number;
    }): Window | null {
        const defaults = {
            name: 'window',
            width: 880,
            height: 990,
            left: 10,
            top: 10
        }

        const helpUrl = 'https://help.magentrix.com'
        const settings = { ...defaults, ...options }
        const fullUrl = url.startsWith('http') ? url : `${helpUrl}${url.startsWith('/') ? '' : '/'}${url}`

        return window.open(
            fullUrl,
            settings.name,
            'left=' + settings.left + 
            ',top=' + settings.top + 
            ',height=' + settings.height + 
            ',width=' + settings.width + 
            ',scrollbars=yes,status=yes,toolbar=no,menubar=no,location=no,resizable=yes'
        )
    }
}