<script setup lang="ts">
import { nextTick, onMounted, ref, watch, type PropType } from 'vue'
import { Collapse, Dropdown, type CollapseInterface, type CollapseOptions, type DropdownOptions, type InstanceOptions } from 'flowbite'
import Common from '@/shared/services/common'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

const props = defineProps({
    value: {
        type: Array as PropType<Array<MenuItem>>,
        required: true
    },
    logoUrl: {
        type: String,
        required: false
    },
    logoClickUrl : {
        type: String,
        required: false
    },
    useIcons: {
        type: Boolean,
        default: false
    },
    hasBg: {
        type: Boolean,
        default: false
    },
    stacked: {
        type: Boolean,
        default: false
    },
    isMenuLeftAligned:{
        type: Boolean,
        default: true
    }
})

interface MenuItem {
  link: string;
  target: string;
  text: string;
  icon: string;
}

type OverflowNavsOptions = {
  more: string;
  parent: string;
  offset: number;
  flagEnlarge : boolean;
};

class OverflowNavs {
    options: OverflowNavsOptions;
    ul: HTMLElement;
    collapseBtn: HTMLElement;
    $dropdown : Dropdown | undefined;

    constructor(ul: HTMLElement, collapseBtn: HTMLElement, options: Partial<OverflowNavsOptions> = {}) {
        this.options = { more: 'More', parent: '', offset: 0, flagEnlarge: false, ...options }
        this.ul = ul
        this.collapseBtn = collapseBtn
        this.init()
    }

    init() {
        const ul = this.ul;
        const options = this.options;
        const parent = options.parent
            ? document.querySelector(options.parent)
            : ul.parentElement
        const offset = options.offset || 35;

        if (!parent) 
            return;

        const isCollapsed = this.isVisible(this.collapseBtn)
        var dropdown = ul.querySelector('.overflow-nav')
        
        if (!isCollapsed && !dropdown)
            dropdown = this.createDropdown();

        const dropdownMenu = dropdown?.querySelector('.dropdown-menu');

        if (isCollapsed && !dropdownMenu?.childNodes.length && window.innerWidth < 768) 
            return;

        const parentWidth = (parent as HTMLElement).offsetWidth - offset

        if (isCollapsed) {
            Array.from(dropdownMenu?.childNodes || []).forEach((child) => {
                const originalIndex = (child as HTMLElement).dataset.originalIndex;
                if (originalIndex) {
                    const index = parseInt(originalIndex);
                    const referenceNode = ul.children[index];
                    ul.insertBefore(child, referenceNode);
                }
            });
        } else {

            if (ul.scrollWidth > parentWidth) {
                if (options.flagEnlarge && !toggleToFullMenu) {
                    let totalWidth = 0;
                    Array.from(ul.children).reverse().forEach((li) => {
                        if (!li.classList.contains('dropdown') && !li.classList.contains('dropdown-item')) {
                            (li as HTMLElement).dataset.originalIndex = `${Array.from(ul.children).indexOf(li)}`
                            // Add the offsetWidth of each `li` element to totalWidth
                            totalWidth += (li as HTMLElement).offsetWidth
                        }
                    })

                    const child = dropdownMenu?.firstElementChild as HTMLElement

                    if (child)  {
                        const originalIndex = (child as HTMLElement).dataset.originalIndex
                        let childWidth = parseInt((child as HTMLElement).dataset.originalWidth || "0")

                        if (originalIndex && totalWidth + childWidth < parentWidth) {
                            const index = parseInt(originalIndex)
                            const referenceNode = ul.children[index]
                            ul.insertBefore(child, referenceNode)
                            totalWidth = totalWidth + childWidth
                        } else {
                            return false
                        }
                    }
                } else {
                    while (ul.scrollWidth > parentWidth) {
                        const liElements = Array.from(ul.children).reverse()
                        const li = liElements.find((li) => 
                            !li.classList.contains('dropdown') && !li.classList.contains('dropdown-item')
                        ) as HTMLLIElement

                        if (li) {
                            
                            if (ul.scrollWidth > parentWidth) {
                                li.dataset.originalIndex = `${Array.from(ul.children).indexOf(li)}`
                                li.dataset.originalWidth = `${li.offsetWidth}`
                                dropdownMenu?.prepend(li)
                            }
                        }
                    }

                    if (ul.scrollWidth <= parentWidth)
                        toggleToFullMenu = false
                }
            } else {
                const dropdownChildren = Array.from(dropdownMenu?.childNodes || [])

                for (const child of dropdownChildren) {
                    const originalIndex = (child as HTMLElement).dataset.originalIndex

                    if (originalIndex && ul.scrollWidth + parseInt((child as HTMLElement).dataset.originalWidth || "0", 10) < parentWidth) {
                        const index = parseInt(originalIndex)
                        const referenceNode = ul.children[index]
                        ul.insertBefore(child, referenceNode)
                    } else
                        break
                }
            }
        }

        if (!dropdownMenu?.childNodes.length) {
            if (this.$dropdown)
                this.$dropdown.destroy()

            dropdown?.remove();
        } else {
            if (!ul.querySelector('.overflow-nav') && dropdown)
                ul.appendChild(dropdown)
        }
    }

    createDropdown() {
        const dropdown = document.createElement('li');
        dropdown.classList.add('overflow-nav', 'dropdown', 'relative', 'right-align-dropdown');

        const dropdownToggle = document.createElement('button');
        const baseClasses = [
            'dropdown-toggle',
            'flex',
            'items-center',
            'whitespace-nowrap',
            'justify-between',
            'h-full',
            'p-4',
            'text-nav-text',
            'opacity-80',
            'hover:opacity-100',
            'dark:text-nav-text-dark',
            'md:w-auto'
        ];

        // Conditionally add classes
        if (props.hasBg) {
            baseClasses.push(
                'hover:bg-nav-bg-hover',
                'dark:text-nav-text-dark',
                'dark:hover:bg-nav-bg-hover-dark',
                'dark:focus:bg-nav-bg-active-dark',
                'focus:bg-nav-bg-active'
            );
        }
        
        dropdownToggle.classList.add(...baseClasses);
        dropdownToggle.setAttribute('type', 'button');
        dropdownToggle.setAttribute('id', 'btndd' + id)
        dropdownToggle.setAttribute('data-dropdown-toggle',"ddnav-" + id)
        dropdownToggle.innerHTML = `${this.options.more}<svg class="h-full w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>`;
        dropdown.appendChild(dropdownToggle);

        const dropdownMenuWrapper = document.createElement('div')
        dropdownMenuWrapper.setAttribute("id", "ddnav-" + id)
        dropdownMenuWrapper.classList.add('z-10', 'right-0','hidden', 
            'font-normal', 'bg-nav-bg', 'divide-y', 'divide-border-muted', 
            'border', 'border-border-color', 'dark:border-border-color-dark',
            'dark:bg-nav-bg-dark', 'dark:divide-border-muted-dark', 'rounded-lg', 'shadow', 'overflow-hidden');
        dropdown.appendChild(dropdownMenuWrapper);

        const dropdownMenu = document.createElement('ul');
        dropdownMenu.classList.add('dropdown-menu', 'py-2', 'text-sm', 'text-text-color-400', 'dark:text-text-color-400-dark');
        dropdownMenu.setAttribute("aria-labelledby", 'btndd' + id)
        dropdownMenuWrapper.appendChild(dropdownMenu);

        setTimeout(() => {
            const instanceOptions: InstanceOptions = { id: "ddnav-" + id, override: true }
            const options : DropdownOptions = {
                placement: 'bottom',
                triggerType: 'click',
                offsetSkidding: 0,
                offsetDistance: 4,
                onShow: () => {
                    window.scrollBy(0, 1)
                    window.scrollBy(-1, 0)

                    setTimeout(() => {
                        window.scrollBy(0, -1)
                        window.scrollBy(1, 0);
                    }, 150)
                }
            }

            this.$dropdown = new Dropdown(dropdownMenuWrapper, dropdownToggle, options, instanceOptions)
        }, 2000);
        
        return dropdown;
    }

    isVisible(element: HTMLElement): boolean {
        if (!element)
            return false;

        // Check if element is attached to the DOM
        if (!element.offsetParent) 
            return false;

        // Check offsetWidth and offsetHeight
        if (element.offsetWidth === 0 && element.offsetHeight === 0) 
            return false;

        // Check computed styles
        const style = window.getComputedStyle(element);

        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') 
            return false;

        return true;
    }
}

const $ulNav = ref<HTMLElement>()
const $triggerEl = ref<HTMLElement>()
const $targetEl = ref<HTMLElement>()
const id = Common.newGuid()
let screenWidth = window.innerWidth
let previousScreenWidth = window.innerWidth
const isMenuOpen = ref(false)
let toggleToFullMenu = true
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

watch(isMenuOpen, (newValue) => {
  if ($triggerEl.value) {
    $triggerEl.value.setAttribute('aria-expanded', newValue.toString());
  }
});

watch(() => [
    props.value
], () => {
    nextTick(() => {
        hookup()
    });
})

onMounted(() => {
    if ($triggerEl.value)
        isMenuOpen.value = $triggerEl.value.getAttribute('aria-expanded') === 'true'

    const options: CollapseOptions = {
        onCollapse: () => {
        },
        onExpand: () => {
        },
        onToggle: () => {
        }
    }

    const collapse: CollapseInterface = new Collapse(
        $targetEl.value,
        $triggerEl.value,
        options);
    
    setTimeout(() => {
        hookup()    
    }, 500);
    
    window.addEventListener('resize', onResize);
})

function onResize() {
    if (previousScreenWidth < window.innerWidth && previousScreenWidth < 768 && window.innerWidth >= 768)
        toggleToFullMenu = true

    previousScreenWidth = window.innerWidth

    nextTick(() => {
        hookup()
    });
}

function hookup() {
    const currentWidth = window.innerWidth
    let flagEnlarge = false

    if (currentWidth > screenWidth)
        flagEnlarge = true
    
    if ($ulNav.value) {
        screenWidth = window.innerWidth
        new OverflowNavs($ulNav.value!, $triggerEl.value!, { more: '', parent: '', offset: 60, flagEnlarge: flagEnlarge })
    }
}
</script>
<template>
    <nav :class="(logoUrl ? 'w-full' : '')" v-if="props.value">
        <div class="flex flex-col md:flex-row items-center mx-auto" :class="(logoUrl ? 'justify-between w-full' : 'justify-end max-w-page')">
            <div class="flex w-full md:w-auto" :class="(logoUrl ? 'justify-between' : 'justify-end')">
                <a :href="logoClickUrl" class="flex items-center ml-4 flex-shrink-0" v-if="logoUrl">
                    <img :src="logoUrl" class="max-h-16 max-w-32 sm:max-w-52" alt="Logo" crossorigin="anonymous" />
                </a>
                <button ref="$triggerEl" type="button" :aria-expanded="isMenuOpen ? 'true' : 'false'" @click="toggleMenu" 
                    class="inline-flex items-center p-4
                        justify-center text-sm rounded-lg md:hidden
                        text-nav-text opacity-80 hover:opacity-100 dark:text-nav-text-dark"
                    :class="(hasBg ? 'hover:bg-nav-bg-hover dark:hover:bg-nav-bg-hover-dark' : '')">
                    <span class="sr-only">Open main menu</span>
                    <IrisIcon v-show="!isMenuOpen" name="colapse_menu" class="w-5 h-5" width="1.5rem" height="1.5rem" aria-hidden="true" fill="none"/>
                    <IrisIcon v-show="isMenuOpen" name="xmark" class="w-5 h-5" width="1.5rem" height="1.5rem" aria-hidden="true" fill="none"/> 
                </button>
            </div>
            <div ref="$targetEl" class="hidden md:block flex-grow min-w-0 w-full">
                <ul ref="$ulNav" class="font-medium flex flex-col md:flex-row sm-shadow-inset pt-2 md:mt-0 md:pt-0"
                    :class="(isMenuLeftAligned ? 'md:float-left' : 'md:float-right' )">
                    <li v-for="(menu, index) in value" :key="index" class="whitespace-nowrap">
                        <IrisIcon :name="menu.icon" v-if="useIcons"/>
                        <IrisLink 
                            :path="menu.link" 
                            :target="menu.target" 
                            class="text-nowrap block px-4 py-2 md:py-4 text-nav-text opacity-80 hover:opacity-100 dark:text-nav-text-dark "
                            :class="(hasBg ? 'hover:bg-nav-bg-hover dark:hover:bg-nav-bg-hover-dark' : '')"
                            aria-current="page">
                            {{ menu.text }}
                        </IrisLink>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</template>

<style scoped>
.menu-logo-sm {
    display: none;
}
.overflow-nav.dropdown li > a {
    padding: 0.5rem 0.75rem;
}
.right-align-dropdown .dropdown-menu {
  left: auto;
  right: 0;
}
@media(max-width:768px) {
    .menu-logo-sm {
        display: block;
    }
    .sm-shadow-inset {
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
}
</style>