import { describe, it, expect, beforeAll } from "vitest"
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from "pinia"
import { useIrisSystemInfoStore } from "@/shared/stores/system-info-store"
import { RequestMethod } from "@/shared/services/enums"
import Auth from "@/shared/services/auth"
import DataAccess from "@/shared/services/data-access"
import type SystemInfoData from "@/shared/services/system-info-data"

describe("DataAccess", async () => {
  let systemInfo: SystemInfoData
  let authResponse: any
  let lastCreatedId = ""
  let dataAccess: DataAccess

  beforeAll(async () => {
    const pinia = createPinia()
    setActivePinia(pinia)
    systemInfo = useIrisSystemInfoStore().getData()
    authResponse = await Auth.createSession(systemInfo.env.refreshToken)

    if (authResponse.success)
      dataAccess = new DataAccess()
  })
    
  it("query", async () => {
    const result = await dataAccess.query('SELECT Name FROM Car__c WHERE Id="07O0000000000gh0000"')
    
    expect(result.data[0].Name).toEqual("Lexus")
  })

  it("execute - Ping", async () => {
      const result = await dataAccess.execute("/iris/ping", null, RequestMethod.get)

      expect(result).toEqual("Pong")
  })

  it("execute - GetSampleIrisData", async () => {
      const result = await dataAccess.execute("/iris/getsampleirisdata", null, RequestMethod.get)
      
      expect(result.role.Name).toEqual("Administrator")
  })

  it("retrieve", async () => {
    const result = await dataAccess.retrieve("07O0000000000gh0000")
    
    expect(result.record.Name).toEqual("Lexus")
  })

  it("create", async () => {
    const data = {
      "Name": "Honda Civic",
      "Brand__c": "07N00000000001C0000",
      "Year__c": "2023",
      "In_Stock__c": true,
      "Electric__c": false,
      "Manual__c": false,
      "External_URL__c": "Test"
    }
    const response = await dataAccess.create("Car__c", data)
    const result = response.success == true && response.id.length == 19

    if (result == true)
      lastCreatedId = response.id

    expect(result).toEqual(true)
  })

  it("edit", async () => {
    const data = {
      "Id": lastCreatedId,
      "Name": "Honda Civic 2"
    }
    const response = await dataAccess.edit("Car__c", data)
    const result = response.success == true && response.id.length == 19

    expect(result).toEqual(true)
  })

  it("upsert", async () => {
    // Create
    const data = {
      "Name": "Honda Civic (Created)",
      "Brand__c": "07N00000000001C0000",
      "Year__c": "2023",
      "In_Stock__c": true,
      "Electric__c": false,
      "Manual__c": false,
      "External_URL__c": "Test"
    }
    const createResponse = await dataAccess.upsert("Car__c", data)
    const createResult = createResponse.success == true && createResponse.id.length == 19

    expect(createResult).toEqual(true)

    // Edit
    data.Name = "Honda Civic (Updated)"
    const editResponse = await dataAccess.upsert("Car__c", data)
    const editResult = editResponse.success == true

    expect(editResult).toEqual(true)

    // Delete
    const newId = createResponse.id
    const deleteResponse = await dataAccess.delete("Car__c", newId, true)
    const deleteResult = deleteResponse.success == true

    expect(deleteResult).toEqual(true)
  })

  it("delete", async () => {
    const response = await dataAccess.delete("Car__c", lastCreatedId, true)
    const result = response.success == true

    expect(result).toEqual(true)
  })

  it("deleteMany", async () => {
    const data = {
      "Name": "Honda Civic",
      "Brand__c": "07N00000000001C0000",
      "Year__c": "2023",
      "In_Stock__c": true,
      "Electric__c": false,
      "Manual__c": false,
      "External_URL__c": "Test"
    }
    const json = JSON.stringify(data)
    const response1 = await dataAccess.create("Car__c", JSON.parse(json))
    const response2 = await dataAccess.create("Car__c", JSON.parse(json))
    const response3 = await dataAccess.create("Car__c", JSON.parse(json))
    const ids = [ response1.id, response2.id, response3.id ]

    const response = await dataAccess.deleteMany('Car__c', ids, true)
    var result = true
    
    response.forEach((x: any) => result = result && !x.HasError)

    expect(result).toEqual(true)
  })
})
