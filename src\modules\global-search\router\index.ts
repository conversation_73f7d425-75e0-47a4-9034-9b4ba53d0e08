import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'

const routes = [
    {
        path: '/global-search',
        name: 'global-search',
        component: () => import('@/modules/global-search/views/Index.vue'),
        meta: { requiresAuth: false },
        beforeEnter: (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
            const systemInfo = useIrisSystemInfoStore().getData()
            if (systemInfo.company.system.globalSearch)
                next()
            else
                next({ path: 'not-found' })
        }
    }
]

export default routes