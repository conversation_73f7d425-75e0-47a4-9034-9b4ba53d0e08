<script setup lang="ts">
import { onMounted, ref } from 'vue';
import Svg from '@/shared/assets/data/svg.json'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'

type IconObject = {
  [key: string]: {
    keywords?: string;
    viewBox: string;
    content: string;
  }
}

const clickedItem = ref<string>()
const searchQuery = ref<string>()
const items = ref<string[]>()

onMounted(() => {
    items.value = Object.keys(Svg)
})

function copyToClipboard(textToCopy : string) {
    navigator.clipboard.writeText(textToCopy).catch(err => {
        console.error('Failed to copy text: ', err);
    });
}

function itemClicked(textToCopy : string) {
    clickedItem.value = textToCopy

    setTimeout(() => {
        clickedItem.value = '';
      }, 1000);

    copyToClipboard(textToCopy)
}

const filterIcons = (icons: IconObject, searchQuery: string): string[] => {
  const query = searchQuery.toLowerCase();

  return Object.keys(icons).filter(name => {
    const data = icons[name];
    const matchesName = name.toLowerCase().includes(query);
    const matchesKeywords = data.keywords?.toLowerCase().includes(query) || false;
    return matchesName || matchesKeywords;
  })
}

function filterItems() {
    if (!searchQuery.value || searchQuery.value.length <= 2)
        items.value = Object.keys(Svg)
    else {
        items.value = filterIcons(Svg, searchQuery.value)
    }
}
</script>

<template>
<div>
    <div class="flex items-center mb-4">
        <div class="flex-1">
            <h1 class="text-4xl leading-none tracking-tight block">Icons</h1>
        </div>
        <div class="flex-1 text-right">
            <div class="w-80 float-right">
                <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                <div class="relative">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input v-model="searchQuery" 
                        type="search"
                        id="default-search"
                        class="block w-full p-4 ps-10 text-sm rounded-lg iris-textbox" 
                        placeholder="Search icons..." 
                        required
                        @keyup.enter.prevent="filterItems" />
                    <button @click="filterItems" type="button" class="absolute end-2.5 bottom-2.5 font-medium rounded-lg btn-primary px-4 py-2">Search</button>
                </div>
            </div>
        </div>
    </div>

    <div class="border-b mb-4"></div>
    
    <div class="flex flex-row flex-wrap mb-5">
        <a @click="itemClicked(icon)" 
        :title="icon" class="block p-3" 
        :class="{'text-yellow-300': clickedItem === icon}"
        v-for="icon in items">
            <IrisIcon :name="icon" width="1.7rem" height="1.7rem" />
        </a>
    </div>
    
</div>
</template>