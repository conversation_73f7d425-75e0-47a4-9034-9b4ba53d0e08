<script setup lang="ts"> 
import { ref, onMounted, computed } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import { GridColumn } from '@/shared/services/grid-column'
import { GridAction, GridActionMode } from '@/shared/services/grid-action'
import { useIrisPage } from '@/shared/composables/iris-page'
import DataAccess from '@/shared/services/data-access'
import Common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisPagination from '@/shared/components/general-controls/IrisPagination.vue'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import IrisField from '@/shared/components/form-controls/IrisField.vue'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisValidationSummary from '@/shared/components/form-controls/IrisValidationSummary.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'

/* --- Props --- */
const props = defineProps({
  enablePagination: {
    type: Boolean,
    default: true,
  },
  size: {
    type: Number,
    default: 10,
  }
})

/* --- Constants and Refs --- */
const languageHelper = new LanguageHelper()
const base = useIrisPage()
const dataAccess = new DataAccess()
const benefits = ref([])
const columns: GridColumn[] = []
const isModelVisible = ref(false)
const benefitModel: any = ref(null)
const isSubmitting = ref(false)
const isLoading = ref(false)
const isModalLoading = ref(false)
const formErrors = ref<string[]>([])
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')
const gridActions = ref<GridAction[]>()
const totalRows = ref(0)
const pageIndex = ref(0)
const showDeleteMessageBox = ref(false)
const pendingDeleteId = ref<string | null>(null)
const originalBenefitName = ref<string>('')
const searchTerm = ref('')

gridActions.value = []
gridActions.value!.push({ label:languageHelper.getMessage("edit"), path:"Edit", type: GridActionMode.Button , icon:"pencil"} as GridAction)
gridActions.value!.push({ label:languageHelper.getMessage("delete"), path:"Delete", type: GridActionMode.Button , icon:"trash-can" } as GridAction)

/* --- Lifecycle Hooks --- */
onMounted(async () => {
  base.setPageTitle(languageHelper.getMessage('partnerProgram'))

  try {
    columns.push(
        GridColumn.create({ name: 'Name', sortExpression: 'Name', sortDirection: 'A', field: 'Name', component: "IrisGridLink", data: { urlPattern: '/setup/partner-program/benefits/{!Id}' }  }),
        GridColumn.create({ name: 'BenefitType', sortExpression: 'BenefitType' }),
        GridColumn.create({ name: 'Description', sortExpression: 'Description' }),
        GridColumn.create({ name: 'CreatedOn', sortExpression: 'CreatedOn', sortDirection: 'D', selected: true, field: 'CreatedOn', type: 'date' })
      )
    
    await loadData('CreatedOn', 'D', 0, '')

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failInitialize") 
  }
})

/* --- Computed --- */
const isEditMode = computed(() => Boolean(benefitModel.value?.Id))

/* --- Functions --- */
function onActionClicked(actionName: string, data: any) {
  if (actionName === 'Edit') {
    originalBenefitName.value = data.Name
    benefitModel.value = { ...data }
    isModelVisible.value = true
  } else if (actionName === 'Delete') {
    pendingDeleteId.value = data.Id
    showDeleteMessageBox.value = true
  }
}

async function loadData(sortExpr: string, sortDirection: string, pageIndex: number, keyword: string = '') {
  isLoading.value = true
  showErrorMessage.value = false
  try {
    const endpoint = `/partnerprogrambenefit/getbenefits`
    const data: any = {
      sortExpression: sortExpr,
      sortDirection: sortDirection,
      keyword: keyword.trim()
    }

    if (props.enablePagination) {
        data.size = props.size
        data.pinx = pageIndex
    } else {
        data.size = 0
        data.pinx = 0
    }

    const url = Common.constructUrl(endpoint, data)
    const response = await dataAccess.execute(url, RequestMethod.post)

    if (!response) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
      isLoading.value = false
      return
    }

    if (!response.benefits || !response.totalRows || !response.benefits.length) {
      benefits.value = []
      isLoading.value = false
      return
    }

    totalRows.value = response.totalRows
    benefits.value = response.benefits || []

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
    benefits.value = []
  } finally {
    isLoading.value = false
  }
}

function doSearch(): void 
{
  loadData(
    columns.find(c => c.selected)!.sortExpression,
    columns.find(c => c.selected)!.sortDirection,
    pageIndex.value,
    searchTerm.value
  )
}

async function onConfirmDelete() {

  if (!pendingDeleteId.value) 
    return

  try {
    const payload = {
      benefitId: pendingDeleteId.value
    }
    pendingDeleteId.value = null
    
    const result = await dataAccess.execute(
      `/partnerprogrambenefit/deletebenefit`,
      payload
    )

    showDeleteMessageBox.value = false

    if (!result?.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failDelete")
    }

    await loadData(
      columns.find(c => c.selected)!.sortExpression,
      columns.find(c => c.selected)!.sortDirection,
      pageIndex.value,
      searchTerm.value
    )
  } catch {
    showErrorMessage.value   = true
    serverErrorMessage.value = languageHelper.getMessage('failDelete')
  }
}

function onCancelDelete() {
  pendingDeleteId.value = null
  showDeleteMessageBox.value = false
}

async function saveBenefit() {
  formErrors.value = []
  isSubmitting.value = true

  if (!benefitModel.value.Name || !benefitModel.value.Name?.trim()) {
    formErrors.value.push(languageHelper.getMessage("benefitNameRequired"))
    isSubmitting.value = false
    return
  }

  await validateBenefitName(benefitModel.value.Name.trim())
  await validateBenefitType()

  if (formErrors.value.length) {
    isSubmitting.value = false
    return
  }

  try {
    const payload = {
      benefit: benefitModel.value
    }

    const endpoint = isEditMode.value
      ? `/partnerprogrambenefit/updatebenefit`
      : `/partnerprogrambenefit/createbenefit`

    const response = await dataAccess.execute(endpoint, payload)

    if (!response.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failSave")
    }

    await loadData(columns.find((c) => c.selected)!.sortExpression, 
      columns.find((c) => c.selected)!.sortDirection,
       pageIndex.value,
      searchTerm.value)

    onModalHide()

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failSave")
  } finally {
    isSubmitting.value = false
  }
}

async function validateBenefitType() {
  formErrors.value = formErrors.value.filter(e => !e.includes(languageHelper.getMessage("benefitTypeError")))

  if (!benefitModel.value.BenefitType)
    formErrors.value.push(languageHelper.getMessage("benefitTypeError"))

}

async function validateBenefitName(raw: Event | string) {
  let name: string

  if (typeof raw === 'string') 
    name = raw
   else if (raw && typeof (raw as any).target?.value === 'string') 
    name = (raw as any).target.value
   else 
    return

  name = name.trim()

  if (!name) 
    return

  if (isEditMode.value && name === originalBenefitName.value) {
    const dupMsg = languageHelper.getMessage("benefitDuplicateNameError")
    formErrors.value = formErrors.value.filter(e => !e.includes(dupMsg))
    return
  }

  try {
    formErrors.value = []

    const endpoint = `/partnerprogrambenefit/checkbenefitbyname`
    const data: any = {
      name: name.trim()
    }
    const url = Common.constructUrl(endpoint, data)
    const result = await dataAccess.execute(url, RequestMethod.get)

    if (!result) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
      return
    }

    if (result.duplicate) 
      formErrors.value.push(languageHelper.getMessage("benefitDuplicateNameError"))
    else 
      formErrors.value = formErrors.value.filter(error =>
        !error.includes(languageHelper.getMessage("benefitDuplicateNameError"))
      )
    
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
  }
}

async function showModal() {
  try {
    isModalLoading.value = true
    formErrors.value = []
    isSubmitting.value = false

    await getNewBenefit()

    isModelVisible.value = true
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
  } finally {
    isModalLoading.value = false
  }
}

function onModalHide() {
  isModelVisible.value = false
  benefitModel.value = null
  formErrors.value = []
}

async function getNewBenefit() {
  try {
    const data = await dataAccess.execute('partnerprogrambenefit/getnewbenefit')

    if (!data || !data.benefit) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      return
    }

    benefitModel.value = data.benefit
  } catch  {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
  }
}

async function sortBenefit(col: GridColumn) {
  const selectedCol = columns.find((column) => column.name == col.name)

  if (selectedCol) {
    columns.forEach((c) => (c.selected = false))
    selectedCol.selected = true
    selectedCol.sortDirection = col.sortDirection

    await loadData(
      selectedCol.sortExpression,
      selectedCol.sortDirection, 
      pageIndex.value, 
      searchTerm.value)
  }
}

function onPageChanged(newPage: number) {
  pageIndex.value = newPage
  loadData(columns.find((c) => c.selected)!.sortExpression,
    columns.find((c) => c.selected)!.sortDirection, pageIndex.value, searchTerm.value)
}

</script>
<template>
  <partner-program-setup-nav selected-tab="benefits" />
  <div class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark rounded">
    <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
      {{ serverErrorMessage }}
    </iris-alert>
    <h2 class="text-xl font-semibold mb-4">{{ languageHelper.getMessage("benefits") }}</h2>
    <div class="flex justify-between items-center mb-4">
        <input
          id="searchInput"
          v-model="searchTerm"
          @keyup.enter="doSearch"
          type="text"
          :placeholder="languageHelper.getMessage('search') + '...'"
                class='p-2 overflow-hidden text-text-color bg-bg-color rounded-lg border-border-color
                  focus:border-border-focus focus:ring-1 focus:ring-border-focus
                      dark:text-text-color-dark dark:bg-bg-color-dark dark:border-border-color-dark 
                      placeholder:italic placeholder:text-xs placeholder:text-text-color-400 dark:placeholder:text-text-color-400-dark'
        />
        <button @click="showModal"
          class="inline-flex btn-primary"
          type="button">
          {{ languageHelper.getMessage("newBenefit") }}
        </button>
    </div>
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
    <iris-grid 
      id="benefits" 
      v-if="benefits && benefits.length > 0" 
      :value="benefits"
      :cols="columns" 
      :actions="gridActions"
      @on-sort="sortBenefit"
      @actionClicked="onActionClicked">
    </iris-grid>
    <div v-else class="text-text-color-400 dark:text-text-color-400-dark">
      <span>{{ languageHelper.getMessage('noRecords') }}</span>
    </div>
    <div class="mt-5" v-if="enablePagination && totalRows > props.size">
      <iris-pagination v-if="enablePagination"
          :page-changed="onPageChanged"
          :total="totalRows"
          :page-size="props.size" 
          :current-page="pageIndex"
          :use-icons-only="true"
          :next-and-previous-only="false" 
          :size="'sm'"
          :first-btn-lbl='languageHelper.getMessage("first")'
          :last-btn-lbl='languageHelper.getMessage("last")'
          :next-btn-lbl='languageHelper.getMessage("next")'
          :prev-btn-lbl='languageHelper.getMessage("previous")'>
      </iris-pagination>
    </div>
  </div>
  <iris-modal 
    id="benefit-modal" 
    :show="isModelVisible" 
    :title='isEditMode ? languageHelper.getMessage("editBenefit") : languageHelper.getMessage("addBenefit")'
    @onHide="onModalHide"
    >
    <template #content>
      <div v-if="isModalLoading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
      <iris-form id="benefitForm" v-else-if="benefitModel">
        <iris-validation-summary formId="benefitForm" />
        <!-- Error Messages -->
        <iris-alert v-if="formErrors.length > 0" type="danger" class="mb-4">
            <ul class="list-disc list-inside">
            <li v-for="(error, index) in formErrors" :key="index">{{ error }}</li>
          </ul>
        </iris-alert>
        <div class="grid gap-4 mb-4 grid-cols-2">
          <div class="col-span-2">
            <iris-field id="benefitName" :value="benefitModel" field="Name" required
              @input="validateBenefitName" @update:value="validateBenefitName"/>
          </div>
          <div class="col-span-2">
            <iris-picklist id="benefitType" :value="benefitModel" field="BenefitType" required
            @onChange="validateBenefitType" @update:value="validateBenefitType" :isNullable="false"/>
          </div>
          <div class="col-span-2">
            <iris-textbox id="benefitDescription" :value="benefitModel" field="Description" />
          </div>
        </div>
      </iris-form>
    </template>
    <template #footer>
      <div class="flex justify-end space-x-3">
        <button 
          type="button"
          class="btn-light"
          @click="onModalHide">
          {{ languageHelper.getMessage("cancel") }}
        </button>
        <button 
          type="submit" 
          form="benefitForm"
          class="btn-primary"
          :disabled="isSubmitting || formErrors.length>0"
          @click="saveBenefit">
          <span v-if="isSubmitting">
            {{ languageHelper.getMessage("saving") }}
          </span>
          <span v-else>
            {{ isEditMode ? languageHelper.getMessage("save") : languageHelper.getMessage("addBenefit") }}
          </span>
        </button>
      </div>
    </template>
  </iris-modal>
  <iris-message-box
    type="Confirm" 
    :message="languageHelper.getMessage('deleteBenefitMessage')"
    :show="showDeleteMessageBox"
    :title="languageHelper.getMessage('deleteBenefit')" 
    :primaryButtonLabel="languageHelper.getMessage('delete')"
    :cancelButtonLabel="languageHelper.getMessage('cancel')"
    :icon="'trash-can'"
    :size="'small'"
    @onPrimaryClick="onConfirmDelete"
    @onCancelClick="onCancelDelete" />
</template>