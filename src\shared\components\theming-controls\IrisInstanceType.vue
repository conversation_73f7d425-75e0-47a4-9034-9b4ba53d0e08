<script setup lang="ts">
import { type PropType } from 'vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import LanguageHelper from '@/shared/services/language-helper';

interface InstanceData {
    instanceName: string;
    instanceType: string;
    portalEmailEnabled: boolean
}

const props = defineProps({
    value: {
        type: Object as PropType<InstanceData>,
        required: true
    }
})
const languageHelper = new LanguageHelper()

function getTooltipContent() {
  return languageHelper.getMessage('emailsDisabled');
}

function navigateToHelp() {
  window.location.href = 'https://help.magentrix.com/wikis/help/configuring-system-settings';
}
</script>

<template>
    <div v-if="value" :class="$attrs.class"
        class="sys-instance-type px-2 py-1 hidden md:flex items-center text-xs max-w-64
        bg-red-600 text-white rounded overflow-hidden truncate relative"
    >
        <div v-if="!value.portalEmailEnabled" class="inline-flex pr-2 mr-2 border-r border-white cursor-pointer">
            <div
            class="relative inline-block"
            @click="navigateToHelp"
            :title="getTooltipContent()"
            >
            <span class="flex bg-red-600 rounded z-20 absolute top-4 -right-1 ban-icon">
                <IrisIcon name="ban" aria-hidden="true" height="0.7rem" width="0.7rem"/>
            </span>
                <IrisIcon name="mail" aria-hidden="true" height="1.5rem" width="1.5rem"/>
            </div>
        </div>
        <div class="sys-instance-name inline-block overflow-hidden truncate"
        :title="value.instanceName">
            {{ value.instanceName }}
        </div>
        
    </div>

</template>