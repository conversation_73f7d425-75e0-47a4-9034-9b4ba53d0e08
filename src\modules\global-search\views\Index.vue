<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { RequestMethod } from '@/shared/services/enums'
import { Drawer } from 'flowbite'
import { useIrisSystemInfoStore } from '@/shared/stores/system-info-store'
import IrisGlobalSearchDataTable from '@/modules/global-search/components/IrisGlobalSearchDataTable.vue'
import DataAccess from '@/shared/services/data-access'
import LanguageHelper from '@/shared/services/language-helper'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import Common from '@/shared/services/common'

// Define an interface for the entity objects
interface Entity {
  Id: string
  Name: string
  PluralLabel: string
  Area: string
  Selected: boolean
  SortExpr: string
  SortDir: string
  PageIndex: number
  Loading: boolean
  refresh: boolean
  TotalRows?: number | null
}

// Define an interface for the eventData object
interface EventData {
  entityId: string
  entity: string
  sortExpr: string
  sortDir: string
  pageIndex: number
  pageSize: number
  totalRows: number
}

// Define an interface for the URL parameters
interface UrlParams {
  sort: string | null
  pageIndex: number
  entity: string | null
  entityId: string | null
  sortExpr: string
  sortDir: string,
  term: string | null
}

const entities = ref<Entity[]>([])
const selectedItem = ref<string>("All")
const term = ref("")
const loading = ref(true)
const showDataLoading = ref(false)
const languageHelper = new LanguageHelper()
const route = useRoute()
const router = useRouter()
const $drawerEl = ref<HTMLElement>()
const headerHeight = ref(0)
const systemInfo = useIrisSystemInfoStore().getData()
const showAllTabs = ref(false)
let drawer : Drawer
let noResults = ref(true)

const checkScreenSize = () => {
  if (window.innerWidth >= 640 && drawer && drawer.isVisible())
    hideDrawer()
};

onMounted(async () => {
  await getEntityList()
  // Initial check when component mounts
  const navbar = document.querySelector('.site-page-header') as HTMLElement;
  
  if (navbar)
    headerHeight.value = navbar.offsetHeight

  initialize()
  checkScreenSize(); 
  window.addEventListener('resize', checkScreenSize);
})

watch(() => [systemInfo.currentLanguage], () => {
  if (!loading.value) {
    entities.value.forEach(entity => {
      entity.refresh = true
    })
    getEntityList()
  }
});

watch(() => [route.query], () => {
  if (loading.value == false) {
    const params: any = getUrlParams()

    if (params.entity == null)
      params.entity = 'All'

    if (params.entity == 'All')
      params.entityId = 'All'

    const eventData = { entityId: params.entityId, sortExpr: params.sortExpr, sortDir: params.sortDir, pageIndex: params.pageIndex } as EventData
    itemClicked(eventData)
  }
}, { immediate: true })

function initialize() {
  const instanceOptions = {
    id: 'gs-result-list-drawer',
    override: true
  };

  const options = {
      placement: 'left',
      backdrop: true,
      bodyScrolling: false,
      edge: false,
      edgeOffset: '',
      backdropClasses:'bg-text-color dark:bg-text-color-dark opacity-50 fixed inset-0 z-30',
      onHide: () => {
      },
      onShow: () => {
      },
      onToggle: () => {
      },
  };

  drawer = new Drawer($drawerEl.value, options, instanceOptions);
}

async function getEntityList() {
    showDataLoading.value = true

    const url = "/sys/globalsearch/getsearchentitylist"
    const dataAccess = new DataAccess()

    const searchEntityListResult = await dataAccess.execute(url, null, RequestMethod.get)

    showAllTabs.value = searchEntityListResult.showAllTabsEnabled
    entities.value = searchEntityListResult.entityList

    entities.value.forEach(en => {
        en.SortDir = "A"
        en.SortExpr = ""
        en.PageIndex = 0
        en.Loading = true
        en.refresh = false
        en.TotalRows = null
    })

    const params = getUrlParams()
    if (showAllTabs.value)
        entities.value.unshift({
            Id: "All",
            Name: languageHelper.getMessage('all'),
            PluralLabel: languageHelper.getMessage('all'),
            Area: "",
            Selected: true,
            SortExpr: "",
            SortDir: "A",
            PageIndex: 0,
            Loading: false,
            refresh: false
        })
    else {
      // when all option is disabled, we need to ensure the URL parameters are still applied.
      params.entity = entities.value[0].Name
      params.entityId = entities.value[0].Id     
    }

    for (const entity of entities.value) {

      if (entity.Id == "All")
        continue

      var endpoint = `/${entity.Name}/getglobalsearchresult`

      if (entity.Area)
        endpoint = `/${entity.Area}${endpoint}`

      const data: any = {
        term: term.value,
        entityId: entity.Id,
        onlyCounts: true
      }

      const url = Common.constructUrl(endpoint, data)
      const dataAccess = new DataAccess()
      let searchResult = null
      try {
        searchResult = await dataAccess.execute(url, null, RequestMethod.get)
        entity.TotalRows = searchResult.totalRows
      }
      catch (err: any) {
        return
      }
    }

    loading.value = false

    if (params.entity) {
      const entity = entities.value.find((item) => item.Name == params.entity && item.Id == params.entityId)

      if (entity) {
        entity.SortDir = params.sortDir
        entity.SortExpr = params.sortExpr
        entity.PageIndex = params.pageIndex

        if (params.term) 
          term.value = params.term

        itemClicked({ entityId: entity.Id, sortExpr: entity.SortExpr, sortDir: entity.SortDir, pageIndex: entity.PageIndex } as EventData)
      }
    }

    if (showAllTabs.value && entities.value.length > 1 || !showAllTabs.value && entities.value.length >= 1)
      noResults.value = false
}

function updateRoute(eventData: EventData) {
  const selectedEntity: any = entities.value.find((item: Entity) => item.Id === eventData.entityId)
  
  if (selectedEntity) {
    eventData.entity = selectedEntity.Name
    setUrlParams(eventData)
  }
}

function itemClicked(eventData: EventData) {
  if (!eventData.sortExpr)
    eventData.sortExpr = ""

  if (!eventData.sortDir)
    eventData.sortDir = "A"

  if (!eventData.pageIndex)
    eventData.pageIndex = 0
  
  selectedItem.value = eventData.entityId
  let previousSelectedItem: Entity
  let flag = false
  
  entities.value.forEach((element: Entity) => {
    if (element.Selected && element.Id !== "All") {
      previousSelectedItem = element
      flag = true
    }

    element.Selected = false
  })

  let selectedEntity = entities.value.find((item: Entity) => item.Id === eventData.entityId)
  
  if (selectedEntity == null && !showAllTabs.value){
    selectedEntity = entities.value[0]
    selectedItem.value = selectedEntity.Id
  }
  
  if (selectedEntity) {
    eventData.entity = selectedEntity.Name

    selectedEntity.Selected = true
    selectedEntity.SortExpr = eventData.sortExpr
    selectedEntity.SortDir = eventData.sortDir
    selectedEntity.PageIndex = eventData.pageIndex

    if (selectedEntity.Id !== "All")
      selectedEntity.Loading = true
    else if (flag)
      previousSelectedItem!.Loading = true

    updateShowDataLoading()
  }
   else
    console.error(`Entity with ID ${eventData.entityId} not found`)

  // Close the drawer
  hideDrawer()
}

function itemLoaded(eventData: EventData) {
  const entity = entities.value.find((en) => en.Name == eventData.entity && en.Id == eventData.entityId)

  if (entity) {
    entity.Loading = false
    entity.TotalRows = eventData.totalRows;
    updateShowDataLoading()
  }

  setUrlParams(eventData)
}

function setUrlParams(eventData: EventData) {
  if (eventData.pageSize == 10) {

    const queryParams = {
      entity: eventData.entity,
      entityId: eventData.entityId == "All" ? '' : eventData.entityId,
      srt: eventData.sortExpr ? `${eventData.sortExpr}-${eventData.sortDir}` : '',
      pinx: eventData.entityId == "All" ? '' : String(eventData.pageIndex),
      kw: term.value
    }

    router.push({ name: 'global-search', query: queryParams })
  }
}

function getUrlParams(): UrlParams {
  const urlObj = new URL(window.location.href)
  const queryString = urlObj.search
  const url_params = new URLSearchParams(queryString);
  const pageIndexStr = url_params.get('pinx')
  const pageIndex = (Number.isNaN(pageIndexStr))? 0 : Number(pageIndexStr)
  const srt = url_params.get("srt")
  const kw = url_params.get('kw')

  let sortExpr = ""
  let sortDir = "A"

  if (srt) {
    sortExpr = srt.split("-")[0]
    sortDir = srt.split("-")[1]
  }

  if (kw)
    term.value = kw

  const params: UrlParams = {
    sort: url_params.get("srt") as string | null,
    pageIndex: pageIndex,
    entity: url_params.get('entity') as string | null,
    entityId: url_params.get('entityId') as string | null,
    term: url_params.get('kw') as string | null,
    sortExpr: sortExpr,
    sortDir: sortDir
  }

  return params
}

function updateShowDataLoading() {
  let showLoading = false

  entities.value.forEach((en) => {
    showLoading = showLoading || en.Loading;
  })

  showDataLoading.value = showLoading;
}

function hideDrawer() {
  if (drawer && drawer.isVisible())
    drawer.hide()
}

function handleRefreshCompleted(entityId: string) {
  const entity = entities.value.find(en => en.Id === entityId)

  if (entity)
    entity.refresh = false
}

</script>

<template>
    <div v-if="!loading && !noResults && entities">
      <h2 class="text-2xl font-bold my-4">{{ $t("messages.searchResult") }} {{ term }}</h2>
      <hr class="border-t border-border-color mt-4 dark:border-border-color-dark">
      <div class="global-search-result-list relative" :data-serach-text="term">
        <div class="flex items-stretch flex-nowrap" v-if="entities">
            <div class="flex-item border-r border-right hidden sm:block">
                <div class="items-container items-inline sm:w-[200px] md:w-[300px]">
                    <div v-for="(item, index) in entities" 
                    :key="item.Id" 
                    :id="item.Id" 
                    :data-index="index" 
                    :class="(item.Selected ? 'search-item-selected border-primary bg-bg-color-200 dark:bg-bg-color-200-dark ' : 'border-transparent ') + 'search-item'"
                    class="border-l hover:border-primary hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">
                        <a 
                          href="#" 
                          class="flex justify-between w-full h-full p-3 text-sm text-primary hover:text-primary-hover"
                          :title="item.PluralLabel" 
                          @click.prevent="updateRoute({ entityId: item.Id, entity: '', sortExpr: item.SortExpr, sortDir: item.SortDir, pageIndex: 0, pageSize: 10, totalRows: item.TotalRows? item.TotalRows : 0 })">
                            <span class="item-text text-responsive">{{ item.PluralLabel }}</span>
                            <span class="entity-count-span">{{ item.TotalRows !== null ? item.TotalRows : '...' }}</span>
                        </a>
                      </div>
                </div>
            </div>
            <div class="flex-item block sm:hidden">
                <button class="btn-primary font-medium rounded-lg text-sm absolute top-10 -left-10 rotate-90" 
                  type="button"
                  aria-controls="gs-result-list-drawer"
                  @click="drawer.show()">
                    Show List
                </button>
                <div id="gs-result-list-drawer" ref="$drawerEl" class="fixed top-0 left-0 z-40 h-screen p-4
                  transition-transform -translate-x-full bg-bg-color w-80 dark:bg-bg-color-dark" 
                  :style="{ top: `${headerHeight}px`, maxHeight: `calc(100vh - ${headerHeight}px)` }"
                  tabindex="-1">
                  <button type="button" aria-controls="gs-result-list-drawer"  
                    class="rounded text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center"
                    @click="hideDrawer">
                    <IrisIcon name="xmark" aria-hidden="true"/>
                    <span class="sr-only">Close menu</span>
                  </button>
                  
                  <div class="mt-6 h-full overflow-y-auto">
                      <div v-for="(item, index) in entities" 
                      :key="item.Id" 
                      :id="item.Id" 
                      :data-index="index" 
                      :class="(item.Selected ? 'search-item-selected border-primary bg-bg-color-200 dark:bg-bg-color-200-dark ' : 'border-transparent ') + 'search-item'"
                      class="border-l hover:border-primary hover:bg-bg-color-300 dark:hover:bg-bg-color-300-dark">
                          <a 
                            href="#" 
                            class="flex justify-between w-full h-full p-3 text-sm text-primary hover:text-primary-hover"
                            :title="item.PluralLabel" 
                            @click.prevent="updateRoute({ entityId: item.Id, entity: '', sortExpr: item.SortExpr, sortDir: item.SortDir, pageIndex: 0, pageSize: 10, totalRows: item.TotalRows? item.TotalRows : 0 })">
                              <span class="item-text text-responsive">{{ item.PluralLabel }}</span>
                              <span class="entity-count-span">{{ item.TotalRows !== null ? item.TotalRows : '...' }}</span>
                          </a>
                        </div>
                  </div>
                  
                </div>
            </div>
            <div class="flex-item pl-3 pt-5 flex-grow overflow-hidden ml-5 sm:ml-0">
                <div v-for="item in entities">
                    <div class="item-search-result" 
                    :data-info="item.Id" 
                    v-if="item.Id !== 'All' && (item.Selected || selectedItem === 'All')">
                        <div class="result-table-heading text-l font-bold mb-3 pl-3">{{ item.PluralLabel }}</div>
                        <iris-global-search-data-table v-if="!loading" 
                          :selected-entity-id="selectedItem"
                          :id="`eg-${item.Id}`"
                          :entity="item.Name"
                          :entity-id="item.Id"
                          :area="item.Area"
                          :term="term"
                          @columnClicked="updateRoute"
                          :size="(item.Selected ? 10 : 5)"
                          :page-index = "item.PageIndex"
                          :sort-experssion="item.SortExpr"
                          :sort-dir="item.SortDir"
                          @onLoaded="itemLoaded"
                          :enable-pagination="item.Selected"
                          @refreshCompleted="handleRefreshCompleted(item.Id)"
                          :refresh="item.refresh" 
                          >
                        </iris-global-search-data-table>
                    </div>
                </div>
            </div>
        </div>
      </div>
    </div>
    <div v-if="noResults && !loading">
      <div class="text-2xl font-bold my-4">{{ $t("messages.searchResult") }} {{ term }}</div>
      <hr class="border-t border-border-color mt-4 dark:border-border-color-dark">
      <div class="text-2xl my-4 text-text-color-400 dark:text-text-color-400-dark">{{ languageHelper.getMessage("noResults") }}</div>
    </div>
    <div class="h-screen grid place-items-center max-w-page m-auto" v-if="loading">
      <div class="flex justify-center content-center">
        <IrisIcon name="spinner-third" class="inline w-10 h-10 animate-spin fill-primary"/>
        <span class="ms-3 text-4xl">{{ languageHelper.getMessage("loading") }}</span>
      </div>
    </div>
</template>