Follow the steps below to launch development server with SSL and in HTTPS mode:

- Use the "openssl.exe" located in the "P:\Leo\CreateCert\bin\Debug\" folder

- Execute the following command => openssl req -nodes -new -x509 -days 7300 -keyout server.key -out server.crt

- Answer the prompts like the following:

    Country Name (2 letter code) [AU]:CA
    State or Province Name (full name) [Some-State]:Ontario
    Locality Name (eg, city) []:Toronto
    Organization Name (eg, company) [Internet Widgits Pty Ltd]:Magentrix
    Organizational Unit Name (eg, section) []:Development
    Common Name (e.g. server FQDN or YOUR name) []:localhost
    Email Address []:<EMAIL>

- Copy the generated "server.key" and "server.crt" files in the root folder of the Vue project where the "vite.config.ts" is located

- Make sure the "https://localhost:5173" entry exist for "Whitelisted CORS" in Magentrix.
    If not then add it. You will need to add like "https://localhost.com:5173" and execute the following SQL to change it from "https://localhost.com:5173" to "https://localhost:5173"

        UPDATE system.Resource SET Name='https://localhost:5173', Metadata='{"Name":"https://localhost:5173","Id":null}' WHERE Id='XYZ'

    Note: Where XYZ is the correct Id for the newly added entry.

- Recycle the App Pool for iris.magentrix.command

- Modify the "vite.config.ts" like below:

    import fs from 'fs'

    server: {
        https: {
        key: fs.readFileSync('server.key'),
        cert: fs.readFileSync('server.crt')
        }
    }

    Note: Add the "server" entry before "plugins" entry.

- Run "npm run dev" and the local site should work in HTTPS mode

- You may need to select "Proceed to unsafe site" manually
    You can also Enable the following in Chrome to bypass the Chrome warning for the unsafe site => chrome://flags/#allow-insecure-localhost
