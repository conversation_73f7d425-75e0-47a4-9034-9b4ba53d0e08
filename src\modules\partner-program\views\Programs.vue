<script setup lang="ts"> 
import { ref, onMounted, computed } from 'vue'
import { GridColumn } from '@/shared/services/grid-column'
import { RequestMethod } from '@/shared/services/enums'
import { GridAction, GridActionMode } from '@/shared/services/grid-action'
import { useIrisPage } from '@/shared/composables/iris-page'
import DataAccess from '@/shared/services/data-access'
import Common from '@/shared/services/common'
import LanguageHelper from '@/shared/services/language-helper'
import IrisPagination from '@/shared/components/general-controls/IrisPagination.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisAlert from '@/shared/components/general-controls/IrisAlert.vue'
import IrisMessageBox from '@/shared/components/general-controls/IrisMessageBox.vue'
import IrisSortableList from '@/shared/components/general-controls/IrisSortableList.vue'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisGrid from '@/shared/components/general-controls/IrisGrid.vue'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import Iris<PERSON>ield from '@/shared/components/form-controls/IrisField.vue'
import IrisTextbox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisCheckbox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisValidationSummary from '@/shared/components/form-controls/IrisValidationSummary.vue'
import IrisAssetField from '@/shared/components/form-controls/IrisAssetField.vue'
import PartnerProgramSetupNav from '@/modules/partner-program/components/PartnerProgramSetupNav.vue'

/* --- Props --- */
const props = defineProps({
  enablePagination: {
    type: Boolean,
    default: true,
  },
  size: {
    type: Number,
    default: 10,
  }
})

/* --- Constants and Refs --- */
const languageHelper = new LanguageHelper()
const base = useIrisPage()
const dataAccess = new DataAccess()
const programs = ref<any[] | null>(null)
const columns: GridColumn[] = []
const totalRows = ref(0)
const pageIndex = ref(0)
const isModelVisible = ref(false)
const isEditModelVisible = ref(false)
const programModel = ref<any>(null)
const tierModel = ref<any>(null)
const tiers = ref<any[]>([])
const minTiers = 1
const maxTiers = 6
const isSubmitting = ref(false)
const formErrors = ref<string[]>([])
const showErrorMessage = ref(false)
const serverErrorMessage = ref('')
const isLoading = ref(false)
const isModalLoading = ref(false)
const hasUnsavedChanges = ref(false)
const gridActions = ref<GridAction[]>()
const showDeleteMessageBox = ref(false)
const pendingDeleteId = ref<string | null>(null)
const searchTerm = ref('')
const originalProgramName = ref<string>('')

gridActions.value = []
gridActions.value!.push({ label:languageHelper.getMessage("edit"), path:"Edit", type: GridActionMode.Button , icon:"pencil"} as GridAction)
gridActions.value!.push({ label:languageHelper.getMessage("delete"), path:"Delete", type: GridActionMode.Button , icon:"trash-can" } as GridAction)

/* --- Lifecycle Hooks --- */
onMounted(async () => {
  base.setPageTitle(languageHelper.getMessage('partnerProgram'))

  try {
    columns.push(GridColumn.create({ name: 'Name', sortExpression: 'Name', sortDirection: 'A', field: 'Name', component: "IrisGridLink", data: { urlPattern: '/setup/partner-program/programs/{!Id}' }  }))
    columns.push(GridColumn.create({ name: 'IsActive', sortExpression: 'IsActive' }))
    columns.push(GridColumn.create({ name: 'Description', sortExpression: 'Description' }))
    columns.push(GridColumn.create({ name: 'ModifiedOn', sortExpression: 'ModifiedOn', selected: true, sortDirection: 'D' }))
    
    await loadData('ModifiedOn', 'D', 0, '')

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failInitialize") 
  }
})

/* --- Computed --- */
const isSaveDisabled = computed(() => {
  if (!programModel.value) 
    return true

  const name = programModel.value.Name?.trim() ?? ''
  return (
    isSubmitting.value ||
    formErrors.value.length > 0 ||
    !name
  )
})

const isEditMode = computed(() => Boolean(programModel.value?.Id))

/* --- Functions --- */
function onActionClicked(actionName: string, data: any) {
  if (actionName === 'Edit') {
    originalProgramName.value = data.Name
    programModel.value = { ...data }
    isEditModelVisible.value = true
  } else if (actionName === 'Delete') {
    pendingDeleteId.value = data.Id
    showDeleteMessageBox.value = true
  }
}

async function loadData(sortExpr: string, sortDirection: string, pageIndex: number, keyword: string = '') {
  isLoading.value = true
  showErrorMessage.value = false

  try {
    var endpoint = `/partnerprogram/getprograms`
    const data: any = {
      sortExpression: sortExpr,
      sortDirection: sortDirection,
      keyword: keyword.trim()
    }

    if (props.enablePagination) {
        data.size = props.size
        data.pinx = pageIndex
    } else {
        data.size = 0
        data.pinx = 0
    }

    const url = Common.constructUrl(endpoint, data)
    const response = await dataAccess.execute(url, RequestMethod.post)

    if (!response) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
      isLoading.value = false
      return
    }

    if (!response.programs || !response.totalRows || !response.programs.length) {
      programs.value = []
      isLoading.value = false
      return
    }
    
    totalRows.value = response.totalRows
    programs.value = response.programs || []

  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData") 
    programs.value = []
  } finally {
    isLoading.value = false
  }
}

function doSearch(): void 
{
  loadData(
    columns.find(c => c.selected)!.sortExpression,
    columns.find(c => c.selected)!.sortDirection,
    pageIndex.value,
    searchTerm.value
  )
}

async function onConfirmDelete() {

  if (!pendingDeleteId.value) 
    return

  try {
    const payload = {
      programId: pendingDeleteId.value
    }
    pendingDeleteId.value = null

    const response = await dataAccess.execute(
      `/partnerprogram/deleteprogram`,
      payload
    )

    showDeleteMessageBox.value = false

    if (!response?.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failDelete")
    }

    await loadData(
      columns.find(c => c.selected)!.sortExpression,
      columns.find(c => c.selected)!.sortDirection,
      pageIndex.value,
      searchTerm.value
    )
  } catch {
    showErrorMessage.value   = true
    serverErrorMessage.value = languageHelper.getMessage('failDelete')
  }
}

function onCancelDelete() {
  pendingDeleteId.value = null
  showDeleteMessageBox.value = false
}

async function saveProgram() {   
    formErrors.value = []
    isSubmitting.value = true

    if (!programModel.value?.Name?.trim())
      formErrors.value.push(languageHelper.getMessage("programNameRequired"))

    const count = tiers.value.length
    if (count < minTiers) 
      formErrors.value.push(languageHelper.getMessage("minTierError"))
    
    if (count > maxTiers) 
      formErrors.value.push(languageHelper.getMessage("maxTierError"))
    
    const tierNames = new Set<string>()

    if (tiers.value.length === 0) {
      formErrors.value.push(languageHelper.getMessage("atLeastOneTierRequired"))
      isSubmitting.value = false
      return
    }
    tiers.value.forEach((tier, i) => {
      const idx = i + 1
      if (!tier.Name?.trim()) 
      formErrors.value.push(languageHelper.getMessage("tierInexNameRequired",{ idx: idx.toString()}))

      const lower = tier.Name?.toLowerCase() || ""
      if (lower && tierNames.has(lower)) 
        formErrors.value.push(languageHelper.getMessage("duplicateIndexTierName", {idx: idx.toString(), tierName: tier.Name}))

      tierNames.add(lower)

      if (tier.sequence !== idx) {
        tier.sequence = idx
      }
    })

    if (formErrors.value.length) {
      isSubmitting.value = false
      return
    }

  try {

    tiers.value = tiers.value.map(t => {

      let rel = t.Badge ? t.Badge.replace(/\\/g, '/') : ''

      if (rel && !rel.startsWith('/')) {
        rel = '/public/assets/' + rel
      }

      return {
        ...t,
        Badge: rel
      }
    })

    const payload = {
      program: programModel.value,
      tiers: tiers.value
    }

    const response = await dataAccess.execute('/partnerprogram/createprogram', payload)
    
    if (!response || response.error) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failSave")
    }
    
    await loadData(columns.find((c) => c.selected)!.sortExpression, 
      columns.find((c) => c.selected)!.sortDirection, pageIndex.value,
       searchTerm.value)

    onModalHide()
      
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failSave")
  } finally {
    isSubmitting.value = false
  }
}

async function showModal() {
  try {
    isModalLoading.value = true
    formErrors.value = []
    isSubmitting.value = false
    
    await getNewProgram()

    await getNewTier()
    
    const initialTier = { ...tierModel.value }
    initialTier.sequence = 1
    tiers.value = [initialTier]
    
    isModelVisible.value = true
    hasUnsavedChanges.value = false
   } catch  {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
    } finally {
      isModalLoading.value = false
    }
}

function onModalHide() {
  if (hasUnsavedChanges.value) {
    if (!confirm(languageHelper.getMessage("unsavedChangesConfimation"))) {
      return
    }
  }
  isModelVisible.value = false
  formErrors.value = []
  tiers.value = []
  programModel.value = null
  tierModel.value = null
  hasUnsavedChanges.value = false
  isEditModelVisible.value = false
  loadData(
    columns.find((c) => c.selected)!.sortExpression, 
    columns.find((c) => c.selected)!.sortDirection,
    pageIndex.value,
    searchTerm.value)
}

async function getNewProgram() {
  try {
    const data = await dataAccess.execute('partnerprogram/getnewprogram')
    if (!data || !data.program) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      return
    }
    programModel.value = data.program
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
  }
}

async function getNewTier() {
  try {
    const data = await dataAccess.execute('partnerprogramtier/getnewtier')
    if (!data || !data.tier) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failFetchData")
      return
    }
    tierModel.value = data.tier
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failFetchData")
  }
}

async function sortData(col: GridColumn) {
  const selectedCol = columns.find((column) => column.name == col.name)

  if (selectedCol) {
    columns.forEach((c) => (c.selected = false))
    selectedCol.selected = true
    selectedCol.sortDirection = col.sortDirection
  }

  await loadData(
    col.sortExpression, 
    col.sortDirection, 
    pageIndex.value, 
    searchTerm.value)
}

function onPageChanged(newPage: number) {
  pageIndex.value = newPage

  loadData(
    columns.find((c) => c.selected)!.sortExpression,
    columns.find((c) => c.selected)!.sortDirection, 
    pageIndex.value, 
    searchTerm.value)
}

function removeTier(index: number){
  if (tiers.value.length > minTiers) {
    tiers.value.splice(index, 1)
    tiers.value.forEach((tier, idx) => {
      tier.sequence = idx + 1
    })
  }
}

function addTier() {
  if (tiers.value.length < maxTiers) {
    const newTier = { ...tierModel.value }
    newTier.sequence = tiers.value.length + 1
    tiers.value.push(newTier)
  }
}

async function editProgram() {
  formErrors.value = []
  
  if (!programModel.value?.Name?.trim()) {
    formErrors.value.push(languageHelper.getMessage("programNameRequired"))
    isSubmitting.value = false
    return
  }

  if (formErrors.value.length) 
    return

  isSubmitting.value = true

  try {
    const payload = { program: programModel.value }
    
    const response = await dataAccess.execute(
      '/partnerprogram/updateprogram',
      payload
    )

    if (!response.Success) {
      showErrorMessage.value = true
      serverErrorMessage.value = languageHelper.getMessage("failSave")
      return
    }

    await loadData(
      columns.find(c => c.selected)!.sortExpression,
      columns.find(c => c.selected)!.sortDirection,
      pageIndex.value,
      searchTerm.value
    )

    onModalHide()
  } catch {
    showErrorMessage.value = true
    serverErrorMessage.value = languageHelper.getMessage("failSave")
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <partner-program-setup-nav selected-tab="programs" />
  <div class="pb-4 w-full bg-bg-color-100 dark:bg-bg-color-100-dark rounded">
    <iris-alert v-if="showErrorMessage" type="danger" class="mb-4">
      {{ serverErrorMessage }}
    </iris-alert>
    <div class="flex justify-between items-center p-4">
        <input
          id="searchInput"
          v-model="searchTerm"
          @keyup.enter="doSearch"
          type="text"
          :placeholder="languageHelper.getMessage('search') + '...'"
                class='p-2 overflow-hidden text-text-color bg-bg-color rounded-lg border-border-color
                  focus:border-border-focus focus:ring-1 focus:ring-border-focus
                  dark:text-text-color-dark dark:bg-bg-color-dark dark:border-border-color-dark 
                  placeholder:italic placeholder:text-xs placeholder:text-text-color-400 dark:placeholder:text-text-color-400-dark'
        />
        <button @click="showModal"
          class="inline-flex btn-primary"
          type="button">
          {{ languageHelper.getMessage("newProgram") }}
        </button>
    </div>
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
    <iris-grid 
        v-if="programs && programs.length"
        id="programs"
        :value="programs"
        :cols="columns"
        @on-sort="sortData"
        :actions="gridActions"
        @actionClicked="onActionClicked"
        :showBorder="false"
        :showRounding="false"
      />
      <div v-else class="text-text-color-400 dark:text-text-color-400-dark">
        <span>{{ languageHelper.getMessage('noRecords') }}</span>
      </div>
    <div class="mt-5" v-if="enablePagination && totalRows > props.size">
      <iris-pagination v-if="enablePagination" 
        :page-changed="onPageChanged" 
        :total="totalRows" 
        :page-size="props.size"
        :current-page="pageIndex" 
        :use-icons-only="true" 
        :next-and-previous-only="false" 
        :size="'sm'"
        :first-btn-lbl='languageHelper.getMessage("first")' 
        :last-btn-lbl='languageHelper.getMessage("last")'
        :next-btn-lbl='languageHelper.getMessage("next")' 
        :prev-btn-lbl='languageHelper.getMessage("previous")'>
      </iris-pagination>
    </div>
  </div>
  <!-- Modal -->
  <iris-modal id="add-program-modal" :show="isModelVisible" :title='languageHelper.getMessage("addProgram")' @onHide="onModalHide">
    <template #content>
      <div v-if="programModel && tierModel">
        <div v-if="isModalLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 fill-primary"></div>
        </div>
        
        <iris-form v-else id="createProgram" class="p-4 md:p-5">
          <iris-validation-summary formId="createProgram" />
          <iris-alert v-if="formErrors.length > 0" type="danger" class="mb-4">
              <ul class="list-disc list-inside">
              <li v-for="(error, index) in formErrors" :key="index">{{ error }}</li>
            </ul>
          </iris-alert>
          
          <!-- Program Section -->
          <div class="mb-4">
            <div class="col-span-2">
            <iris-field id="programName" :value="programModel" field="Name" required />
            </div>
            <div class="col-span-2">
            <iris-checkbox id="programActive" :value="programModel" field="IsActive"/>
            </div>
            <div class="col-span-2">
              <iris-textbox id="programDescription" :value="programModel" field="Description"/>
            </div>
          </div>
          <!-- Tiers Section -->
          <div class="mt-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium">{{ languageHelper.getMessage("programTiers") }}</h3>
              <button
                v-if="tiers.length < maxTiers"
                type="button"
                class="btn-primary"
                @click="addTier"
              >
                {{ languageHelper.getMessage("addTier") }}
              </button>
            </div>
            <iris-sortable-list v-model="tiers" itemKey="sequence">
              <template #default="{ item, index}">
                <div class="border rounded-lg p-4 bg-bg-color mb-4">
                  <!-- Header with drag-handle and remove button -->
                  <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center gap-2">
                      <iris-icon name="bars" class="cursor-move flex-shrink-0" />
                      <h4 class="text-text-color-dark dark:text-text-color-dark-dark truncate">
                        {{ item.Name }}
                      </h4>
                    </div>
                    <button
                      v-if="tiers.length > minTiers"
                      @click="removeTier(index)"
                      type="button"
                      class="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm
                            text-pageB-base-400 hover:bg-bg-color-300 hover:text-text-color-200
                            dark:text-pageB-base-400-dark dark:hover:bg-bg-color-300-dark dark:hover:text-text-color-200-dark"
                    >
                      <iris-icon name="xmark" class="cursor-move flex-shrink-0" />
                    </button>
                  </div>

                  <!-- Body with fields -->
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <iris-field
                        :id="`tierName${index}`"
                        :value="item"
                        field="Name"
                        required
                      />
                    </div>
                    <div>
                      <iris-textbox
                        :id="`tierDescription${index}`"
                        :value="item"
                        field="Description"
                      />
                    </div>
                  </div>
                  <div>
                      <iris-asset-field 
                          :id="`tierBadge${index}`" 
                          :value="item.Badge"
                          @onChange="value => item.Badge = value"
                          :label="languageHelper.getMessage('tierBadge')" 
                          :allowedExtensions="['.jpg', '.jpeg', '.png']"
                          hint="Select an image for the tier picture. Minimum size: 100 x 100 pixels." 
                      />
                    </div>
                </div>
              </template>
            </iris-sortable-list>
          </div>
        </iris-form>
      </div>
    </template>
    
    <template #footer>
      <div class="flex justify-end space-x-3">
        <button 
          type="button" 
          class="px-4 py-2inline-flex items-center p-1 ms-2 text-sm text-text-color-400 bg-transparent hover:text-primary dark:text-text-color-400-dark rounded-lg transition-colors duration-200"
          @click="onModalHide"
        >
          {{ languageHelper.getMessage("cancel") }}
        </button>
        <button 
          type="button" 
          class="px-4 py-2 btn btn-primary font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" 
          @click="saveProgram"
          :disabled="isSubmitting"
        >
          <span v-if="isSubmitting" class="inline-flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 fill-primary"></div>
            {{ languageHelper.getMessage("saving") }}
          </span>
          <span v-else>{{ languageHelper.getMessage("addProgram") }}</span>
        </button>
      </div>
    </template>
  </iris-modal>
  <!-- Edit Program -->
  <iris-modal
    id="edit-program-modal" 
    :show="isEditModelVisible" 
    :title='languageHelper.getMessage("editProgram")'
    @onHide="onModalHide"
    >
    <template #content>
      <div v-if="isModalLoading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
      <iris-form id="editProgram" v-else-if="programModel">
        <iris-validation-summary formId="editProgram" />
        <!-- Error Messages -->
        <iris-alert v-if="formErrors.length > 0" type="danger" class="mb-4">
            <ul class="list-disc list-inside">
            <li v-for="(error, index) in formErrors" :key="index">{{ error }}</li>
          </ul>
        </iris-alert>
        <div class="grid gap-4 mb-4 grid-cols-2">
          <div class="col-span-2">
            <iris-field 
              id="programName" 
              :value="programModel" 
              field="Name"
              required 
            />
          </div>
          <div class="col-span-2">
            <iris-checkbox 
              id="programActive" 
              :value="programModel" 
              field="IsActive"
            />
          </div>
          <div class="col-span-2">
            <iris-textbox
              id="programDescription" 
              :value="programModel" 
              field="Description"
            />
          </div>
        </div>
      </iris-form>
    </template>
    <template #footer>
      <div class="flex justify-end space-x-3">
        <button 
          type="button"
          class="btn-light"
          @click="onModalHide">
          {{ languageHelper.getMessage("cancel") }}
        </button>
        <button 
          type="submit" 
          form="editProgram"
          class="btn-primary"
          :disabled="isSaveDisabled || isSubmitting || formErrors.length>0"
          @click="editProgram">
          <span v-if="isSubmitting">
            {{ languageHelper.getMessage("saving") }}
          </span>
          <span v-else>
            {{ languageHelper.getMessage("save") }}
          </span>
        </button>
      </div>
    </template>
  </iris-modal>
  <iris-messageBox
    type="Confirm" 
    :message="languageHelper.getMessage('deleteProgramMessage')"
    :show="showDeleteMessageBox"
    :title="languageHelper.getMessage('deleteProgram')" 
    :primaryButtonLabel="languageHelper.getMessage('delete')"
    :cancelButtonLabel="languageHelper.getMessage('cancel')"
    :icon="'trash-can'"
    :size="'small'"
    @onPrimaryClick="onConfirmDelete"
    @onCancelClick="onCancelDelete" />
</template>
