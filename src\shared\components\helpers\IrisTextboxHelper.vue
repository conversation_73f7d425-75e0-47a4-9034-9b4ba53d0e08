<script setup lang="ts">
import { ref } from 'vue'
import { FormControlModeEnum } from '@/shared/services/form-control-enums'
import LanguageHelper from '@/shared/services/language-helper'

const languageHelper = new LanguageHelper()

const props = defineProps({
    type: String,
    fields: {
        type: Object,
        default: {}
    },
    emits: {
        type: Function,
        default: {}
    },
    refs: {
        type: Object,
        default: {}
    },
    methods: {
        type: Object,
        default: {}
    },
    hasIconOnLeft: {
        type: Boolean,
        default: false
    },
    hasIconOnRight: {
        type: Boolean,
        default: false
    }
})

const localEmits = defineEmits({
    onFocus: null,
    onBlur: null
})

const _value = ref(props.refs._value)
const _required = ref(props.refs._required)
const _readOnly = ref(props.refs._readOnly)
const _mode = ref(props.refs._mode)
const _maxLength = ref(props.refs._maxLength)

function getPlaceHolder() {
    if (props.fields.placeHolder)
        return props.fields.placeHolder
    else if (props.type == 'Text' && props.fields.floatingLabel)
        return ''
    else if (_required.value)
        return languageHelper.getMessage('required')
    else
        return null
}
</script>

<template>
    <template v-if="type == 'Text'">
        <input
            v-model="_value"
            class="iris-textbox"
            :class="{
                'iris-textbox-lg': !methods.isInputGroup() && fields.size == 'large',
                'iris-textbox-sm': !methods.isInputGroup() && fields.size == 'small',
                'iris-textbox-invalid': !methods.isValid(),
                'peer': fields.floatingLabel,
                'ps-10': hasIconOnLeft,
                'pe-10': hasIconOnRight,
                'px-2': methods.isInputGroup() && fields.size == 'small'
            }"
            :id="fields.id"
            :type="methods.getInputType()"
            :disabled="_mode == FormControlModeEnum.disabled"
            :required="_required"
            :readonly="_readOnly"
            :maxlength="_maxLength"
            :placeholder="getPlaceHolder()"
            :tabindex="_mode == FormControlModeEnum.edit ? fields.tabIndex : undefined"
            :spellcheck="methods.isTextType() && fields.autoCorrect ? true : undefined"
            :autocapitalize="methods.isTextType() && fields.autoCapitalize ? 'on' : undefined"
            :autocomplete="fields.autoComplete ? 'on' : undefined"
            :aria-label="fields.ariaLabel"
            @keypress="emits('onKeyPress', $event.key)"
            @blur="emits('onBlur'); localEmits('onBlur');"
            @focus="localEmits('onFocus')" />
    </template>

    <template v-if="type == 'TextArea'">
        <div class="iris-textarea-wrapper">
            <textarea
                v-model="_value"
                class="iris-textarea"
                :id="fields.id"
                :rows="fields.rows"
                :disabled="_mode == FormControlModeEnum.disabled"
                :required="_required"
                :readonly="_readOnly"
                :maxlength="_maxLength"
                :placeholder="getPlaceHolder()"
                :tabindex="_mode == FormControlModeEnum.edit ? fields.tabIndex : undefined"
                :spellcheck="methods.isTextType() && fields.autoCorrect ? true : undefined"
                :autocapitalize="methods.isTextType() && fields.autoCapitalize ? 'on' : undefined"
                :autocomplete="fields.autoComplete ? 'on' : undefined"
                :aria-label="fields.ariaLabel"
                @keypress="emits('onKeyPress', $event.key)"
                @blur="emits('onBlur'); localEmits('onBlur');"
                @focus="localEmits('onFocus')">
            </textarea>
        </div>
    </template>
</template>