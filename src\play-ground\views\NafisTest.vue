<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useIrisPage } from '@/shared/composables/iris-page'
import { RequestMethod } from '@/shared/services//enums'
import IrisForm from '@/shared/components/form-controls/IrisForm.vue'
import IrisColor from '@/shared/components/form-controls/IrisColor.vue'
import IrisPicklist from '@/shared/components/form-controls/IrisPicklist.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import DataAccess from '@/shared/services/data-access'

const base = useIrisPage()
const model:any = ref(null)

onMounted(async () => {
  const dataAccess = new DataAccess()

  base.setPageTitle('Test Page')
  model.value = await dataAccess.execute('iris/GetSampleIrisData', null, RequestMethod.get)
  console.log(model.value)
})
</script>

<template>
  <div class="max-w-page m-5 mx-auto">
    <IrisForm id="f1" v-if="model">
        <IrisColor id="colorpkl" :value="model.contact" field="Test" required mode="edit"/>
        <br/>
        <IrisPicklist id="pkl" :enableSearch=true :value="model.contact" field="Color_Picklist" required mode="edit"/>
        <br/>
        <IrisMultiPicklist id="mp" :value="model.contact" field="MultiSelect_Picklist__c" required mode="edit"/>
        <br/>
        <p class="font-heading text-primary font-bold">This is a text for font</p>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
    </IrisForm>
</div>
</template>