import type { CatchInfo } from "@/shared/services/types"

export class HandleException {
    tryCallback: Function
    catchCallbacks: CatchInfo[] = []
    finallyCallback: Function | undefined = undefined

    constructor(tryFunc: Function) {
        this.tryCallback = tryFunc
    }

    Catch(type: any, func: Function): HandleException {
        this.catchCallbacks.push({ type, func })
        return this
    }

    async Finally(func: Function | undefined = undefined): Promise<void> {
        if (func)
            this.finallyCallback = func

        await this.execute()
    }

    private async execute() {
        if (!this.tryCallback)
            throw new Error('Try callback function is required.')

        if (this.catchCallbacks.length == 0)
            throw new Error('At least one catch callback is required.')

        try {
            if (this.isAsyncFunc(this.tryCallback))
                await this.tryCallback()
            else
                this.tryCallback()
        }
        catch (ex: any) {
            var found = false

            for (const catchCallback of this.catchCallbacks)
                if (ex instanceof catchCallback.type) {
                    found = true

                    if (this.isAsyncFunc(catchCallback.func))
                        await catchCallback.func(ex)
                    else
                        catchCallback.func(ex)
                    
                    break
                }
            
            if (!found)
                throw ex
        }
        finally {
            if (this.finallyCallback)
                if (this.isAsyncFunc(this.finallyCallback))
                    await this.finallyCallback()
                else
                    this.finallyCallback()
        }
    }

    private isAsyncFunc(func: Function): boolean {
        const result = func.constructor.name === 'AsyncFunction'

        return result
    }
}