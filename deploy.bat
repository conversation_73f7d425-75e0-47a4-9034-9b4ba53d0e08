@echo off

if [%1] == [] goto ERROR

echo Compiling...
cmd /C npx vite build > NUL

rem powershell -NoProfile -ExecutionPolicy Bypass -File "./cache-bust.ps1"

del %1\Web\_Assets\Scripts\iris\*.js
del %1\Web\_Assets\Scripts\iris\*.css

copy .\dist\_assets\scripts\iris\*.js       %1\Web\_Assets\Scripts\iris > NUL
copy .\dist\_assets\scripts\iris\*.css      %1\Web\_Assets\Scripts\iris > NUL

echo Deployed

goto END

:ERROR
    echo Note: Execute the command inside the Vue root folder with admin rights.
    echo Usage: deploy.bat {Magentrix Base Path}
    echo Example: ./deploy.bat P:\Leo\Staging

:END
