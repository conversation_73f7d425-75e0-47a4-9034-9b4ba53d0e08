<script setup lang="ts">
/* --- Imports --- */
import { onMounted, ref } from 'vue'
import { RequestMethod } from '@/shared/services/enums'
import { DropDownItem } from '@/shared/services/dropdown-item'
import { useIrisPage } from '@/shared/composables/iris-page'
import DataAccess from '@/shared/services/data-access'
import IrisIcon from '@/shared/components/general-controls/IrisIcon.vue'
import IrisLink from '@/shared/components/general-controls/IrisLink.vue'
import IrisCheckBox from '@/shared/components/form-controls/IrisCheckbox.vue'
import IrisTextBox from '@/shared/components/form-controls/IrisTextbox.vue'
import IrisMultiPicklist from '@/shared/components/form-controls/IrisMultiPicklist.vue'
import IrisSortableList from '@/shared/components/general-controls/IrisSortableList.vue'
import IrisModal from '@/shared/components/general-controls/IrisModal.vue'
import IrisDrawer from '@/shared/components/general-controls/IrisDrawer.vue'
import FileSelector from '@/modules/document-library/components/IrisFileSelector.vue'
import <PERSON><PERSON><PERSON><PERSON>ield from '@/shared/components/form-controls/IrisAssetField.vue'
import IrisToast from '@/shared/components/general-controls/IrisToast.vue'
import Common from '@/shared/services/common'

/* --- Interfaces --- */
interface SettingModel {
  ChatbotName: string
  ChatbotImage: string
  IsEnabled: boolean
  CaseDeflectionResponseEnabled: boolean
  IndexSearchEnabled: boolean
  ChatbotEnabled: boolean
  PredefinedQuestions: string
  ArticleTypes: string 
  Wikis: string
}

interface ResourceSettingModel {
  CaseArticleTypes: any[]
  CaseWikis: any[]
  ArticleTypes: any[]
  Wikis: any[]
  TicketDeflectionEnabled: boolean
}

interface Role {
  Id: string;
  Name: string;
  IsSelected: boolean;
}

interface PredefinedQuestion {
  id: number;
  text: string;
}

/* --- State Refs --- */
const dataAccess = new DataAccess()
const settingModel = ref<SettingModel>({
  ChatbotName: '',
  ChatbotImage: '',
  IsEnabled: false,
  CaseDeflectionResponseEnabled: false,
  IndexSearchEnabled: false,
  ChatbotEnabled: false,
  PredefinedQuestions: '',
  ArticleTypes: '',
  Wikis: ''
})
const resourceSetting = ref<ResourceSettingModel>({
  CaseArticleTypes: [],
  CaseWikis: [],
  ArticleTypes: [],
  Wikis: [],
  TicketDeflectionEnabled: false
})
const tabs = ref([
  { id: 'ai-search', label: 'AI Search & Ticket Deflection' },
  { id: 'ai-chatbot', label: 'Wizard Assistant' }
])
const selectedTab = ref('ai-search')
const settingRoles = ref<Role[]>([])
const currentPredefinedQuestion = ref<string>('')
const predefinedQuestionList = ref<PredefinedQuestion[]>([])
const showDisableConfirmation = ref(false)
const showActivateConfirmation = ref(false)
const componentKey = ref(0)
const showEdit = ref(false)
const currentEditQuestion = ref<string>('')
const selectedFiles = ref<string[]>([])
const supportedExtensions = ref<string[]>([])
const showAlert = ref(false)
const alertMessage = ref("Settings saved successfully!")
const alertType = ref<'danger' | 'info' | 'success' | 'warning'>("success")
const showDocumentDrawerState = ref(false)
const showAssignRoleDrawerState = ref(false)
const fileSelectorRef = ref<typeof FileSelector>()
const base = useIrisPage()



let currentEditIndexQuestion = 0
let caseSetting: ResourceSettingModel
let nextQuestionId = 1
let hasFetchedDocuments = false;

/* --- Lifecycle --- */
onMounted(async () => {
  base.setPageTitle('AI Tools Settings')

  await getAISetting()
})

/* --- Tab --- */
function selectTab(tabId: string) {
  selectedTab.value = tabId
}

/* --- Main Settings --- */
async function getAISetting() {
  const url = "/setup/aiassistantsetting/get"
  settingModel.value = await dataAccess.execute(url, null, RequestMethod.get)

  const questionStrings = settingModel.value.PredefinedQuestions?.split(/[\n;]+/).map(line => line.trim()).filter(text => text.length > 0) || []

  predefinedQuestionList.value = questionStrings.map((text, index) => ({
    id: index + 1,
    text: text
  }))

  nextQuestionId = predefinedQuestionList.value.length > 0
    ? Math.max(...predefinedQuestionList.value.map(q => q.id)) + 1
    : 1

  // Force re-render of the sortable list component
  componentKey.value++

  await loadData()
}

async function saveSetting() {
  const url = "/setup/aiassistantsetting/savesetting"

  const questionsString = predefinedQuestionList.value
    .map(q => q.text)
    .join(';')

  console.log('Saving questions in order:', predefinedQuestionList.value.map(q => q.text))
  console.log('Questions string being saved:', questionsString)

  settingModel.value.PredefinedQuestions = questionsString

  await dataAccess.execute(url, settingModel.value, RequestMethod.post)
  alertMessage.value = "Settings saved successfully!"
  alertType.value = "success"
  showAlert.value = true
}

/* --- Enable/Disable Settings --- */
function changeEnable(value: boolean) {
  if (!value && settingModel.value.IsEnabled) {
    settingModel.value.IsEnabled = value
    showDisableConfirmation.value = true
  } else if (value && !settingModel.value.IsEnabled) {
    showActivateConfirmation.value = true
    componentKey.value++
  } else
    settingModel.value.IsEnabled = value
}

async function saveAIEnableSetting(isEnabled: boolean) {
  settingModel.value.IsEnabled = isEnabled

  if (isEnabled)
    showActivateConfirmation.value = false
  else
    showDisableConfirmation.value = false

  const url = "/setup/aiassistantsetting/saveenable"
  await dataAccess.execute(url, { isEnabled }, RequestMethod.post)
}

async function saveDisableSetting() {
  await saveAIEnableSetting(false)
}

function cancelDisableClicked() {
  settingModel.value.IsEnabled = true
  showDisableConfirmation.value = false
}

async function saveActivateSetting() {
  await saveAIEnableSetting(true)
}

function cancelActivateClicked() {
  settingModel.value.IsEnabled = false
  showActivateConfirmation.value = false
}

/* --- Resource Data --- */
async function loadData() {
  await loadRolesSetting()

  const url = "/setup/aiassistantsetting/getdropdowndata"
  caseSetting = await dataAccess.execute(url, null, RequestMethod.get)

  const selectedArticleTypeIds = settingModel.value.ArticleTypes?.split(";") ?? []
  resourceSetting.value.ArticleTypes = caseSetting.ArticleTypes.map((item: any) => {
    const dropDownItem = new DropDownItem(item.Name, item.Id, "")
    dropDownItem.selected = selectedArticleTypeIds.includes(item.Id)
    return dropDownItem
  })
  resourceSetting.value.CaseArticleTypes = caseSetting.CaseArticleTypes ? [...caseSetting.CaseArticleTypes] : []

  const selectedWikiIds = settingModel.value.Wikis?.split(";") ?? []
  resourceSetting.value.Wikis = caseSetting.Wikis.map((item: any) => {
    const dropDownItem = new DropDownItem(item.Name, item.Id, "")
    dropDownItem.selected = selectedWikiIds.includes(item.Id)
    return dropDownItem
  })
  resourceSetting.value.CaseWikis = caseSetting.CaseWikis ? [...caseSetting.CaseWikis] : []
  resourceSetting.value.TicketDeflectionEnabled = caseSetting.TicketDeflectionEnabled
}

/* --- Role Settings --- */
async function loadRolesSetting() {
  const url = "/setup/aiassistantsetting/getroles"
  settingRoles.value = await dataAccess.execute(url, null, RequestMethod.get)
}

async function updateRolesSetting() {
  const url = "/setup/aiassistantsetting/updateroles"
  const selectedRoles = settingRoles.value.filter(role => role.IsSelected).map(role => role.Id).join(';')
  await dataAccess.execute(url, { roles: selectedRoles }, RequestMethod.post)
  closeAssignRoleDrawer()
}

async function cancelRolesSetting() {
  await loadRolesSetting()
  closeAssignRoleDrawer()
}

function openAssignRoleDrawer() {
    showAssignRoleDrawerState.value = true
}

function closeAssignRoleDrawer() {
    showAssignRoleDrawerState.value = false
}

/* --- Document Settings --- */
async function getDocumentSetting() {
  const url = "/setup/aiassistantsetting/getdocumentsetting"
  const response = await dataAccess.execute(url, null, RequestMethod.get)
  selectedFiles.value = response.DocumentIds || []
  supportedExtensions.value = response.SupportedExtensions || []
}

async function openDocumentDrawer(isViewMode = false) {
  showDocumentDrawerState.value = true

  if (!hasFetchedDocuments) {
    await getDocumentSetting()

    hasFetchedDocuments = true
  }
  
  await fileSelectorRef.value?.openWithMode(isViewMode ? 'view' : 'edit')
}

async function closeDocumentDrawer() {
  showDocumentDrawerState.value = false
}

async function saveDocumentChanges() {
  try {
    const url = "/setup/aiassistantsetting/savedocumentsetting"
    
    await dataAccess.execute(url, {
      docSetting: { DocumentIds: selectedFiles.value }
    }, RequestMethod.post)
    
    // Show success message
    alertMessage.value = "Documents queued for processing. This may take a few minutes."
    alertType.value = "success"
    showAlert.value = true
    
    closeDocumentDrawer()
  } catch (error: any) {
    // Show error message
    alertMessage.value = error.message || "Failed to save document settings"
    alertType.value = "danger"
    showAlert.value = true
  }
}

/* --- Question Management --- */
function handleQuestionListUpdate(newList: PredefinedQuestion[]) {
  console.log('Parent received update from sortable list:', newList);
  predefinedQuestionList.value = newList;
}

function addQuestion() {
  if (currentPredefinedQuestion.value.trim()) {
    predefinedQuestionList.value.push({
      id: nextQuestionId++,
      text: currentPredefinedQuestion.value.trim()
    })
    currentPredefinedQuestion.value = ''
  }
}

function selectEditQuestion(questionId: number) {
  const question = predefinedQuestionList.value.find(q => q.id === questionId)
  
  if (question) {
    currentEditIndexQuestion = questionId
    currentEditQuestion.value = question.text
    showEdit.value = true
  }
}

function saveQuestion() {
  const questionIndex = predefinedQuestionList.value.findIndex(q => q.id === currentEditIndexQuestion)
  
  if (questionIndex !== -1)
    predefinedQuestionList.value[questionIndex].text = currentEditQuestion.value.trim()
  
  showEdit.value = false
}


function cancelEditQuestion() {
  currentEditQuestion.value = ''
  showEdit.value = false
}

function deleteQuestion(questionId: number) {
  const index = predefinedQuestionList.value.findIndex(q => q.id === questionId)

  if (index !== -1)
    predefinedQuestionList.value.splice(index, 1)
}

/* --- Field Change Handlers --- */
function handleArticleTypesChange(newValue: string) {
  settingModel.value.ArticleTypes = newValue
}

function handleWikisChange(newValue: string) {
  settingModel.value.Wikis = newValue
}

/* --- Utility --- */
function getItemsWithRemovableFlag(items: any[], nonRemovableItems: any[]) {
  return items.map(item => {
    const isNonRemovable = nonRemovableItems.some(ni => ni === item.value)
    return new DropDownItem(
      item.label || item.Name,
      item.value || item.Id,
      item.color || '',
      !isNonRemovable
    )
  })
}

function openHelpWindow() {
    const url = '/doc/index/?Group=ai-assistant-setting'
    Common.openHelpWindow(url)
}
</script>

<template>
    <!-- IrisAlert positioned at the top-right corner -->
    <IrisToast v-model:show="showAlert" :message="alertMessage" :type="alertType" :duration="3000" />

    <header class="flex justify-between mb-8">
        <div>
            <div class="inline-flex items-center gap-4 mb-2">
                <IrisLink :path="'/setup/home/<USER>'" class="font-bold text-text-color dark:text-text-color-dark">
                    <IrisIcon name="arrow-left"></IrisIcon>
                </IrisLink>  
                
                <h1 class="text-2xl font-bold">AI Tools Settings</h1>
            </div>
            
            <!-- Breadcrumb -->
            <div class="text-sm text-text-color-400 dark:text-text-color-400-dark">
                <nav class="flex justify-between" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center text-sm">
                        <li>
                            <IrisIcon name="home" class="block"></IrisIcon>
                        </li>
                        <span class="mx-2">/</span>
                        <li>
                            Manage
                        </li>
                        <span class="mx-2">/</span>
                        <li>
                            <IrisLink
                            :path="'/setup/home/<USER>'">
                                Community Settings
                            </IrisLink>                            
                        </li>
                        <span class="mx-2">/</span>
                        <li>
                            AI Tools Settings
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
        <a 
            class="self-center hover:text-primary-hover"
            href="#"
            @click="openHelpWindow">
            Help on this page
        </a>
    </header>

    <div class="w-full p-6 bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm">
        <h5 class="mb-2 font-bold text-text-color dark:text-text-color-dark">AI Status</h5>
        <div class="flex justify-between items-end">
            <IrisCheckBox id="ai_enabled" :key="componentKey" :value="settingModel.IsEnabled"
            label="Enable AI" mode="edit" output-mode="toggle" @onChange="changeEnable"
            hint="Enable AI solutions for ticket deflection and Wizard Assistant."/>
            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 font-medium p-2 text-xs font-medium rounded" v-if="!settingModel.IsEnabled">AI is Disabled</span>
            <span class="bg-bg-color-200 dark:bg-bg-color-200-dark text-xs font-medium p-2 rounded" v-else>AI is Active</span>        
        </div>
    </div>
    <iris-modal :show="showDisableConfirmation" size="small" id="disable_confirmation" :closable=true @onHide="showDisableConfirmation = false">
        <template #content>
            <div>
                <h3 class="mb-5 font-bold text-xl text-text-color dark:text-text-color-dark">Are you sure you want to disable AI?</h3>
                <div class="text-text-color-300 dark:text-text-color-300-dark space-y-3 mb-4">
                    <ul class="list-disc list-inside space-y-2">
                        <li>Disabling AI will pause all AI-powered features immediately.</li>
                        <li>If you do not re-enable AI within 30 days, all AI-related data will be permanently deleted.</li>
                        <li>
                            To continue using AI later, you will need to set it up again.<br />
                            This action cannot be undone after 30 days!
                        </li>
                    </ul>
                </div>
            </div> 
        </template>
        <template #footer>
            <div class="flex flex-1 justify-between gap-4">
                <button class="flex-1 btn-light px-3" type="button"
                    @click="cancelDisableClicked">Cancel</button>
                <button ref="$btn" class="flex-1 btn-primary px-3" type="button"
                    @click="saveDisableSetting">OK</button>
            </div>
        </template>
    </iris-modal>

    <iris-modal :show="showActivateConfirmation" size="small" id="activate_confirmation" :closable=true @onHide="showActivateConfirmation = false">
        <template #content>
            <div>
                <h3 class="mb-5 font-bold text-xl text-text-color dark:text-text-color-dark">Activate AI Tools</h3>
                <div class="text-text-color-300 dark:text-text-color-300-dark space-y-3 mb-4">
                    <p>By enabling or using any of Magentrix AI services, including AI Tools, you agree to the terms outlined in the
                        <a href="https://help.magentrix.com/articles/knowledge/AI-Search" target="_blank" class="text-primary hover:text-primary-hover">Magentrix AI Services: Data & Privacy Policy</a>.
                    </p>
                </div>
            </div>
        </template>
        <template #footer>
            <div class="flex flex-1 justify-between gap-4">
                <button class="flex-1 btn-light px-3" type="button"
                    @click="cancelActivateClicked">Cancel</button>
                <button ref="$btn" class="flex-1 btn-primary px-3" type="button"
                    @click="saveActivateSetting">Activate</button>
            </div>
        </template>
    </iris-modal>
    <!-- AI Settings Sections -->
    <div class="w-full mt-6" v-if="settingModel.IsEnabled">
        <IrisDrawer
            id="document-drawer"
            :show="showDocumentDrawerState"
            @onHide="showDocumentDrawerState = false"
            placement="right"
        >
            <template #content>
                <FileSelector
                    v-model="selectedFiles"
                    ref="fileSelectorRef"
                    :supportedExtensions="supportedExtensions"
                />
            </template>
            <template #footer>
                <button class="flex-1 btn-light px-3" @click="closeDocumentDrawer">Cancel</button>
                <button class="flex-1 btn-primary px-3" @click="saveDocumentChanges">Save</button>
            </template>
        </IrisDrawer>
        <!-- Tab Menu -->
        <div class="mb-8">
            <div class="flex font-medium text-center border rounded border-border-color dark:border-border-color-dark bg-bg-color dark:bg-bg-color-dark">
                <div 
                    v-for="(tab, index) in tabs" 
                    :key="index" 
                    class="flex-1 p-3 transition" 
                    :class="{
                        'bg-bg-color-300 dark:bg-bg-color-300-dark font-bold': selectedTab === tab.id,
                        'cursor-default': selectedTab === tab.id,
                        'cursor-pointer hover:bg-bg-color-200 dark:hover:bg-bg-color-200-dark': selectedTab !== tab.id,
                        'rounded-r': index === tabs.length - 1,
                        'rounded-l': index === 0
                    }"
                    @click="selectTab(tab.id)">
                {{ tab.label }}
                </div>
            </div>
        </div>
        <!-- AI Search Section -->
        <transition
            enter-active-class="transition-all duration-200 ease-in-out"
            leave-active-class="transition-all duration-200 ease-in-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-[500px]"
            leave-from-class="opacity-100 max-h-[500px]"
            leave-to-class="opacity-0 max-h-0"
            mode="out-in"
        >
            <div v-if="selectedTab === 'ai-search'">
                <div>
                    <!-- Ticket Deflection Settings -->
                    <div v-if="resourceSetting.TicketDeflectionEnabled" class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm py-4 px-6 mb-6">
                        <h2 class="text-lg font-semibold mb-6 text-text-color dark:text-text-color-dark">Ticket Deflection Settings</h2>
                        <IrisCheckBox id="casedeflect_enabled" :value="settingModel.CaseDeflectionResponseEnabled" label="Enable AI Ticket Deflection" 
                            hint="When enabled, AI will suggest solutions to users before they submit a support ticket." mode="edit" output-mode="toggle" 
                            @onChange="val => settingModel.CaseDeflectionResponseEnabled = val"> 
                            <template #hint>
                                <div class="iris-input-hint">When enabled, AI will suggest solutions to users before they submit a support ticket.</div>
                                <IrisLink
                                    :path="'/sys/portalsettings/casedeflection'"
                                    class="text-primary hover:text-primary-hover font-normal text-sm mt-2 inline-block hover:underline"
                                    target="_blank">
                                    Show Case Deflection Articles & Wikis
                                </IrisLink>
                            </template>
                        </IrisCheckBox>

                    </div>
                    <!-- Semantic Search Settings -->
                    <div class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm py-4 px-6 mb-6">
                        <h2 class="text-lg font-semibold">Semantic Search Settings</h2>
                        <p class="text-sm text-text-color-400 dark:text-text-color-400-dark mb-6">
                            Select the types of content that AI should use for searching and answering user queries. Articles and wikis designed for case deflection are always available for semantic search.
                        </p>
                        <IrisCheckBox 
                             id="semantic_search_enabled" 
                             :value="settingModel.IndexSearchEnabled" 
                             label="Semantic Search Enabled" 
                             hint="Enable AI-powered search to find relevant answers based on meaning, not just keywords."
                             mode="edit" 
                             output-mode="toggle" 
                             class="mb-4"
                             @onChange="val => settingModel.IndexSearchEnabled = val" 
                        />
                        <div v-if="settingModel.IndexSearchEnabled">
                            <div class="mb-4">
                                <IrisMultiPicklist 
                                id="search_blogs" 
                                :value="settingModel.ArticleTypes" 
                                :items="getItemsWithRemovableFlag(resourceSetting.ArticleTypes, resourceSetting.CaseArticleTypes)" 
                                :appendWhenNotFound="false"
                                label="Articles"
                                @onChange="handleArticleTypesChange" 
                                >
                                    <template #help>
                                        <p>This is the list of published Articles that support semantic article search.</p>
                                        <strong>Case deflection settings</strong> are always included by default. You can add additional Articles if needed.
                                    </template>  
                                </IrisMultiPicklist>
                            </div>
                            <div>
                                <IrisMultiPicklist 
                                id="search_wikis" 
                                :value="settingModel.Wikis" 
                                :items="getItemsWithRemovableFlag(resourceSetting.Wikis, resourceSetting.CaseWikis)" 
                                :appendWhenNotFound="false"
                                label="Wikis"
                                @onChange="handleWikisChange" 
                                >
                                    <template #help>                                    
                                        <p>This is the list of published Wikis that support semantic article search.</p>
                                        <strong>Case deflection settings</strong> are always included by default. You can add additional Wikis if needed.
                                    </template>
                                </IrisMultiPicklist>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </transition>

        <!-- AI Chatbot Section -->
        <transition
            enter-active-class="transition-all duration-200 ease-in-out"
            leave-active-class="transition-all duration-200 ease-in-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-[500px]"
            leave-from-class="opacity-100 max-h-[500px]"
            leave-to-class="opacity-0 max-h-0"
            mode="out-in"
        >
            <div v-if="selectedTab == 'ai-chatbot'">
                <div class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm py-4 px-6 mb-6">
                    <h2 class="text-lg font-semibold mb-6">Wizard Assistant Settings</h2>
                    <IrisCheckBox id="chatbot_enabled" :value="settingModel.ChatbotEnabled" label="Wizard Assistant Enabled" 
                    hint="When enabled, Wizard Assistant will automatically analyze user queries and suggest solutions based on configuration resources." mode="edit" output-mode="toggle" 
                    @onChange="val => settingModel.ChatbotEnabled = val" class="mb-4" />

                    <div v-if="settingModel.ChatbotEnabled">
                        <div class="mb-4">
                            <IrisMultiPicklist 
                              id="chatbot_blogs" 
                              :value="settingModel.ArticleTypes" 
                              :items="getItemsWithRemovableFlag(resourceSetting.ArticleTypes, resourceSetting.CaseArticleTypes)" 
                              :appendWhenNotFound="false"
                              label="Articles"
                              @onChange="handleArticleTypesChange" 
                            >
                                <template #help>                                    
                                    <p>This is the list of published Articles that support Wizard Assistant resources.</p>
                                    <strong>Case deflection settings</strong> are always included by default. You can add additional Articles if needed.
                                </template>
                            </IrisMultiPicklist>
                        </div>
                        <div>
                            <IrisMultiPicklist 
                              id="chatbot_wikis" 
                              :value="settingModel.Wikis" 
                              :items="getItemsWithRemovableFlag(resourceSetting.Wikis, resourceSetting.CaseWikis)" 
                              :appendWhenNotFound="false"
                              label="Wikis"
                              @onChange="handleWikisChange" 
                              >
                                <template #help>                                    
                                    <p>This is the list of published Wikis that support Wizard Assistant resources.</p>
                                    <strong>Case deflection settings</strong> are always included by default. You can add additional Wikis if needed.
                                </template>
                            </IrisMultiPicklist>
                        </div>                    
                    </div>
                </div>
                <div v-if="settingModel.ChatbotEnabled">
                    <div class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm py-4 px-6 mb-6">
                        <h2 class="text-lg font-semibold mb-6">Assign Role</h2>
                        <div class="flex justify-between items-center cursor-pointer" aria-controls="assign-role-drawer" @click="openAssignRoleDrawer">
                            <div>
                                <label class="font-semibold">Assign Wizard Assistant to roles</label>
                                <p class="iris-input-hint">Choose roles to enable Wizard Assistant access</p>
                            </div>
                            <IrisIcon name="chevron-right" class="font-semibold" width="1.5rem" height="1.2rem"></IrisIcon>
                        </div>
                        <!-- drawer component -->
                        <IrisDrawer
                            id="assign-role-drawer"
                            :show="showAssignRoleDrawerState"
                            @onHide="showAssignRoleDrawerState = false"
                            placement="right"
                        >
                            <template #content>
                                <h2 class="text-lg font-semibold p-4">Assign Roles</h2>
                                <h3 class="font-semibold px-4">Choose roles to enable Wizard Assistant access</h3>
                                <div class="flex-1 overflow-y-auto px-4 pb-2">
                                    <div v-for="role in settingRoles" :key="role.Id" class="mt-4">
                                        <IrisCheckBox
                                            :id="`chk${role.Id}`"
                                            :label="role.Name"
                                            :value="role.IsSelected"
                                            @onChange="value => role.IsSelected = value"
                                        >
                                        </IrisCheckBox>
                                    </div>
                                </div>
                            </template>
                            <template #footer>
                                <button class="flex-1 btn-light px-3" @click="cancelRolesSetting">Cancel</button>
                                <button class="flex-1 btn-primary px-3" @click="updateRolesSetting">Save</button>
                            </template>
                        </IrisDrawer>
                    </div>
                    <div class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-color dark:border-border-color-dark rounded shadow-sm py-4 px-6 mb-6">
                        <h2 class="text-lg font-semibold mb-6">Document Library</h2>
                        <div class="flex justify-between items-center" aria-controls="assign-role-drawer">
                            <div>
                                <label class="font-semibold">Choose files Document Library</label>
                                <p class="iris-input-hint">When enabled, AI will suggest files based on file you chose.</p>
                            </div>
                            <div class="flex gap-2">
                                <button class="btn-light !py-2" @click="openDocumentDrawer(true)">
                                    View
                                </button>
                                <button class="btn-light !py-2" @click="openDocumentDrawer(false)">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-bg-color-100 dark:bg-bg-color-100-dark border border-border-muted dark:border-border-muted-dark rounded shadow-sm py-4 px-6 mb-6">
                        <h2 class="text-lg font-semibold mb-6">Wizard Assistant Settings</h2>
                        <div class="grid gap-6 mb-4 md:grid-cols-2">
                            <IrisTextBox
                                id="chatbot-name"
                                type="text"
                                :value="settingModel.ChatbotName"
                                required
                                place-holder="Enter Wizard Assistant Name"
                                :max-length=20
                                aria-label="Wizard Assistant Name"
                                label="Wizard Assistant Name"
                                hint="Give your Wizard Assistant a unique name to personalize user interactions."
                                @onChange="value => settingModel.ChatbotName = value"
                                mode="edit"
                                ></IrisTextBox>
                            <IrisAssetField 
                                id="file_input" 
                                :value="settingModel.ChatbotImage"
                                @onChange="value => settingModel.ChatbotImage = value"
                                label="Wizard Assistant Image" 
                                :allowedExtensions="['.jpg', '.jpeg', '.png']"
                                hint="Select an image for the Wizard Assistant's profile picture. Minimum size: 128 x 128 pixels." 
                            />
                        </div>
                        <div class="mb-4">
                            <div class="flex gap-2 justify-between items-end">
                                <div class="flex-1">
                                    <IrisTextBox
                                        id="predefined-question-input"
                                        type="text"
                                        :value="currentPredefinedQuestion"
                                        place-holder="Write text here..."
                                        mode="edit"
                                        label="Predefined Question"
                                        :max-length=150
                                        @onChange="value => currentPredefinedQuestion = value"
                                        />
                                </div>
                                
                                <button class="btn-light !py-2 disabled:cursor-not-allowed" @click="addQuestion()" :disabled="!currentPredefinedQuestion || predefinedQuestionList.length == 4">Add</button>
                            </div>
                            <div class="iris-input-hint text-text-color-400 dark:text-text-color-400-dark">
                                Add up to 4 predefined questions to help users get started.
                            </div>
                        </div>

                        <div  class="bg-bg-color-200 dark:bg-bg-color-200-dark p-4 rounded">
                            <label class="font-semibold text-text-color dark:text-text-color-dark">Predefined Questions List</label>
                            <iris-sortable-list v-model="predefinedQuestionList" v-if="predefinedQuestionList.length" :item-key="'id'" :key="componentKey" class="question-list" @update:modelValue="handleQuestionListUpdate">
                                <template #default="{ item }">
                                    <div class="flex justify-between py-4 border-b border-border-color dark:border-border-color-dark select-none question-item">
                                    <div class="flex items-center gap-2 min-w-0 flex-1">
                                        <IrisIcon name="bars" class="cursor-move flex-shrink-0"></IrisIcon>
                                        <span class="text-text-color dark:text-text-color-dark truncate">{{ item.text }}</span>
                                    </div>
                                    <div>
                                        <button type="button" @click="selectEditQuestion(item.id)">
                                            <IrisIcon name="pencil" class="me-4 cursor-pointer"></IrisIcon>
                                        </button>
                                        <button type="button" @click="deleteQuestion(item.id)">
                                            <IrisIcon name="xmark" class="cursor-pointer"></IrisIcon>
                                        </button>
                                    </div>
                                    </div>
                                </template>
                            </iris-sortable-list>
                            <div v-else class="pt-2 text-text-color-400 dark:text-text-color-400-dark text-sm">
                                No predefined questions added yet. Add questions above to help users get started.
                            </div>
                            <iris-modal :show="showEdit" size="small" id="edit_question" :closable=true @onHide="cancelEditQuestion" title="Edit Predefined Question">
                                <template #content>
                                    <IrisTextBox
                                        id="defined-question"
                                        type="text"
                                        :value="currentEditQuestion"
                                        required
                                        place-holder="Write text here..."
                                        :max-length=150
                                        aria-label="Chatbot Name"
                                        label="Predefined Question"
                                        @onChange="value => currentEditQuestion = value"
                                        mode="edit"
                                    ></IrisTextBox>
                                </template>
                                <template #footer>
                                    <div class="flex flex-1 justify-between gap-4">
                                        <button class="flex-1 btn-light px-3" type="button"
                                            @click="cancelEditQuestion">Cancel</button>
                                        <button class="flex-1 btn-primary px-3" type="button"
                                            @click="saveQuestion">OK</button>
                                    </div>
                                </template>
                            </iris-modal>
                        </div>
                    </div>
                </div>
            </div>
        </transition>

        <div class="text-right">
            <button type="button" class="btn-primary px-4 py-2 rounded hover:bg-primary-hover dark:hover:bg-primary-hover" @click="saveSetting">Save</button>
        </div>
    </div>
</template>

<style scoped>
.question-list .question-item:last-child {
  border-bottom: none !important;
}
</style>